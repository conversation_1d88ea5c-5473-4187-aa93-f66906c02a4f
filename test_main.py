#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试主程序启动 - 简化版本
"""

import sys
import os
import logging
from datetime import datetime

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# 配置日志
log_file = f"logs/test_main_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
os.makedirs("logs", exist_ok=True)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file, encoding="utf-8"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def test_imports():
    """测试关键模块导入"""
    try:
        logger.info("开始测试模块导入...")
        
        # 测试PyQt5
        from PyQt5.QtWidgets import QApplication, QMainWindow
        from PyQt5.QtCore import Qt
        logger.info("✅ PyQt5导入成功")
        
        # 测试主窗口
        from app.main_window import MainWindow
        logger.info("✅ 主窗口模块导入成功")
        
        # 测试账号标签页
        from app.tabs.account_tab import AccountTab
        logger.info("✅ 账号标签页模块导入成功")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 模块导入失败: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_main_window_creation():
    """测试主窗口创建"""
    try:
        logger.info("开始测试主窗口创建...")
        
        # 设置Qt应用属性
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        # 创建应用
        app = QApplication(sys.argv)
        app.setApplicationName("测试应用")
        
        logger.info("✅ QApplication创建成功")
        
        # 创建主窗口
        from app.main_window import MainWindow
        main_window = MainWindow()
        
        logger.info("✅ 主窗口创建成功")
        
        # 显示窗口
        main_window.show()
        logger.info("✅ 主窗口显示成功")
        
        # 检查加载账号按钮
        if hasattr(main_window, 'account_tab') and hasattr(main_window.account_tab, 'load_account_btn'):
            button = main_window.account_tab.load_account_btn
            logger.info(f"✅ 加载账号按钮存在: enabled={button.isEnabled()}, text='{button.text()}'")
            
            # 测试按钮点击处理方法
            if hasattr(main_window.account_tab, 'on_load_account_clicked'):
                logger.info("✅ 按钮点击处理方法存在")
                
                # 检查状态检查方法
                if hasattr(main_window.account_tab, '_check_loading_state'):
                    state = main_window.account_tab._check_loading_state()
                    logger.info(f"✅ 当前加载状态: {state}")
                
            else:
                logger.warning("⚠️  按钮点击处理方法不存在")
        else:
            logger.warning("⚠️  加载账号按钮不存在")
        
        # 运行事件循环（短时间）
        logger.info("开始运行事件循环，请点击加载账号按钮进行测试...")
        
        # 设置定时器自动退出
        from PyQt5.QtCore import QTimer
        timer = QTimer()
        timer.timeout.connect(app.quit)
        timer.start(30000)  # 30秒后自动退出
        
        # 运行应用
        result = app.exec_()
        logger.info(f"应用退出，返回码: {result}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 主窗口创建失败: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """主函数"""
    logger.info("🧪 开始测试主程序启动")
    logger.info("=" * 50)
    
    # 测试导入
    if not test_imports():
        logger.error("模块导入测试失败，退出")
        return 1
    
    # 测试主窗口创建
    if not test_main_window_creation():
        logger.error("主窗口创建测试失败，退出")
        return 1
    
    logger.info("🎉 所有测试完成")
    return 0

if __name__ == "__main__":
    try:
        sys.exit(main())
    except Exception as e:
        logger.error(f"程序异常退出: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        sys.exit(1)
