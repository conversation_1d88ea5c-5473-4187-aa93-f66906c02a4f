#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
监控装饰器和集成工具 - 用于将监控功能集成到头条存稿工具中
"""

import functools
import time
import logging
import traceback
from datetime import datetime
from .deep_monitor import (
    log_button_click, log_operation_start, log_operation_complete,
    update_state, log_browser_event, get_deep_monitor
)

def monitor_button_click(button_name, window_name=""):
    """按钮点击监控装饰器"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 记录按钮点击
            extra_info = {
                'function': func.__name__,
                'args_count': len(args),
                'kwargs': list(kwargs.keys())
            }
            log_button_click(button_name, window_name, extra_info)
            
            try:
                result = func(*args, **kwargs)
                return result
            except Exception as e:
                logging.error(f"按钮点击处理出错 {button_name}: {str(e)}")
                raise
                
        return wrapper
    return decorator

def monitor_operation(operation_name):
    """操作监控装饰器"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 开始操作监控
            parameters = {
                'function': func.__name__,
                'args_count': len(args),
                'kwargs': kwargs
            }
            operation_id = log_operation_start(operation_name, parameters)
            
            start_time = time.time()
            success = False
            result = ""
            
            try:
                # 执行操作
                return_value = func(*args, **kwargs)
                success = True
                result = "操作成功完成"
                return return_value
                
            except Exception as e:
                success = False
                result = f"操作失败: {str(e)}"
                logging.error(f"操作 {operation_name} 失败: {str(e)}")
                raise
                
            finally:
                # 记录操作完成
                if operation_id:
                    log_operation_complete(operation_id, success, result)
                    
        return wrapper
    return decorator

def monitor_state_change(module_name, state_name):
    """状态变化监控装饰器"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(self, *args, **kwargs):
            # 获取旧状态
            old_value = getattr(self, state_name, None) if hasattr(self, state_name) else None
            
            # 执行函数
            result = func(self, *args, **kwargs)
            
            # 获取新状态
            new_value = getattr(self, state_name, None) if hasattr(self, state_name) else None
            
            # 记录状态变化
            if old_value != new_value:
                update_state(module_name, state_name, new_value)
                
            return result
            
        return wrapper
    return decorator

def monitor_browser_operation(operation_type):
    """浏览器操作监控装饰器"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            
            # 记录浏览器操作开始
            details = {
                'operation': operation_type,
                'function': func.__name__,
                'start_time': datetime.now().isoformat()
            }
            log_browser_event(f"{operation_type}_start", details)
            
            try:
                result = func(*args, **kwargs)
                
                # 记录成功
                end_details = {
                    **details,
                    'success': True,
                    'duration': time.time() - start_time,
                    'end_time': datetime.now().isoformat()
                }
                log_browser_event(f"{operation_type}_success", end_details)
                
                return result
                
            except Exception as e:
                # 记录失败
                error_details = {
                    **details,
                    'success': False,
                    'error': str(e),
                    'duration': time.time() - start_time,
                    'end_time': datetime.now().isoformat()
                }
                log_browser_event(f"{operation_type}_error", error_details)
                raise
                
        return wrapper
    return decorator

def monitor_exception_safe(operation_name="未知操作"):
    """异常安全监控装饰器"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # 记录异常详情
                error_info = {
                    'operation': operation_name,
                    'function': func.__name__,
                    'error_type': type(e).__name__,
                    'error_message': str(e),
                    'traceback': traceback.format_exc(),
                    'timestamp': datetime.now().isoformat()
                }
                
                # 发送到深度监控
                monitor = get_deep_monitor()
                if monitor and monitor.deep_crash_detector:
                    monitor.deep_crash_detector.exception_caught.emit(
                        error_info['error_type'],
                        error_info['error_message'],
                        error_info['traceback']
                    )
                
                logging.error(f"异常安全监控捕获异常 - {operation_name}: {str(e)}")
                logging.debug(f"异常详情: {error_info}")
                
                # 重新抛出异常
                raise
                
        return wrapper
    return decorator

class MonitoredButton:
    """监控按钮包装类"""
    
    def __init__(self, button, button_name, window_name=""):
        self.button = button
        self.button_name = button_name
        self.window_name = window_name
        self.original_click_handler = None
        
        # 包装点击事件
        self._wrap_click_event()
        
    def _wrap_click_event(self):
        """包装按钮点击事件"""
        # 保存原始点击处理器
        if hasattr(self.button, 'clicked'):
            # 连接监控处理器
            self.button.clicked.connect(self._on_button_clicked)
            
    def _on_button_clicked(self):
        """按钮点击处理器"""
        # 记录按钮点击
        extra_info = {
            'button_text': self.button.text() if hasattr(self.button, 'text') else '',
            'button_enabled': self.button.isEnabled() if hasattr(self.button, 'isEnabled') else True,
            'button_visible': self.button.isVisible() if hasattr(self.button, 'isVisible') else True
        }
        log_button_click(self.button_name, self.window_name, extra_info)

class PerformanceMonitor:
    """性能监控工具"""
    
    def __init__(self, operation_name):
        self.operation_name = operation_name
        self.start_time = None
        self.operation_id = None
        
    def __enter__(self):
        """进入上下文管理器"""
        self.start_time = time.time()
        self.operation_id = log_operation_start(self.operation_name)
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        """退出上下文管理器"""
        success = exc_type is None
        duration = time.time() - self.start_time
        
        if success:
            result = f"操作成功，耗时 {duration:.2f} 秒"
        else:
            result = f"操作失败: {str(exc_val)}，耗时 {duration:.2f} 秒"
            
        if self.operation_id:
            log_operation_complete(self.operation_id, success, result)

class StateTracker:
    """状态跟踪器"""
    
    def __init__(self, module_name):
        self.module_name = module_name
        self.tracked_states = {}
        
    def track_state(self, state_name, value):
        """跟踪状态变化"""
        old_value = self.tracked_states.get(state_name)
        if old_value != value:
            self.tracked_states[state_name] = value
            update_state(self.module_name, state_name, value)
            
    def get_state(self, state_name):
        """获取当前状态"""
        return self.tracked_states.get(state_name)
        
    def get_all_states(self):
        """获取所有状态"""
        return self.tracked_states.copy()

# 便捷函数
def create_monitored_button(button, button_name, window_name=""):
    """创建监控按钮"""
    return MonitoredButton(button, button_name, window_name)

def performance_monitor(operation_name):
    """性能监控上下文管理器"""
    return PerformanceMonitor(operation_name)

def create_state_tracker(module_name):
    """创建状态跟踪器"""
    return StateTracker(module_name)

# 批量监控工具
def monitor_all_buttons(window, window_name=""):
    """监控窗口中的所有按钮"""
    from PyQt5.QtWidgets import QPushButton
    
    buttons = window.findChildren(QPushButton)
    monitored_buttons = []
    
    for button in buttons:
        button_name = button.objectName() or button.text() or f"button_{id(button)}"
        monitored_button = create_monitored_button(button, button_name, window_name)
        monitored_buttons.append(monitored_button)
        
    logging.info(f"已监控 {len(monitored_buttons)} 个按钮在窗口 {window_name}")
    return monitored_buttons

def inject_monitoring_to_class(cls, class_name=""):
    """向类注入监控功能"""
    original_init = cls.__init__
    
    @functools.wraps(original_init)
    def monitored_init(self, *args, **kwargs):
        # 调用原始初始化
        original_init(self, *args, **kwargs)
        
        # 添加状态跟踪器
        self._state_tracker = create_state_tracker(class_name or cls.__name__)
        
        # 监控所有按钮（如果是窗口类）
        if hasattr(self, 'findChildren'):
            monitor_all_buttons(self, class_name or cls.__name__)
            
    cls.__init__ = monitored_init
    return cls
