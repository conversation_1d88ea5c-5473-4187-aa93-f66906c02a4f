#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更多网创项目选项卡
提供引流和推广相关的网创项目信息
"""

import os
import json
import webbrowser
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QScrollArea, QFrame, QGridLayout, QTextEdit, QGroupBox,
    QSplitter, QApplication, QComboBox, QMessageBox
)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QPixmap, QIcon, QDesktopServices
from PyQt5.QtCore import QUrl
import logging

logger = logging.getLogger(__name__)

class ProjectCard(QFrame):
    """项目卡片组件"""
    
    clicked = pyqtSignal(str)  # 点击信号，传递项目ID
    
    def __init__(self, project_data):
        super().__init__()
        self.project_data = project_data
        self.setup_ui()
        
    def setup_ui(self):
        """设置UI"""
        self.setFrameStyle(QFrame.Box)
        self.setStyleSheet("""
            QFrame {
                border: 2px solid #e0e0e0;
                border-radius: 12px;
                background-color: #ffffff;
                margin: 8px;
                padding: 5px;
            }
            QFrame:hover {
                border-color: #4CAF50;
                background-color: #f8f9fa;
                transform: translateY(-2px);
            }
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 18, 20, 18)  # 增加内边距
        layout.setSpacing(12)  # 增加间距
        
        # 项目标题
        title_label = QLabel(self.project_data.get('title', '未知项目'))
        title_label.setFont(QFont("Microsoft YaHei", 15, QFont.Bold))  # 增大字体
        title_label.setStyleSheet("""
            color: #2c3e50;
            margin-bottom: 8px;
            padding: 2px 0px;
        """)
        title_label.setWordWrap(True)  # 允许标题换行
        layout.addWidget(title_label)

        # 项目描述
        desc_label = QLabel(self.project_data.get('description', '暂无描述'))
        desc_label.setFont(QFont("Microsoft YaHei", 11))  # 增大字体
        desc_label.setStyleSheet("""
            color: #7f8c8d;
            line-height: 1.5;
            padding: 4px 0px;
        """)
        desc_label.setWordWrap(True)
        desc_label.setMinimumHeight(70)  # 设置最小高度而不是最大高度
        layout.addWidget(desc_label)
        
        # 项目特点和价格
        info_layout = QVBoxLayout()
        info_layout.setSpacing(8)  # 增加间距

        features = self.project_data.get('features', [])
        if features:
            features_text = "✨ " + " | ".join(features[:3])  # 最多显示3个特点
            features_label = QLabel(features_text)
            features_label.setFont(QFont("Microsoft YaHei", 10))  # 增大字体
            features_label.setStyleSheet("""
                color: #27ae60;
                font-weight: bold;
                padding: 4px 0px;
                background-color: #f0f9f0;
                border-radius: 4px;
                padding-left: 8px;
                padding-right: 8px;
            """)
            features_label.setWordWrap(True)
            info_layout.addWidget(features_label)

        # 价格和评分
        price_rating_layout = QHBoxLayout()
        price_rating_layout.setSpacing(15)  # 增加间距

        price = self.project_data.get('price', '价格面议')
        price_label = QLabel(f"💰 {price}")
        price_label.setFont(QFont("Microsoft YaHei", 11, QFont.Bold))  # 增大字体
        price_label.setStyleSheet("""
            color: #e74c3c;
            padding: 2px 6px;
            background-color: #ffeaea;
            border-radius: 4px;
        """)
        price_rating_layout.addWidget(price_label)

        price_rating_layout.addStretch()

        rating = self.project_data.get('rating', 0)
        if rating > 0:
            rating_label = QLabel(f"⭐ {rating}")
            rating_label.setFont(QFont("Microsoft YaHei", 11))  # 增大字体
            rating_label.setStyleSheet("""
                color: #f39c12;
                padding: 2px 6px;
                background-color: #fff8e1;
                border-radius: 4px;
            """)
            price_rating_layout.addWidget(rating_label)

        info_layout.addLayout(price_rating_layout)
        layout.addLayout(info_layout)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)  # 增加按钮间距
        button_layout.setContentsMargins(0, 10, 0, 0)  # 增加顶部边距

        # 了解详情按钮
        detail_btn = QPushButton("📖 了解详情")
        detail_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 16px;
                font-weight: bold;
                font-size: 12px;
                min-height: 35px;
            }
            QPushButton:hover {
                background-color: #2980b9;
                transform: translateY(-1px);
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)
        detail_btn.clicked.connect(lambda: self.open_url(self.project_data.get('detail_url', '')))
        button_layout.addWidget(detail_btn)
        
        # 立即体验按钮
        if self.project_data.get('demo_url'):
            demo_btn = QPushButton("🚀 立即体验")
            demo_btn.setStyleSheet("""
                QPushButton {
                    background-color: #e74c3c;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 10px 16px;
                    font-weight: bold;
                    font-size: 12px;
                    min-height: 35px;
                }
                QPushButton:hover {
                    background-color: #c0392b;
                    transform: translateY(-1px);
                }
                QPushButton:pressed {
                    background-color: #a93226;
                }
            """)
            demo_btn.clicked.connect(lambda: self.open_url(self.project_data.get('demo_url', '')))
            button_layout.addWidget(demo_btn)

        # 联系咨询按钮
        contact_btn = QPushButton("💬 联系咨询")
        contact_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 16px;
                font-weight: bold;
                font-size: 12px;
                min-height: 35px;
            }
            QPushButton:hover {
                background-color: #229954;
                transform: translateY(-1px);
            }
            QPushButton:pressed {
                background-color: #1e8449;
            }
        """)
        contact_btn.clicked.connect(self.show_contact_options)
        button_layout.addWidget(contact_btn)
        
        layout.addLayout(button_layout)
        
        # 设置固定高度 - 进一步增加高度以避免挤压
        self.setFixedHeight(280)  # 增加高度以容纳更多信息和更好的间距
        self.setMinimumWidth(350)  # 设置最小宽度

    def show_contact_options(self):
        """显示联系选项"""
        try:
            contact_qq = self.project_data.get('contact_qq', '')
            contact_wechat = self.project_data.get('contact_wechat', '')

            msg = QMessageBox()
            msg.setWindowTitle("联系方式")
            msg.setIcon(QMessageBox.Information)

            contact_text = f"项目：{self.project_data.get('title', '未知项目')}\n\n"
            if contact_qq:
                contact_text += f"QQ咨询：{contact_qq}\n"
            if contact_wechat:
                contact_text += f"微信咨询：{contact_wechat}\n"
            contact_text += "\n点击确定复制联系方式到剪贴板"

            msg.setText(contact_text)
            msg.setStandardButtons(QMessageBox.Ok | QMessageBox.Cancel)

            if msg.exec_() == QMessageBox.Ok:
                # 复制联系方式到剪贴板
                clipboard_text = f"QQ: {contact_qq}\n微信: {contact_wechat}"
                QApplication.clipboard().setText(clipboard_text)
                logger.info(f"已复制联系方式: {clipboard_text}")

        except Exception as e:
            logger.error(f"显示联系选项失败: {e}")
        
    def open_url(self, url):
        """打开URL"""
        if url:
            try:
                QDesktopServices.openUrl(QUrl(url))
                logger.info(f"打开URL: {url}")
            except Exception as e:
                logger.error(f"打开URL失败: {e}")

class MoreProjectsTab(QWidget):
    """更多网创项目选项卡"""
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.load_projects()
        
    def setup_ui(self):
        """设置UI"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(25, 25, 25, 25)  # 增加边距
        main_layout.setSpacing(20)  # 增加间距
        
        # 标题区域
        title_layout = QHBoxLayout()
        title_layout.setSpacing(20)  # 增加间距

        title_label = QLabel("🌟 更多网创项目")
        title_label.setFont(QFont("Microsoft YaHei", 22, QFont.Bold))  # 增大字体
        title_label.setStyleSheet("""
            color: #2c3e50;
            margin-bottom: 15px;
            padding: 10px 0px;
        """)
        title_layout.addWidget(title_label)

        title_layout.addStretch()

        # 刷新按钮
        refresh_btn = QPushButton("🔄 刷新项目")
        refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 20px;
                font-weight: bold;
                font-size: 13px;
                min-height: 40px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
                transform: translateY(-1px);
            }
            QPushButton:pressed {
                background-color: #6c7b7d;
            }
        """)
        refresh_btn.clicked.connect(self.load_projects)
        title_layout.addWidget(refresh_btn)

        main_layout.addLayout(title_layout)
        
        # 介绍文本
        intro_label = QLabel(
            "💡 发现更多优质网创项目，拓展您的在线业务版图！\n"
            "这里汇集了各种经过验证的网创项目，帮助您实现多元化收入。"
        )
        intro_label.setFont(QFont("Microsoft YaHei", 13))  # 增大字体
        intro_label.setStyleSheet("""
            color: #7f8c8d;
            line-height: 1.6;
            margin-bottom: 20px;
            padding: 15px 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        """)
        intro_label.setWordWrap(True)
        main_layout.addWidget(intro_label)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: transparent;
            }
            QScrollBar:vertical {
                background-color: #f0f0f0;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #c0c0c0;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #a0a0a0;
            }
        """)
        
        # 项目容器
        self.projects_widget = QWidget()
        self.projects_layout = QGridLayout(self.projects_widget)
        self.projects_layout.setSpacing(25)  # 增加卡片间距
        self.projects_layout.setContentsMargins(15, 15, 15, 15)  # 增加边距

        # 设置列的拉伸比例，确保卡片均匀分布
        self.projects_layout.setColumnStretch(0, 1)
        self.projects_layout.setColumnStretch(1, 1)
        
        scroll_area.setWidget(self.projects_widget)
        main_layout.addWidget(scroll_area)
        
        # 底部联系信息
        contact_frame = QFrame()
        contact_frame.setStyleSheet("""
            QFrame {
                background-color: #ecf0f1;
                border-radius: 12px;
                padding: 20px;
                margin-top: 15px;
                border: 2px solid #d5dbdb;
            }
        """)
        contact_layout = QHBoxLayout(contact_frame)
        contact_layout.setSpacing(20)  # 增加间距

        contact_label = QLabel("💬 想要推荐项目或合作？")
        contact_label.setFont(QFont("Microsoft YaHei", 13, QFont.Bold))  # 增大字体
        contact_label.setStyleSheet("""
            color: #2c3e50;
            padding: 5px 0px;
        """)
        contact_layout.addWidget(contact_label)

        contact_layout.addStretch()

        contact_btn = QPushButton("📞 联系我们")
        contact_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 25px;
                font-weight: bold;
                font-size: 13px;
                min-height: 40px;
            }
            QPushButton:hover {
                background-color: #229954;
                transform: translateY(-1px);
            }
            QPushButton:pressed {
                background-color: #1e8449;
            }
        """)
        contact_btn.clicked.connect(self.show_contact_info)
        contact_layout.addWidget(contact_btn)

        main_layout.addWidget(contact_frame)
        
    def load_projects(self):
        """加载项目数据"""
        # 清空现有项目
        for i in reversed(range(self.projects_layout.count())):
            child = self.projects_layout.itemAt(i).widget()
            if child:
                child.setParent(None)

        try:
            # 从配置文件加载项目数据
            config_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'data', 'projects_config.json')

            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                    projects_data = config_data.get('projects', [])
            else:
                logger.warning(f"配置文件不存在: {config_path}")
                projects_data = self.get_default_projects()

        except Exception as e:
            logger.error(f"加载项目配置失败: {e}")
            projects_data = self.get_default_projects()

        # 只显示状态为active的项目
        active_projects = [p for p in projects_data if p.get('status') == 'active']

        # 按热门程度排序，热门项目优先显示
        active_projects.sort(key=lambda x: (x.get('hot', False), x.get('rating', 0)), reverse=True)

        # 创建项目卡片 - 改进布局逻辑
        for i, project_data in enumerate(active_projects):
            card = ProjectCard(project_data)
            row = i // 2  # 每行2个卡片
            col = i % 2
            self.projects_layout.addWidget(card, row, col)

        # 如果项目数量为奇数，在最后一行添加空白占位符以保持布局平衡
        if len(active_projects) % 2 == 1:
            spacer_widget = QWidget()
            spacer_widget.setMinimumWidth(350)
            last_row = (len(active_projects) - 1) // 2
            self.projects_layout.addWidget(spacer_widget, last_row, 1)

        logger.info(f"已加载 {len(active_projects)} 个活跃项目")

    def get_default_projects(self):
        """获取默认项目数据（当配置文件不存在时使用）"""
        return [
            {
                'title': '🎯 精准引流系统',
                'description': '基于AI算法的精准用户引流系统，帮助您快速获取目标客户，提升转化率。',
                'features': ['AI智能筛选', '多平台支持', '高转化率'],
                'price': '免费试用',
                'rating': 4.8,
                'detail_url': 'https://example.com/traffic-system',
                'demo_url': 'https://demo.example.com/traffic',
                'contact_qq': '123456789',
                'contact_wechat': 'traffic_bot',
                'status': 'active',
                'hot': True
            },
            {
                'title': '💰 自动化赚钱机器',
                'description': '24小时自动运行的赚钱系统，无需人工干预，稳定收益。',
                'features': ['24小时运行', '稳定收益', '多种变现'],
                'price': '¥299/月',
                'rating': 4.6,
                'detail_url': 'https://example.com/auto-money',
                'demo_url': None,
                'contact_qq': '987654321',
                'contact_wechat': 'auto_bot',
                'status': 'active',
                'hot': True
            }
        ]
        
    def show_contact_info(self):
        """显示联系信息"""
        try:
            # 这里可以打开联系方式，比如QQ群、微信群等
            contact_url = "https://example.com/contact"  # 替换为实际的联系方式
            QDesktopServices.openUrl(QUrl(contact_url))
            logger.info("打开联系信息页面")
        except Exception as e:
            logger.error(f"打开联系信息失败: {e}")
