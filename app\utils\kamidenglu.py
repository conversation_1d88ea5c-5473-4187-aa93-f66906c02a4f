#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
卡密登录验证模块
实现卡密登录、验证、查询等功能
"""

import os
import sys
import json
import time
import uuid
import hashlib
import requests
import platform
import subprocess
import re
import socket
import base64
import random
import threading
import ctypes
import winreg
import psutil
# WMI模块是可选的，主要功能使用psutil和winreg实现
try:
    import wmi
    WMI_AVAILABLE = True
    print(f"✅ WMI模块加载成功，版本: {wmi.__version__ if hasattr(wmi, '__version__') else '未知'}")
except ImportError as e:
    # WMI模块不可用，但不影响主要功能
    WMI_AVAILABLE = False
    wmi = None
    print(f"⚠️ WMI模块导入失败: {e}")
    print("注意: WMI模块不可用，但不影响防破解功能，所有检测使用psutil和winreg实现")
from datetime import datetime

from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QObject
from PyQt5.QtGui import QIcon, QPixmap, QFont
from PyQt5.QtWidgets import (
    QApplication, QDialog, QLabel, QLineEdit, QPushButton,
    QVBoxLayout, QHBoxLayout, QMessageBox, QProgressBar,
    QGridLayout, QDesktopWidget, QFrame, QSplashScreen, QCheckBox, QProgressDialog,
    QWidget, QRadioButton
)

# 获取当前脚本的目录
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))

# 定义获取可写入的程序目录函数
def get_writable_dir():
    """获取可写入的程序目录"""
    # 首先尝试使用当前工作目录（这通常是主程序目录）
    current_dir = os.getcwd()

    # 检查是否可写
    try:
        test_file = os.path.join(current_dir, "test_write.tmp")
        with open(test_file, 'w') as f:
            f.write("test")
        os.remove(test_file)
        return current_dir
    except Exception:
        # 如果当前目录不可写，尝试使用用户的文档目录
        try:
            from pathlib import Path
            docs_dir = str(Path.home() / "Documents" / "TouTiaoAutoPublisher")
            if not os.path.exists(docs_dir):
                os.makedirs(docs_dir)
            return docs_dir
        except Exception:
            # 最后尝试使用脚本所在目录
            return SCRIPT_DIR

# 常量定义 - 接口1 (主接口)
API1_BASE = "http://api.1wxyun.com"
API1_URL = f"{API1_BASE}/?type=17"  # 卡密登录api
API1_DQ = f"{API1_BASE}/?type=24"  # 到期时间api
API1_VERSION = f"{API1_BASE}/?type=9"  # 版本号api
API1_DOWNLOAD = f"{API1_BASE}/?type=6"  # 下载地址api
API1_UPDATE = f"{API1_BASE}/?type=9"  # 更新检查api
API1_VMP = f"{API1_BASE}/?type=25"  # VMP授权api
API1_ANNOUNCEMENT = f"{API1_BASE}/?type=1"  # 公告获取api
API1_BLACKLIST = f"{API1_BASE}/?type=20"  # 黑名单api

# 常量定义 - 接口2 (备用接口)
API2_BASE = "http://api2.1wxyun.com"
API2_URL = f"{API2_BASE}/?type=17"  # 卡密登录api
API2_DQ = f"{API2_BASE}/?type=24"  # 到期时间api
API2_VERSION = f"{API2_BASE}/?type=9"  # 版本号api
API2_DOWNLOAD = f"{API2_BASE}/?type=6"  # 下载地址api
API2_UPDATE = f"{API2_BASE}/?type=9"  # 更新检查api
API2_VMP = f"{API2_BASE}/?type=25"  # VMP授权api
API2_ANNOUNCEMENT = f"{API2_BASE}/?type=1"  # 公告获取api
API2_BLACKLIST = f"{API2_BASE}/?type=20"  # 黑名单api

# 兼容性保持 - 保留原有常量名称，默认指向接口1
API_URL = API1_URL
API_DQ = API1_DQ
API_VERSION = API1_VERSION
API_DOWNLOAD = API1_DOWNLOAD
API_UPDATE = API1_UPDATE
API_VMP = API1_VMP

# 接口配置
API_INTERFACES = {
    'interface1': {
        'name': '接口1 (主接口)',
        'base_url': API1_BASE,
        'login': API1_URL,
        'expiry': API1_DQ,
        'version': API1_VERSION,
        'download': API1_DOWNLOAD,
        'update': API1_UPDATE,
        'vmp': API1_VMP,
        'announcement': API1_ANNOUNCEMENT,
        'blacklist': API1_BLACKLIST,
        'description': 'api.1wxyun.com - 主要接口服务器'
    },
    'interface2': {
        'name': '接口2 (备用接口)',
        'base_url': API2_BASE,
        'login': API2_URL,
        'expiry': API2_DQ,
        'version': API2_VERSION,
        'download': API2_DOWNLOAD,
        'update': API2_UPDATE,
        'vmp': API2_VMP,
        'announcement': API2_ANNOUNCEMENT,
        'blacklist': API2_BLACKLIST,
        'description': 'api2.1wxyun.com - 备用接口服务器'
    }
}

SOFT_ID = "7449"  # 软件id
SOFT_KEY = "1J6C3Q8E6H0O4K4O"  # 软件标识
VERSION = "1.0"  # 服务器用的版本号标识

# 应用实际版本号（用于显示和更新检测）
APP_VERSION = "6.0.7"#件实际版本号，格式为 X.Y.Z

# 卡密配置文件保存在主程序目录
CONFIG_FILE = os.path.join(get_writable_dir(), "kami_config.dat")

# 加密密钥 - 使用自定义密码和软件标识结合生成
CUSTOM_PASSWORD = "zhengyang\\"  # 自定义密码
ENCRYPTION_KEY = hashlib.md5((CUSTOM_PASSWORD + SOFT_KEY).encode()).hexdigest()

# 黑名单文件
BLACKLIST_FILE = os.path.join(get_writable_dir(), "blacklist.dat")

# 导入防破解配置管理器
try:
    from .anti_crack_config import get_config, is_feature_enabled, get_dangerous_processes, get_dangerous_window_titles
    ANTI_CRACK_CONFIG = get_config().config
except ImportError:
    # 如果导入失败，使用默认配置
    # 检测是否为打包后的程序
    IS_PACKAGED = getattr(sys, 'frozen', False)

    ANTI_CRACK_CONFIG = {
        'check_ce': True,           # 检测CE
        'check_ollydbg': True,      # 检测OllyDbg
        'check_x64dbg': True,       # 检测x64dbg
        'check_ida': True,          # 检测IDA Pro
        'check_windbg': True,       # 检测WinDbg
        'check_process_hacker': False, # 禁用Process Hacker检测（减少误判）
        'check_process_monitor': False, # 禁用Process Monitor检测（减少误判）
        'check_api_monitor': True,  # 检测API Monitor
        'check_wireshark': False,   # 禁用Wireshark检测（减少误判）
        'check_fiddler': False,     # 禁用Fiddler检测（减少误判）
        'check_vm': not IS_PACKAGED,  # 打包后禁用虚拟机检测（避免误判）
        'check_sandbox': True,      # 检测沙箱
        'check_debugger': False,    # 禁用调试器检测（减少误判）
        'check_system_drive': False, # 检测系统盘运行 (已禁用)
        'check_integrity': not IS_PACKAGED,  # 打包后禁用完整性检测（避免误判）
        'auto_blacklist': False,    # 禁用自动加入黑名单（减少误判）
        'exit_on_detect': False,    # 禁用检测到后立即退出（减少误判）
        'sensitivity_level': 'low', # 设置为低敏感度
    }


class APIManager:
    """API接口管理器 - 支持双接口模式"""

    def __init__(self):
        self.current_interface = 'interface1'  # 默认使用接口1
        self.auto_switch = True  # 默认启用自动切换
        self.interface_status = {
            'interface1': {'available': True, 'last_check': 0, 'error_count': 0},
            'interface2': {'available': True, 'last_check': 0, 'error_count': 0}
        }
        self.load_interface_config()

    def load_interface_config(self):
        """加载接口配置"""
        try:
            # 延迟调用load_config，避免函数未定义错误
            config = self._get_config_safe()
            self.current_interface = config.get('api_interface', 'interface1')
            self.auto_switch = config.get('api_auto_switch', True)
            self.interface_status = config.get('interface_status', self.interface_status)
        except Exception as e:
            print(f"加载接口配置失败: {e}")

    def _get_config_safe(self):
        """安全获取配置，避免函数未定义错误"""
        try:
            # 检查load_config函数是否已定义
            if 'load_config' in globals():
                return load_config() or {}
            else:
                # 如果函数未定义，返回空配置
                print("load_config函数未定义，使用默认配置")
                return {}
        except Exception as e:
            print(f"获取配置时出错: {e}")
            return {}

    def save_interface_config(self):
        """保存接口配置"""
        try:
            config = self._get_config_safe()
            config['api_interface'] = self.current_interface
            config['api_auto_switch'] = self.auto_switch
            config['interface_status'] = self.interface_status
            save_config(config)
        except Exception as e:
            print(f"保存接口配置失败: {e}")

    def get_current_apis(self):
        """获取当前接口的API地址"""
        return API_INTERFACES[self.current_interface]

    def get_backup_apis(self):
        """获取备用接口的API地址"""
        backup_interface = 'interface2' if self.current_interface == 'interface1' else 'interface1'
        return API_INTERFACES[backup_interface]

    def switch_interface(self, interface_name=None):
        """切换接口"""
        if interface_name:
            if interface_name in API_INTERFACES:
                self.current_interface = interface_name
                print(f"手动切换到 {API_INTERFACES[interface_name]['name']}")
        else:
            # 自动切换到备用接口
            backup_interface = 'interface2' if self.current_interface == 'interface1' else 'interface1'
            self.current_interface = backup_interface
            print(f"自动切换到 {API_INTERFACES[backup_interface]['name']}")

        self.save_interface_config()

    def mark_interface_error(self, interface_name=None):
        """标记接口错误"""
        if interface_name is None:
            interface_name = self.current_interface

        self.interface_status[interface_name]['error_count'] += 1
        self.interface_status[interface_name]['last_check'] = time.time()

        # 如果错误次数过多，标记为不可用
        if self.interface_status[interface_name]['error_count'] >= 3:
            self.interface_status[interface_name]['available'] = False
            print(f"接口 {API_INTERFACES[interface_name]['name']} 标记为不可用")

    def mark_interface_success(self, interface_name=None):
        """标记接口成功"""
        if interface_name is None:
            interface_name = self.current_interface

        self.interface_status[interface_name]['error_count'] = 0
        self.interface_status[interface_name]['available'] = True
        self.interface_status[interface_name]['last_check'] = time.time()

    def get_available_interfaces(self):
        """获取可用的接口列表"""
        available = []
        for interface_name, status in self.interface_status.items():
            if status['available']:
                available.append(interface_name)
        return available

    def reset_interface_status(self):
        """重置接口状态"""
        for interface_name in self.interface_status:
            self.interface_status[interface_name] = {
                'available': True,
                'last_check': 0,
                'error_count': 0
            }
        self.save_interface_config()


# 全局API管理器实例
api_manager = APIManager()


class SignalHub(QObject):
    """信号中心，用于模块间通信"""
    login_success = pyqtSignal(str, str)  # 卡密, 到期时间
    login_failed = pyqtSignal(str)
    version_check = pyqtSignal(str, str, bool)  # 当前版本, 最新版本, 是否需要更新
    interface_switched = pyqtSignal(str, str)  # 旧接口, 新接口


class AntiCrackDetector:
    """防破解检测器"""

    def __init__(self):
        self.blacklist = self.load_blacklist()
        self.detection_results = []
        self._config = None
        self.vmp_authorization = None  # VMP授权码存储

    @property
    def config(self):
        """获取配置信息"""
        if self._config is None:
            self._config = self.load_config()
        return self._config

    def load_config(self):
        """加载防破解配置"""
        try:
            from app.utils.anti_crack_config import AntiCrackConfig
            config_manager = AntiCrackConfig()
            return config_manager.load_config()
        except Exception as e:
            # 如果无法加载配置，返回默认配置
            return {
                'dangerous_processes': [
                    'ollydbg.exe', 'x64dbg.exe', 'x32dbg.exe', 'windbg.exe',
                    'ida.exe', 'ida64.exe', 'idaq.exe', 'idaq64.exe',
                    'cheatengine.exe', 'cheatengine-x86_64.exe', 'cheatengine-i386.exe',
                    'procmon.exe', 'procexp.exe', 'processhacker.exe',
                    'apimonitor.exe', 'wireshark.exe', 'fiddler.exe'
                ],
                'check_ce': True,
                'check_ollydbg': True,
                'check_vm': True,
                'check_debugger': True
            }

    def get_machine_code(self):
        """获取机器码"""
        return get_machine_code()

    def get_vmp_machine_code(self):
        """获取VMP模块专用机器码

        VMP机器码通常基于硬件特征生成，与普通机器码不同
        这里实现一个基于硬件信息的VMP风格机器码生成
        """
        try:
            import hashlib
            import platform

            # 收集硬件信息用于VMP机器码生成
            hardware_info = []

            # CPU信息 - 优先使用cpuinfo，不可用时使用platform
            try:
                # 尝试导入cpuinfo模块
                import cpuinfo  # type: ignore
                cpu_info = cpuinfo.get_cpu_info()
                hardware_info.append(cpu_info.get('brand_raw', ''))
                hardware_info.append(str(cpu_info.get('hz_actual_friendly', '')))
            except ImportError:
                # cpuinfo模块不可用，使用platform信息
                hardware_info.append(platform.processor())
                # 添加更多platform信息作为补充
                hardware_info.append(platform.machine())
                hardware_info.append(platform.architecture()[0])
            except Exception:
                # 其他异常也使用platform信息
                hardware_info.append(platform.processor())
                hardware_info.append(platform.machine())

            # 系统信息
            hardware_info.append(platform.system())
            hardware_info.append(platform.release())
            hardware_info.append(platform.version())

            # MAC地址（与普通机器码相同的基础）
            import uuid
            mac = uuid.getnode()
            hardware_info.append(str(mac))

            # 磁盘序列号（Windows特有）
            try:
                if os.name == 'nt':
                    import subprocess
                    result = subprocess.run(['wmic', 'diskdrive', 'get', 'serialnumber'],
                                          capture_output=True, text=True, timeout=5)
                    if result.returncode == 0:
                        lines = result.stdout.strip().split('\n')
                        for line in lines[1:]:  # 跳过标题行
                            serial = line.strip()
                            if serial and serial != 'SerialNumber':
                                hardware_info.append(serial)
                                break
            except Exception:
                # 磁盘序列号获取失败，添加其他硬件信息
                try:
                    import socket
                    hardware_info.append(socket.gethostname())
                except Exception:
                    pass

            # 生成VMP风格的机器码
            combined_info = '|'.join(filter(None, hardware_info))
            vmp_hash = hashlib.sha256(combined_info.encode('utf-8')).hexdigest()

            # VMP机器码格式：32位十六进制，大写
            vmp_machine_code = vmp_hash[:32].upper()

            return vmp_machine_code

        except Exception as e:
            # 如果VMP机器码生成失败，使用普通机器码作为备用
            print(f"VMP机器码生成失败，使用普通机器码: {str(e)}")
            return get_machine_code()

    def get_vmp_authorization(self, username, token):
        """获取VMP授权码，支持双接口自动切换

        Args:
            username: 用户名或单码
            token: 登录成功后返回的Token

        Returns:
            tuple: (success: bool, result: str) 成功返回(True, 授权码)，失败返回(False, 错误信息)
        """
        try:
            # 获取VMP机器码
            vmp_mac = self.get_vmp_machine_code()

            # 准备POST数据
            post_data = {
                'Softid': SOFT_KEY,      # 软件标识
                'UserName': username,     # 用户名或单码
                'Token': token,          # 登录Token
                'VmpMac': vmp_mac        # VMP机器码
            }

            # 使用通用API请求函数
            headers = {
                'Content-Type': 'application/x-www-form-urlencoded',
                'User-Agent': 'VMP-Authorization/1.0'
            }

            success, result, interface_used = make_api_request('vmp', post_data, headers=headers, timeout=15)

            if not success:
                error_msg = f"VMP授权请求失败: {result}"
                self.detection_results.append(error_msg)
                return False, error_msg

            # 检查返回结果
            if result and not result.startswith('错误') and not result.startswith('失败') and not result.startswith('-'):
                # 成功获取VMP授权码
                if interface_used:
                    self.detection_results.append(f"VMP授权验证: 成功获取授权码 (使用接口: {API_INTERFACES[interface_used]['name']})")
                else:
                    self.detection_results.append(f"VMP授权验证: 成功获取授权码")
                return True, result
            else:
                # 获取失败
                error_msg = f"VMP授权获取失败: {result}"
                self.detection_results.append(error_msg)
                return False, error_msg

        except Exception as e:
            error_msg = f"VMP授权验证异常: {str(e)}"
            self.detection_results.append(error_msg)
            return False, error_msg

    def load_blacklist(self):
        """加载黑名单"""
        try:
            if os.path.exists(BLACKLIST_FILE):
                with open(BLACKLIST_FILE, 'r', encoding='utf-8') as f:
                    return set(line.strip() for line in f if line.strip())
            return set()
        except Exception:
            return set()

    def save_blacklist(self):
        """保存黑名单"""
        try:
            with open(BLACKLIST_FILE, 'w', encoding='utf-8') as f:
                for item in sorted(self.blacklist):
                    f.write(f"{item}\n")
        except Exception:
            pass

    def add_to_blacklist(self, identifier):
        """添加到黑名单"""
        self.blacklist.add(identifier)
        self.save_blacklist()

    def is_blacklisted(self):
        """检查当前机器是否在黑名单中"""
        machine_code = get_machine_code()
        return machine_code in self.blacklist

    def check_system_drive(self):
        """检测是否在系统盘运行（已禁用，允许任意盘符运行）"""
        try:
            # 取消系统盘限制，允许在任意盘符运行
            current_drive = os.path.splitdrive(os.path.abspath(__file__))[0].upper()
            self.detection_results.append(f"系统盘检测: 当前在{current_drive}盘运行（已允许）")
            return True  # 始终返回True，表示检测通过
        except Exception:
            return True

    def check_virtual_machine(self):
        """检测虚拟机环境"""
        try:
            # 检测虚拟机相关进程
            vm_processes = [
                'vmware.exe', 'vmtoolsd.exe', 'vboxservice.exe', 'vboxtray.exe',
                'qemu-ga.exe', 'xenservice.exe', 'vmsrvc.exe', 'vmusrvc.exe'
            ]

            # 检测虚拟机相关注册表项
            vm_registry_keys = [
                r'SYSTEM\CurrentControlSet\Services\VBoxGuest',
                r'SYSTEM\CurrentControlSet\Services\VBoxMouse',
                r'SYSTEM\CurrentControlSet\Services\VBoxSF',
                r'SYSTEM\CurrentControlSet\Services\vmci',
                r'SYSTEM\CurrentControlSet\Services\vmhgfs'
            ]

            # 检测虚拟机相关文件
            vm_files = [
                r'C:\windows\system32\drivers\vboxguest.sys',
                r'C:\windows\system32\drivers\vmhgfs.sys',
                r'C:\windows\system32\drivers\vmmouse.sys'
            ]

            # 检测进程
            for proc in psutil.process_iter(['name']):
                try:
                    proc_info = proc.info  # type: ignore
                    if proc_info['name'].lower() in [p.lower() for p in vm_processes]:
                        self.detection_results.append(f"虚拟机检测: 发现虚拟机进程 {proc_info['name']}")
                        return True
                except Exception:
                    continue

            # 检测注册表
            for key_path in vm_registry_keys:
                try:
                    winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, key_path)
                    self.detection_results.append(f"虚拟机检测: 发现虚拟机注册表项 {key_path}")
                    return True
                except Exception:
                    continue

            # 检测文件
            for file_path in vm_files:
                if os.path.exists(file_path):
                    self.detection_results.append(f"虚拟机检测: 发现虚拟机文件 {file_path}")
                    return True

            return False
        except Exception:
            return False

    def _is_system_path(self, path):
        """检查路径是否为系统路径"""
        if not path:
            return False

        path_lower = path.lower()
        system_paths = [
            'c:\\windows\\system32',
            'c:\\windows\\syswow64',
            'c:\\windows\\',
            'c:\\program files\\windows',
            'c:\\program files (x86)\\windows'
        ]

        return any(sys_path in path_lower for sys_path in system_paths)

    def check_debugging_tools(self):
        """检测调试工具 - 基于行为的主动检测"""
        try:
            detected_threats = []

            # 1. 检测主动调试会话
            active_debugging = self._detect_active_debugging_sessions()
            if active_debugging:
                detected_threats.extend(active_debugging)

            # 2. 检测新启动的调试工具
            new_debug_tools = self._detect_newly_launched_debug_tools()
            if new_debug_tools:
                detected_threats.extend(new_debug_tools)

            # 3. 检测活跃的调试工具窗口
            active_debug_windows = self._detect_active_debug_windows()
            if active_debug_windows:
                detected_threats.extend(active_debug_windows)

            # 4. 检测调试行为特征
            debug_behaviors = self._detect_debugging_behaviors()
            if debug_behaviors:
                detected_threats.extend(debug_behaviors)

            return len(detected_threats) > 0
        except Exception as e:
            # 静默处理异常，避免影响正常程序运行
            return False

    def _detect_active_debugging_sessions(self):
        """检测主动调试会话"""
        threats = []
        try:
            # 检测是否有进程被调试器附加
            for proc in psutil.process_iter(['pid', 'name']):
                try:
                    proc_info = proc.info  # type: ignore
                    pid = proc_info.get('pid')
                    name = proc_info.get('name', '')

                    # 跳过系统进程和自身进程
                    if self._is_system_process(name) or pid == os.getpid():
                        continue

                    # 检测进程是否被调试
                    if self._is_process_being_debugged(pid):
                        threats.append(f"主动调试会话: 进程 {name} (PID: {pid}) 正在被调试")
                        self.detection_results.append(f"主动调试会话: 进程 {name} (PID: {pid}) 正在被调试")

                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

        except Exception:
            pass
        return threats

    def _detect_newly_launched_debug_tools(self):
        """检测新启动的调试工具 - 降低敏感度版本"""
        threats = []
        try:
            # 检查敏感度设置
            sensitivity = ANTI_CRACK_CONFIG.get('sensitivity_level', 'medium')

            if sensitivity == 'low':
                # 低敏感度：只检测核心恶意调试工具
                debug_tools = [
                    'cheatengine.exe', 'cheatengine-x86_64.exe', 'cheatengine-i386.exe',
                    'ollydbg.exe'  # 只保留最核心的恶意工具
                ]
                time_window = 120  # 缩短到2分钟
            else:
                # 中高敏感度：检测更多工具
                debug_tools = [
                    'ollydbg.exe', 'x64dbg.exe', 'x32dbg.exe', 'windbg.exe',
                    'cheatengine.exe', 'cheatengine-x86_64.exe', 'cheatengine-i386.exe',
                    'ida.exe', 'ida64.exe', 'idaq.exe', 'idaq64.exe'
                ]
                time_window = 300  # 5分钟

            for proc in psutil.process_iter(['name', 'exe', 'create_time']):
                try:
                    proc_info = proc.info  # type: ignore
                    proc_name = proc_info.get('name', '').lower()
                    proc_exe = proc_info.get('exe', '') or ''
                    create_time = proc_info.get('create_time', 0)

                    # 检测最近启动的进程
                    import time
                    if time.time() - create_time > time_window:
                        continue

                    # 检查是否为调试工具
                    if proc_name in debug_tools:
                        # 确保不是系统目录中的进程
                        if not self._is_system_path(proc_exe):
                            # 低敏感度下，只有明确的恶意工具才报告
                            if sensitivity == 'low' and proc_name not in ['cheatengine.exe', 'cheatengine-x86_64.exe', 'cheatengine-i386.exe']:
                                continue
                            threats.append(f"新启动调试工具: {proc_name}")
                            self.detection_results.append(f"新启动调试工具: {proc_name}")

                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

        except Exception:
            pass
        return threats

    def _detect_active_debug_windows(self):
        """检测活跃的调试工具窗口"""
        threats = []
        try:
            import win32gui
            import win32con

            # 调试工具窗口标题关键词
            debug_window_keywords = [
                'cheat engine', 'ollydbg', 'x64dbg', 'x32dbg', 'windbg',
                'ida pro', 'ida disassembler'
            ]

            def enum_windows_callback(hwnd, results):
                try:
                    if win32gui.IsWindowVisible(hwnd) and win32gui.IsWindow(hwnd):
                        window_title = win32gui.GetWindowText(hwnd).lower()
                        if window_title:
                            for keyword in debug_window_keywords:
                                if keyword in window_title:
                                    # 检查窗口是否处于活跃状态
                                    if self._is_window_active(hwnd):
                                        threats.append(f"活跃调试窗口: {window_title}")
                                        self.detection_results.append(f"活跃调试窗口: {window_title}")
                                        break
                except Exception:
                    pass

            win32gui.EnumWindows(enum_windows_callback, [])

        except ImportError:
            pass
        except Exception:
            pass
        return threats

    def _detect_debugging_behaviors(self):
        """检测调试行为特征"""
        threats = []
        try:
            # 检测调试器API调用
            if self._detect_debugger_api_usage():
                threats.append("调试行为: 检测到调试器API调用")
                self.detection_results.append("调试行为: 检测到调试器API调用")

            # 检测内存扫描行为
            if self._detect_memory_scanning():
                threats.append("调试行为: 检测到内存扫描活动")
                self.detection_results.append("调试行为: 检测到内存扫描活动")

        except Exception:
            pass
        return threats

    def _is_system_process(self, process_name):
        """判断是否为系统进程"""
        system_processes = [
            'services.exe', 'svchost.exe', 'lsass.exe', 'winlogon.exe',
            'csrss.exe', 'wininit.exe', 'explorer.exe', 'dwm.exe',
            'conhost.exe', 'dllhost.exe', 'taskhostw.exe', 'sihost.exe',
            'ctfmon.exe', 'runtimebroker.exe', 'searchindexer.exe',
            'spoolsv.exe', 'audiodg.exe', 'fontdrvhost.exe',
            'emdriverassist.exe', 'wechatocr.exe', 'officeclicktorun.exe',
            'lzservice.exe'
        ]
        return process_name.lower() in system_processes

    def _is_process_being_debugged(self, pid):
        """检测进程是否被调试"""
        try:
            import ctypes
            from ctypes import wintypes

            # 打开进程
            PROCESS_QUERY_INFORMATION = 0x0400
            handle = ctypes.windll.kernel32.OpenProcess(PROCESS_QUERY_INFORMATION, False, pid)
            if not handle:
                return False

            try:
                # 检查调试端口
                debug_port = ctypes.c_ulong()
                status = ctypes.windll.ntdll.NtQueryInformationProcess(
                    handle, 7, ctypes.byref(debug_port), ctypes.sizeof(debug_port), None
                )
                return status == 0 and debug_port.value != 0
            finally:
                ctypes.windll.kernel32.CloseHandle(handle)

        except Exception:
            return False

    def _is_window_active(self, hwnd):
        """检查窗口是否处于活跃状态"""
        try:
            import win32gui
            # 检查窗口是否可见且不是最小化状态
            return (win32gui.IsWindowVisible(hwnd) and
                   not win32gui.IsIconic(hwnd))
        except Exception:
            return False

    def _detect_debugger_api_usage(self):
        """检测调试器API使用"""
        try:
            import ctypes
            # 检测常见的调试器检测API
            kernel32 = ctypes.windll.kernel32

            # 检查是否有调试器附加到当前进程
            if kernel32.IsDebuggerPresent():
                return True

            # 检查远程调试器
            debug_present = ctypes.c_bool()
            if kernel32.CheckRemoteDebuggerPresent(kernel32.GetCurrentProcess(), ctypes.byref(debug_present)):
                if debug_present.value:
                    return True

        except Exception:
            pass
        return False

    def _detect_memory_scanning(self):
        """检测内存扫描行为"""
        try:
            # 检测异常的内存访问模式
            # 这里可以添加更复杂的内存扫描检测逻辑
            # 目前简化为检测高频的内存访问
            pass
        except Exception:
            pass
        return False

    def _analyze_suspicious_behavior(self):
        """分析可疑行为 - 降低敏感度版本"""
        suspicious_count = 0

        try:
            # 检查敏感度设置
            sensitivity = ANTI_CRACK_CONFIG.get('sensitivity_level', 'medium')

            if sensitivity == 'low':
                # 低敏感度：只检测极端异常情况

                # 1. 检测极端异常的进程数量
                process_count = len(list(psutil.process_iter()))
                if process_count > 800:  # 提高阈值到800个进程
                    suspicious_count += 1
                    self.detection_results.append("行为分析: 检测到极端异常的进程数量")

                # 2. 检测明显的恶意进程（只检测明确的恶意关键词）
                for proc in psutil.process_iter(['name', 'cpu_percent']):
                    try:
                        proc_info = proc.info  # type: ignore
                        if proc_info.get('cpu_percent', 0) > 95:  # 提高CPU阈值到95%
                            proc_name = proc_info.get('name', '').lower()
                            # 只检测明确的恶意关键词
                            if any(keyword in proc_name for keyword in ['hack', 'crack', 'keygen', 'patch']):
                                suspicious_count += 1
                                self.detection_results.append(f"行为分析: 检测到高CPU使用率的恶意进程 {proc_name}")
                    except Exception:
                        continue

                # 3. 禁用内存使用检测（容易误判）
                # memory = psutil.virtual_memory()
                # if memory.percent > 95:  # 提高内存阈值到95%
                #     suspicious_count += 1
                #     self.detection_results.append("行为分析: 检测到极端高的内存使用率")

                # 4. 只检测明确的恶意端口
                try:
                    connections = psutil.net_connections()
                    suspicious_ports = [1337, 31337, 4444, 5555]  # 只检测明确的恶意端口
                    for conn in connections:
                        if conn.laddr and conn.laddr.port in suspicious_ports:
                            suspicious_count += 1
                            self.detection_results.append(f"行为分析: 检测到恶意端口连接 {conn.laddr.port}")
                            break
                except Exception:
                    pass

                # 5. 禁用文件系统监控检测（容易误判）
                # 文件系统监控检测在低敏感度下禁用

            elif sensitivity == 'medium':
                # 中等敏感度：原有逻辑但提高阈值
                process_count = len(list(psutil.process_iter()))
                if process_count > 600:  # 提高到600个进程
                    suspicious_count += 1
                    self.detection_results.append("行为分析: 检测到异常多的进程数量")

                for proc in psutil.process_iter(['name', 'cpu_percent']):
                    try:
                        proc_info = proc.info  # type: ignore
                        if proc_info.get('cpu_percent', 0) > 90:  # 提高CPU阈值到90%
                            proc_name = proc_info.get('name', '').lower()
                            if any(keyword in proc_name for keyword in ['debug', 'monitor', 'hack', 'crack']):
                                suspicious_count += 1
                                self.detection_results.append(f"行为分析: 检测到高CPU使用率的可疑进程 {proc_name}")
                    except Exception:
                        continue

                memory = psutil.virtual_memory()
                if memory.percent > 95:  # 提高内存阈值到95%
                    suspicious_count += 1
                    self.detection_results.append("行为分析: 检测到异常高的内存使用率")

            # 高敏感度保持原有逻辑（不推荐）

        except Exception:
            pass

        return suspicious_count

    def check_debugger_present(self):
        """检测调试器附加"""
        try:
            # 使用Windows API检测调试器
            kernel32 = ctypes.windll.kernel32

            # IsDebuggerPresent
            if kernel32.IsDebuggerPresent():
                self.detection_results.append("调试器检测: IsDebuggerPresent返回True")
                return True

            # CheckRemoteDebuggerPresent
            debug_flag = ctypes.c_bool()
            if kernel32.CheckRemoteDebuggerPresent(kernel32.GetCurrentProcess(), ctypes.byref(debug_flag)):
                if debug_flag.value:
                    self.detection_results.append("调试器检测: CheckRemoteDebuggerPresent检测到远程调试器")
                    return True

            # NtQueryInformationProcess
            try:
                ntdll = ctypes.windll.ntdll
                process_debug_port = 7
                debug_port = ctypes.c_ulong()

                status = ntdll.NtQueryInformationProcess(
                    kernel32.GetCurrentProcess(),
                    process_debug_port,
                    ctypes.byref(debug_port),
                    ctypes.sizeof(debug_port),
                    None
                )

                if status == 0 and debug_port.value != 0:
                    self.detection_results.append("调试器检测: NtQueryInformationProcess检测到调试端口")
                    return True
            except Exception:
                pass

            return False
        except Exception:
            return False

    def check_file_integrity(self):
        """检测文件完整性"""
        try:
            # 检测当前执行文件的完整性
            current_file = sys.executable if getattr(sys, 'frozen', False) else __file__

            # 如果是打包后的exe文件，跳过完整性检测
            if getattr(sys, 'frozen', False):
                self.detection_results.append("文件完整性检测: 打包程序跳过检测")
                return True

            # 计算文件哈希（仅在开发环境中）
            with open(current_file, 'rb') as f:
                file_hash = hashlib.md5(f.read()).hexdigest()

            # 这里可以添加预期的哈希值检查
            # 如果文件被修改，哈希值会不匹配

            # 检测是否有调试符号或补丁痕迹（仅在开发环境中）
            with open(current_file, 'rb') as f:
                content = f.read()

                # 检测常见的补丁特征
                patch_signatures = [
                    b'\x90\x90\x90\x90',  # NOP指令
                    b'\xEB\xFE',          # 无限循环
                    b'\xCC\xCC\xCC\xCC',  # 断点指令
                ]

                for signature in patch_signatures:
                    if signature in content:
                        self.detection_results.append(f"文件完整性检测: 发现可疑补丁特征")
                        return False

            self.detection_results.append("文件完整性检测: 通过")
            return True
        except Exception as e:
            # 检测异常时默认通过，避免误判
            self.detection_results.append(f"文件完整性检测: 异常跳过 ({str(e)})")
            return True

    def perform_full_check(self):
        """执行完整的防破解检测"""
        self.detection_results.clear()

        # 检查黑名单
        if self.is_blacklisted():
            self.detection_results.append("黑名单检测: 当前机器在黑名单中")
            return False, self.detection_results

        detection_failed = False

        # 系统盘检测
        if ANTI_CRACK_CONFIG['check_system_drive']:
            if not self.check_system_drive():
                detection_failed = True

        # 虚拟机检测
        if ANTI_CRACK_CONFIG['check_vm']:
            if self.check_virtual_machine():
                detection_failed = True

        # 调试工具检测
        if (ANTI_CRACK_CONFIG['check_ce'] or ANTI_CRACK_CONFIG['check_ollydbg'] or
            ANTI_CRACK_CONFIG['check_x64dbg']):
            if self.check_debugging_tools():
                detection_failed = True

        # 调试器检测
        if ANTI_CRACK_CONFIG['check_debugger']:
            if self.check_debugger_present():
                detection_failed = True

        # 文件完整性检测
        if ANTI_CRACK_CONFIG['check_integrity']:
            if not self.check_file_integrity():
                detection_failed = True

        # VMP授权验证（如果已获取授权码）
        if hasattr(self, 'vmp_authorization') and self.vmp_authorization:
            self.detection_results.append(f"VMP授权状态: 已验证 (授权码: {self.vmp_authorization[:16]}...)")
        else:
            # VMP授权未获取不算检测失败，只是记录状态
            self.detection_results.append("VMP授权状态: 未获取或验证失败")

        # 如果检测失败且启用自动黑名单
        if detection_failed and ANTI_CRACK_CONFIG['auto_blacklist']:
            machine_code = get_machine_code()
            self.add_to_blacklist(machine_code)
            self.detection_results.append(f"自动黑名单: 机器码 {machine_code} 已加入黑名单")

            # 调用API将机器码加入服务器黑名单
            self.add_to_server_blacklist(machine_code, "检测到调试工具")

        return not detection_failed, self.detection_results

    def add_to_server_blacklist(self, machine_code, reason="违规使用"):
        """将机器码添加到服务器黑名单，支持双接口自动切换"""
        try:
            # 准备POST数据
            post_data = {
                'Softid': SOFT_KEY,  # 软件标识
                'Mac': machine_code,  # 机器码
                'Reason': reason  # 加黑原因
            }

            # 使用通用API请求函数
            headers = {
                'Content-Type': 'application/x-www-form-urlencoded',
                'User-Agent': 'AntiCrack-Protection/1.0'
            }

            success, result, interface_used = make_api_request('blacklist', post_data, headers=headers, timeout=10)

            if success and result and not result.startswith('错误') and not result.startswith('-'):
                if interface_used:
                    self.detection_results.append(f"服务器黑名单: 机器码 {machine_code} 已成功加入服务器黑名单 (使用接口: {API_INTERFACES[interface_used]['name']})")
                else:
                    self.detection_results.append(f"服务器黑名单: 机器码 {machine_code} 已成功加入服务器黑名单")
                print(f"✅ 机器码 {machine_code} 已加入服务器黑名单，原因: {reason}")
                return True, result
            else:
                self.detection_results.append(f"服务器黑名单: 加入失败 - {result}")
                print(f"❌ 加入服务器黑名单失败: {result}")
                return False, result

        except Exception as e:
            error_msg = f"未知错误: {str(e)}"
            self.detection_results.append(f"服务器黑名单: {error_msg}")
            print(f"❌ 服务器黑名单API调用异常: {error_msg}")
            return False, error_msg

    def check_server_blacklist(self, machine_code=None):
        """检查机器码是否在服务器黑名单中"""
        try:
            if machine_code is None:
                machine_code = get_machine_code()

            # 这里可以添加检查服务器黑名单的API调用
            # 目前API文档中没有提供查询黑名单的接口，所以暂时返回False
            # 如果后续有查询接口，可以在这里实现

            return False, "暂无查询接口"

        except Exception as e:
            error_msg = f"检查服务器黑名单异常: {str(e)}"
            print(f"⚠️ {error_msg}")
            return False, error_msg


def get_machine_code():
    """获取机器码（确保不超过32位且仅包含字母和数字）"""
    mac = uuid.getnode()
    machine_code = "%012X" % mac
    if len(machine_code) > 32:
        machine_code = machine_code[:32]
    return machine_code


def simple_encrypt(data_str, key):
    """简单的XOR加密算法"""
    # 将数据和密钥转换为字节序列
    data_bytes = data_str.encode('utf-8')
    key_bytes = key.encode('utf-8')

    # 加密结果
    result = bytearray()

    # 对每个字节进行XOR操作
    for i in range(len(data_bytes)):
        key_char = key_bytes[i % len(key_bytes)]
        encrypted_char = data_bytes[i] ^ key_char
        result.append(encrypted_char)

    # Base64编码以便存储
    return base64.b64encode(result).decode('utf-8')


def simple_decrypt(encrypted_str, key):
    """简单的XOR解密算法"""
    try:
        # Base64解码
        encrypted_bytes = base64.b64decode(encrypted_str)
        key_bytes = key.encode('utf-8')

        # 解密结果
        result = bytearray()

        # 对每个字节进行XOR操作
        for i in range(len(encrypted_bytes)):
            key_char = key_bytes[i % len(key_bytes)]
            decrypted_char = encrypted_bytes[i] ^ key_char
            result.append(decrypted_char)

        # 返回解密后的字符串
        return result.decode('utf-8')
    except Exception as e:
        print(f"解密数据失败: {str(e)}")
        return None


def load_config():
    """从文件加载配置"""
    try:
        print(f"尝试从 {CONFIG_FILE} 加载卡密配置")
        if os.path.exists(CONFIG_FILE):
            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                encrypted_data = f.read()
                if not encrypted_data.strip():
                    print(f"配置文件为空")
                    return {}

                json_str = simple_decrypt(encrypted_data, ENCRYPTION_KEY)
                if json_str:
                    try:
                        config = json.loads(json_str)
                        print(f"卡密配置加载成功: {config}")

                        # 确保配置中有remember_login字段，默认为True
                        if 'remember_login' not in config:
                            config['remember_login'] = True
                            print(f"添加默认remember_login=True")
                            # 立即保存更新后的配置
                            save_config(config)

                        return config
                    except json.JSONDecodeError:
                        print(f"JSON解析失败，配置文件可能损坏")
                        return {}
                else:
                    print(f"卡密配置解密失败")
        else:
            print(f"卡密配置文件不存在: {CONFIG_FILE}")
        return {}
    except Exception as e:
        print(f"加载配置失败: {str(e)}")
        import traceback
        print(traceback.format_exc())
        return {}


def save_config(data):
    """保存配置到文件"""
    try:
        # 输出配置文件保存路径
        print(f"正在保存卡密配置到: {CONFIG_FILE}")
        print(f"保存的配置内容: {data}")

        # 确保目录存在
        config_dir = os.path.dirname(CONFIG_FILE)
        if not os.path.exists(config_dir) and config_dir:
            try:
                os.makedirs(config_dir)
                print(f"创建配置目录成功: {config_dir}")
            except Exception as e:
                print(f"创建配置目录失败: {str(e)}")
                return False

        # 确保remember_login字段存在
        if 'remember_login' not in data:
            data['remember_login'] = True

        # 加密数据
        json_str = json.dumps(data, ensure_ascii=False)
        encrypted_data = simple_encrypt(json_str, ENCRYPTION_KEY)

        # 添加错误处理和验证
        if not encrypted_data:
            print("加密失败，无法保存配置")
            return False

        try:
            with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
                f.write(encrypted_data)

            # 验证写入是否成功
            if os.path.exists(CONFIG_FILE):
                with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if content == encrypted_data:
                        print(f"卡密配置保存成功，验证通过")
                        return True
                    else:
                        print(f"卡密配置保存后验证失败，文件内容不匹配")
                        return False
            else:
                print(f"卡密配置文件保存后不存在")
                return False
        except Exception as e:
            print(f"写入配置文件时出错: {str(e)}")
            import traceback
            print(traceback.format_exc())
            return False
    except Exception as e:
        print(f"保存配置失败: {str(e)}")
        import traceback
        print(traceback.format_exc())
        return False


def make_api_request(api_type, post_data, headers=None, timeout=15, max_retries=1):
    """通用API请求函数，支持双接口自动切换

    Args:
        api_type: API类型 ('login', 'expiry', 'version', 'download', 'vmp', 'announcement', 'blacklist')
        post_data: POST数据
        headers: 请求头
        timeout: 超时时间
        max_retries: 最大重试次数

    Returns:
        tuple: (success: bool, result: str, interface_used: str)
    """
    if headers is None:
        headers = {"Content-Type": "application/x-www-form-urlencoded"}

    # 获取当前接口的API地址
    current_apis = api_manager.get_current_apis()
    backup_apis = api_manager.get_backup_apis()

    # API类型映射
    api_mapping = {
        'login': 'login',
        'expiry': 'expiry',
        'version': 'version',
        'download': 'download',
        'update': 'update',
        'vmp': 'vmp',
        'announcement': 'announcement',
        'blacklist': 'blacklist'
    }

    if api_type not in api_mapping:
        return False, f"不支持的API类型: {api_type}", None

    api_key = api_mapping[api_type]

    # 尝试接口列表
    interfaces_to_try = [
        (api_manager.current_interface, current_apis[api_key]),
    ]

    # 如果启用自动切换，添加备用接口
    if api_manager.auto_switch:
        backup_interface = 'interface2' if api_manager.current_interface == 'interface1' else 'interface1'
        interfaces_to_try.append((backup_interface, backup_apis[api_key]))

    last_error = None

    for interface_name, api_url in interfaces_to_try:
        print(f"尝试使用 {API_INTERFACES[interface_name]['name']} - {api_url}")

        for retry in range(max_retries):
            try:
                response = requests.post(api_url, data=post_data, headers=headers, timeout=timeout)
                response.raise_for_status()

                # 标记接口成功
                api_manager.mark_interface_success(interface_name)

                # 如果使用的不是当前接口，切换到成功的接口
                if interface_name != api_manager.current_interface:
                    print(f"接口切换: {API_INTERFACES[api_manager.current_interface]['name']} -> {API_INTERFACES[interface_name]['name']}")
                    api_manager.switch_interface(interface_name)

                return True, response.text.strip(), interface_name

            except requests.RequestException as e:
                last_error = str(e)
                print(f"接口 {API_INTERFACES[interface_name]['name']} 请求失败: {last_error}")

                if retry < max_retries - 1:
                    print(f"正在重试 ({retry+1}/{max_retries})...")
                    time.sleep(1)
                    continue
                else:
                    # 标记接口错误
                    api_manager.mark_interface_error(interface_name)
                    break

    return False, last_error or "所有接口都无法访问", None


def login(card, machine_code, max_retries=1):
    """登录验证并获取Token，支持双接口自动切换

    Args:
        card: 卡密
        machine_code: 机器码
        max_retries: 最大重试次数，默认为1（不重试）

    Returns:
        str or None: 成功返回Token，失败返回None
    """
    post_data = {
        "Softid": SOFT_KEY,
        "Card": card,
        "Version": VERSION,
        "Mac": machine_code
    }

    success, result, interface_used = make_api_request('login', post_data, max_retries=max_retries)

    if success and re.match("^[A-Za-z0-9]+$", result):
        if interface_used:
            print(f"登录成功，使用接口: {API_INTERFACES[interface_used]['name']}")
        return result  # 返回Token
    else:
        print(f"登录失败: {result}")
        return None


def login_with_vmp_authorization(card, machine_code, max_retries=1):
    """登录验证并获取VMP授权的完整流程

    Args:
        card: 卡密
        machine_code: 机器码
        max_retries: 最大重试次数，默认为1（不重试）

    Returns:
        dict: 包含登录和VMP授权信息的字典
        {
            'success': bool,           # 整体是否成功
            'token': str,             # 登录Token
            'vmp_auth': str,          # VMP授权码
            'error': str,             # 错误信息（如果失败）
            'username': str           # 用户名（使用卡密作为用户名）
        }
    """
    result = {
        'success': False,
        'token': None,
        'vmp_auth': None,
        'error': None,
        'username': card  # 使用卡密作为用户名
    }

    try:
        # 第一步：普通登录获取Token
        print("🔑 正在进行卡密登录验证...")
        token = login(card, machine_code, max_retries)

        if not token:
            result['error'] = "卡密登录失败，无法获取Token"
            return result

        result['token'] = token
        print(f"✅ 卡密登录成功，获取Token: {token[:8]}...")

        # 第二步：获取VMP授权
        print("🛡️ 正在获取VMP授权...")
        detector = AntiCrackDetector()
        vmp_success, vmp_result = detector.get_vmp_authorization(card, token)

        if vmp_success:
            result['vmp_auth'] = vmp_result
            result['success'] = True
            print(f"✅ VMP授权获取成功: {vmp_result[:16]}...")
        else:
            result['error'] = f"VMP授权获取失败: {vmp_result}"
            print(f"❌ VMP授权获取失败: {vmp_result}")
            # 注意：VMP授权失败不一定要阻止程序运行，取决于配置

        return result

    except Exception as e:
        result['error'] = f"登录流程异常: {str(e)}"
        print(f"❌ 登录流程异常: {str(e)}")
        return result


def check_card_expiry(card, max_retries=1):
    """检查卡密到期时间，支持双接口自动切换

    Args:
        card: 卡密
        max_retries: 最大重试次数，默认为1（不重试）

    Returns:
        tuple: (是否成功, 到期时间或错误信息)
    """
    post_data = {
        "Softid": SOFT_KEY,
        "UserName": card
    }

    success, result, interface_used = make_api_request('expiry', post_data, max_retries=max_retries)

    if not success:
        return False, f"无法验证卡密: {result}"

    expiry_status = result

    if expiry_status == "-83006":
        return False, "卡密已过期"
    elif expiry_status.startswith("-"):
        return False, f"检查卡密失败: {expiry_status}"
    else:
        try:
            expiry_time = datetime.strptime(expiry_status, "%Y-%m-%d %H:%M:%S")
            if interface_used:
                print(f"到期时间查询成功，使用接口: {API_INTERFACES[interface_used]['name']}")
            return True, expiry_time.strftime('%Y-%m-%d %H:%M:%S')
        except ValueError as e:
            return False, f"解析到期时间失败: {str(e)}"


def check_version():
    """获取最新版本号"""
    try:
        # 获取登录token
        config = load_config()
        if not config.get("card") or not config.get("machine_code"):
            return False, "未找到有效的卡密信息"

        token = login(config["card"], config["machine_code"])
        if not token:
            return False, "获取token失败"

        post_data = {
            "Softid": SOFT_KEY,  # 软件标识
            "UserName": config.get("card", ""),  # 使用卡密作为用户名
            "Token": token,  # 使用登录返回的token
            "Version": VERSION  # 当前版本号
        }
        headers = {"Content-Type": "application/x-www-form-urlencoded"}

        # 打印请求参数
        print(f"版本检查参数: {post_data}")

        response = requests.post(API_VERSION, data=post_data, headers=headers, timeout=10)
        response.raise_for_status()

        # 解析返回数据
        version_info = response.text.strip()
        print(f"API返回值: {version_info}")  # 打印API返回值

        # 检查错误码
        if version_info.startswith("-"):
            return False, f"获取版本号失败: {version_info}"

        return True, version_info

    except requests.RequestException as e:
        return False, f"获取版本号失败: {str(e)}"
    except Exception as e:
        return False, f"检查版本时发生错误: {str(e)}"


def check_update():
    """检查软件更新"""
    try:
        # 先获取token
        config = load_config()
        if not config.get("card") or not config.get("machine_code"):
            return False, "未找到有效的卡密信息"

        token = login(config["card"], config["machine_code"])
        if not token:
            return False, "获取token失败"

        post_data = {
            "Token": token,
            "Id": SOFT_ID,
            "Version": VERSION
        }
        headers = {"Content-Type": "application/x-www-form-urlencoded"}

        print(f"更新检查参数: {post_data}")

        try:
            response = requests.post(API_UPDATE, data=post_data, headers=headers, timeout=10)
            response.raise_for_status()
        except requests.RequestException as e:
            return False, f"请求更新接口失败: {str(e)}"

        update_info = response.text.strip()
        print(f"API返回值: {update_info}")

        if not update_info:
            return False, "API返回数据为空"

        if update_info.startswith("-"):
            return False, f"检查更新失败: {update_info}"

        return (True, update_info) if update_info != VERSION else (False, VERSION)

    except Exception as e:
        return False, f"检查更新时发生未知错误: {str(e)}"


def get_announcement():
    """获取公告内容，支持双接口自动切换

    Returns:
        tuple: (是否成功, 公告内容或错误信息)
    """
    try:
        print("\n===== 开始获取公告内容 =====")

        # 构建请求参数
        post_data = {
            "Softid": SOFT_KEY  # 使用软件标识
        }

        print(f"请求参数: {post_data}")

        # 使用通用API请求函数
        success, result, interface_used = make_api_request('announcement', post_data, timeout=10)

        if not success:
            print(f"获取公告失败: {result}")
            print("===== 获取公告结束 =====\n")
            return False, f"获取公告失败: {result}"

        # 检查返回内容是否是错误码
        if result.startswith("-"):
            print(f"获取公告失败: {result}")
            print("===== 获取公告结束 =====\n")
            return False, f"获取公告失败: {result}"

        # 打印返回信息（不打印全部内容，可能很长）
        preview = result[:50] + "..." if len(result) > 50 else result
        print(f"API返回值: {preview}")

        if interface_used:
            print(f"公告获取成功，使用接口: {API_INTERFACES[interface_used]['name']}")
        print("===== 获取公告结束 =====\n")
        return True, result

    except Exception as e:
        print(f"获取公告时发生错误: {str(e)}")
        print("===== 获取公告结束 =====\n")
        return False, f"获取公告时发生错误: {str(e)}"


def check_network_connection():
    """检查网络连接状态"""
    try:
        # 尝试连接到百度来检测网络连接
        socket.create_connection(("www.baidu.com", 80), timeout=5)
        return True
    except OSError:
        try:
            # 再尝试连接谷歌
            socket.create_connection(("www.google.com", 80), timeout=5)
            return True
        except OSError:
            return False


def verify_license(card_number):
    """验证卡密"""
    try:
        # 首先执行防破解检测
        detector = AntiCrackDetector()
        detection_passed, detection_results = detector.perform_full_check()

        if not detection_passed:
            # 记录检测结果
            print("防破解检测失败:")
            for result in detection_results:
                print(f"  - {result}")

            # 如果配置为检测到后立即退出
            if ANTI_CRACK_CONFIG['exit_on_detect']:
                error_msg = "安全检测失败，程序将退出。\n检测到以下问题:\n" + "\n".join(detection_results[:3])
                if len(detection_results) > 3:
                    error_msg += f"\n... 还有 {len(detection_results) - 3} 个问题"

                # 显示错误信息后退出
                try:
                    from PyQt5.QtWidgets import QMessageBox, QApplication
                    app = QApplication.instance()
                    if app:
                        QMessageBox.critical(None, "安全检测失败", error_msg)
                        app.processEvents()
                except Exception:
                    pass

                # 强制退出
                os._exit(1)

            return False, "安全检测失败，无法继续验证"

        # 首先检查网络连接
        if not check_network_connection():
            return False, "网络连接异常，无法验证卡密。请检查您的网络连接后重试。"

        # 获取账号（机器码）
        machine_code = get_machine_code()

        # 使用新的VMP授权登录流程
        print("🔐 开始VMP授权验证流程...")
        login_result = login_with_vmp_authorization(card_number, machine_code)

        if not login_result['success']:
            # 如果VMP授权失败，尝试普通登录作为备用
            print("⚠️ VMP授权登录失败，尝试普通登录...")
            token = login(card_number, machine_code)
            if not token:
                return False, f"登录失败: {login_result.get('error', '未知错误')}"
            print("✅ 普通登录成功，但未获取VMP授权")
        else:
            token = login_result['token']
            vmp_auth = login_result['vmp_auth']
            print(f"✅ VMP授权验证完成，Token: {token[:8]}..., VMP授权: {vmp_auth[:16] if vmp_auth else 'None'}...")

            # 将VMP授权信息保存到检测器中以供后续使用
            detector.vmp_authorization = vmp_auth
            detector.detection_results.append(f"VMP授权验证: 成功获取授权码 {vmp_auth[:16]}...")

        # 检查到期时间
        try:
            result = check_card_expiry(card_number)
            # 确保返回值是一个元组，并且有两个元素
            if result is None:
                return False, "检查到期时间失败: 服务器未返回有效数据"

            if not isinstance(result, tuple) or len(result) < 2:
                return False, f"检查到期时间失败: 返回格式错误 {result}"

            valid, expiry_info = result
            if not valid:
                return False, expiry_info
        except Exception as e:
            return False, f"检查到期时间失败: {str(e)}"

        # 保存验证通过的卡密到配置
        config = load_config() or {}
        config["card"] = card_number
        config["card_number"] = card_number  # 兼容旧版本
        config["machine_code"] = machine_code
        config["expiry_time"] = expiry_info
        # 默认记住登录状态为True
        config["remember_login"] = True
        save_config(config)

        return True, expiry_info
    except Exception as e:
        return False, f"验证过程发生错误: {str(e)}"


def check_expiry():
    """检查卡密是否到期"""
    try:
        config = load_config()
        if not config:
            return False, "配置文件不存在或为空"

        card_number = config.get('card_number') or config.get('card')
        machine_code = config.get('machine_code')

        if not card_number or not machine_code:
            return False, "卡密或机器码不存在"

        try:
            result = check_card_expiry(card_number)
            # 确保返回值是一个元组，并且有两个元素
            if result is None:
                return False, "检查到期时间失败: 服务器未返回有效数据"

            if not isinstance(result, tuple) or len(result) < 2:
                return False, f"检查到期时间失败: 返回格式错误 {result}"

            valid, expiry_info = result
            if not valid:
                return False, expiry_info
        except Exception as e:
            return False, f"检查到期时间失败: {str(e)}"

        # 更新配置
        config['expiry_time'] = expiry_info
        save_config(config)

        return True, expiry_info
    except Exception as e:
        return False, f"查询服务器连接失败: {str(e)}"


class LoginDialog(QDialog):
    """卡密登录对话框"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle(f"头条自媒体工具 - 授权验证 v{APP_VERSION}")
        self.setFixedSize(580, 800)  # 进一步增大窗口高度，解决接口选择区域遮挡问题
        # 设置窗口图标和样式
        self.setStyleSheet("""
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                border-radius: 8px;
            }
        """)
        self.center()  # 居中显示
        self.init_ui()
        self.signals = SignalHub()
        self.login_success = False

        # 检查更新
        QTimer.singleShot(500, self.check_for_updates)

    def center(self):
        """窗口居中"""
        qr = self.frameGeometry()
        cp = QDesktopWidget().availableGeometry().center()
        qr.moveCenter(cp)
        self.move(qr.topLeft())

    def init_ui(self):
        """初始化现代化UI"""
        # 主布局
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(20, 12, 20, 12)  # 减少边距
        main_layout.setSpacing(6)  # 减少组件间距，为接口选择区域腾出空间

        # 顶部标题区域
        header_widget = QWidget()
        header_widget.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #667eea, stop:1 #764ba2);
                border-radius: 12px;
                margin-bottom: 20px;
            }
        """)
        header_layout = QVBoxLayout(header_widget)
        header_layout.setContentsMargins(20, 12, 20, 12)  # 减少头部内边距

        # 主标题
        title_label = QLabel("🎯 头条自媒体工具")
        title_label.setStyleSheet("""
            QLabel {
                color: white;
                font-size: 20px;
                font-weight: bold;
                font-family: 'Microsoft YaHei';
                text-align: center;
                background: transparent;
            }
        """)
        header_layout.addWidget(title_label)

        # 副标题
        subtitle_label = QLabel(f"授权验证系统 v{APP_VERSION}")
        subtitle_label.setStyleSheet("""
            QLabel {
                color: rgba(255, 255, 255, 0.9);
                font-size: 12px;
                font-family: 'Microsoft YaHei';
                text-align: center;
                background: transparent;
                margin-top: 5px;
            }
        """)
        header_layout.addWidget(subtitle_label)

        main_layout.addWidget(header_widget)
        main_layout.addSpacing(6)  # 进一步减少间距，为接口选择区域腾出空间

        # 内容区域容器
        content_widget = QWidget()
        content_widget.setStyleSheet("""
            QWidget {
                background: white;
                border-radius: 12px;
                border: 1px solid #e0e0e0;
            }
        """)
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(15, 15, 15, 15)  # 适当的内容区域内边距
        content_layout.setSpacing(15)  # 适当的组件间距，避免挤压

        # 获取真实机器码
        real_machine_code = get_machine_code()
        # 创建一个部分隐藏的机器码，一半显示，一半用*号替代
        half_length = len(real_machine_code) // 2
        masked_machine_code = real_machine_code[:half_length] + '*' * (len(real_machine_code) - half_length)

        # 账号区域
        account_group = QWidget()
        account_group.setStyleSheet("""
            QWidget {
                background: #f8f9fa;
                border-radius: 8px;
                border: 1px solid #dee2e6;
            }
        """)
        account_layout = QVBoxLayout(account_group)
        account_layout.setContentsMargins(15, 12, 15, 12)  # 适当的账号区域内边距
        account_layout.setSpacing(8)  # 添加垂直间距

        # 账号标签
        account_title = QLabel("👤 设备账号")
        account_title.setStyleSheet("""
            QLabel {
                color: #495057;
                font-size: 14px;
                font-weight: bold;
                font-family: 'Microsoft YaHei';
                background: transparent;
                border: none;
                margin-bottom: 8px;
            }
        """)
        account_layout.addWidget(account_title)

        # 机器码输入区域
        machine_code_container = QHBoxLayout()
        machine_code_container.setSpacing(10)

        self.machine_code_text = QLineEdit(masked_machine_code)
        self.machine_code_text.setReadOnly(True)
        self.machine_code_text.setStyleSheet("""
            QLineEdit {
                background: white;
                border: 2px solid #e9ecef;
                border-radius: 6px;
                padding: 8px 12px;
                font-family: 'Consolas';
                font-size: 11px;
                color: #495057;
                selection-background-color: #667eea;
            }
            QLineEdit:focus {
                border-color: #667eea;
            }
        """)

        # 存储真实机器码，用于复制
        self.real_machine_code = real_machine_code

        # 复制按钮
        copy_btn = QPushButton("📋 复制")
        copy_btn.setFixedSize(80, 36)
        copy_btn.setStyleSheet("""
            QPushButton {
                background: #6c757d;
                color: white;
                border: none;
                border-radius: 6px;
                font-family: 'Microsoft YaHei';
                font-size: 10px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #5a6268;
            }
            QPushButton:pressed {
                background: #545b62;
            }
        """)
        copy_btn.clicked.connect(self.copy_machine_code)

        machine_code_container.addWidget(self.machine_code_text)
        machine_code_container.addWidget(copy_btn)
        account_layout.addLayout(machine_code_container)
        content_layout.addWidget(account_group)

        # 卡密输入区域
        card_group = QWidget()
        card_group.setStyleSheet("""
            QWidget {
                background: #f8f9fa;
                border-radius: 8px;
                border: 1px solid #dee2e6;
            }
        """)
        card_layout = QVBoxLayout(card_group)
        card_layout.setContentsMargins(15, 12, 15, 12)  # 适当的卡密区域内边距
        card_layout.setSpacing(8)  # 添加垂直间距

        # 卡密标签
        card_title = QLabel("🔑 授权卡密")
        card_title.setStyleSheet("""
            QLabel {
                color: #495057;
                font-size: 14px;
                font-weight: bold;
                font-family: 'Microsoft YaHei';
                background: transparent;
                border: none;
                margin-bottom: 8px;
            }
        """)
        card_layout.addWidget(card_title)

        # 卡密输入框
        self.card_edit = QLineEdit()
        self.card_edit.setPlaceholderText("请输入您的授权卡密...")
        self.card_edit.setStyleSheet("""
            QLineEdit {
                background: white;
                border: 2px solid #e9ecef;
                border-radius: 6px;
                padding: 12px 15px;
                font-family: 'Consolas';
                font-size: 12px;
                color: #495057;
                selection-background-color: #667eea;
            }
            QLineEdit:focus {
                border-color: #667eea;
                background: #ffffff;
            }
            QLineEdit:hover {
                border-color: #ced4da;
            }
        """)
        card_layout.addWidget(self.card_edit)

        content_layout.addWidget(card_group)

        # 接口选择区域 - 简化为下拉菜单避免挤压
        interface_group = QWidget()
        interface_group.setStyleSheet("""
            QWidget {
                background: #f8f9fa;
                border-radius: 8px;
                border: 1px solid #dee2e6;
                min-height: 50px;
                max-height: 60px;
            }
        """)
        interface_layout = QHBoxLayout(interface_group)  # 改为水平布局
        interface_layout.setContentsMargins(15, 10, 15, 10)
        interface_layout.setSpacing(15)

        # 接口选择标签
        interface_label = QLabel("接口:")
        interface_label.setStyleSheet("""
            QLabel {
                color: #495057;
                font-size: 12px;
                font-weight: bold;
                font-family: 'Microsoft YaHei';
                background: transparent;
                min-width: 40px;
            }
        """)
        interface_layout.addWidget(interface_label)

        # 接口选择下拉菜单
        from PyQt5.QtWidgets import QComboBox
        self.interface_combo = QComboBox()
        self.interface_combo.addItem("接口1 (主要)", "interface1")
        self.interface_combo.addItem("接口2 (备用)", "interface2")
        self.interface_combo.setStyleSheet("""
            QComboBox {
                background: white;
                border: 2px solid #e9ecef;
                border-radius: 6px;
                padding: 5px 10px;
                font-family: 'Microsoft YaHei';
                font-size: 11px;
                color: #495057;
                min-width: 120px;
                min-height: 25px;
            }
            QComboBox:hover {
                border-color: #667eea;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #6c757d;
                margin-right: 5px;
            }
        """)
        interface_layout.addWidget(self.interface_combo)

        # 自动切换复选框
        self.auto_switch_checkbox = QCheckBox("自动切换")
        self.auto_switch_checkbox.setStyleSheet("""
            QCheckBox {
                color: #28a745;
                font-size: 11px;
                font-family: 'Microsoft YaHei';
                font-weight: 500;
                background: transparent;
                spacing: 5px;
                padding: 5px;
            }
            QCheckBox::indicator {
                width: 14px;
                height: 14px;
                border: 2px solid #28a745;
                border-radius: 3px;
                background: white;
            }
            QCheckBox::indicator:checked {
                background: #28a745;
                border-color: #28a745;
            }
        """)
        interface_layout.addWidget(self.auto_switch_checkbox)
        interface_layout.addStretch()  # 添加弹性空间

        # 连接信号
        self.interface_combo.currentIndexChanged.connect(self.on_interface_combo_changed)
        self.auto_switch_checkbox.toggled.connect(self.on_auto_switch_changed)

        # 设置默认选择
        current_interface = api_manager.current_interface
        if current_interface == 'interface1':
            self.interface_combo.setCurrentIndex(0)
        else:
            self.interface_combo.setCurrentIndex(1)

        self.auto_switch_checkbox.setChecked(api_manager.auto_switch)

        # 接口状态提示 - 极简显示
        self.interface_status_label = QLabel()
        self.interface_status_label.setStyleSheet("""
            QLabel {
                color: #6c757d;
                font-size: 9px;
                font-family: 'Microsoft YaHei';
                background: rgba(108, 117, 125, 0.08);
                border: none;
                border-radius: 3px;
                margin-top: 3px;
                padding: 3px 6px;
                line-height: 1.2;
            }
        """)
        self.interface_status_label.setWordWrap(False)  # 禁用换行，强制单行
        self.interface_status_label.setMaximumHeight(20)  # 严格限制高度
        self.update_interface_status_display()

        # 状态显示已经在interface_layout中，不需要额外添加
        content_layout.addWidget(interface_group)

        # 选项区域
        options_layout = QHBoxLayout()
        self.remember_checkbox = QCheckBox("💾 记住登录状态")
        self.remember_checkbox.setStyleSheet("""
            QCheckBox {
                color: #6c757d;
                font-family: 'Microsoft YaHei';
                font-size: 11px;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
                border-radius: 3px;
                border: 2px solid #ced4da;
                background: white;
            }
            QCheckBox::indicator:checked {
                background: #667eea;
                border-color: #667eea;
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDQuNUw0LjUgOEwxMSAxIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
            }
            QCheckBox::indicator:hover {
                border-color: #667eea;
            }
        """)
        self.remember_checkbox.setChecked(True)  # 默认勾选
        options_layout.addWidget(self.remember_checkbox)
        options_layout.addStretch()

        content_layout.addLayout(options_layout)
        main_layout.addWidget(content_widget)
        main_layout.addSpacing(8)  # 减少间距，为接口选择区域腾出空间

        # 按钮区域
        button_container = QWidget()
        button_container.setStyleSheet("""
            QWidget {
                background: transparent;
                border: none;
            }
        """)
        button_layout = QHBoxLayout(button_container)
        button_layout.setContentsMargins(0, 5, 0, 5)  # 减少按钮区域内边距
        button_layout.setSpacing(12)  # 减少按钮间距

        # 按钮样式定义
        primary_button_style = """
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #667eea, stop:1 #764ba2);
                color: white;
                border: none;
                border-radius: 8px;
                font-family: 'Microsoft YaHei';
                font-size: 12px;
                font-weight: bold;
                padding: 12px 20px;
                min-width: 100px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #5a67d8, stop:1 #6b46c1);
                transform: translateY(-1px);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4c51bf, stop:1 #553c9a);
                transform: translateY(0px);
            }
        """

        secondary_button_style = """
            QPushButton {
                background: white;
                color: #667eea;
                border: 2px solid #667eea;
                border-radius: 8px;
                font-family: 'Microsoft YaHei';
                font-size: 11px;
                font-weight: bold;
                padding: 10px 18px;
                min-width: 90px;
            }
            QPushButton:hover {
                background: #f7fafc;
                border-color: #5a67d8;
                color: #5a67d8;
            }
            QPushButton:pressed {
                background: #edf2f7;
                border-color: #4c51bf;
                color: #4c51bf;
            }
        """

        # 登录按钮（主要按钮）
        self.login_btn = QPushButton("🚀 立即登录")
        self.login_btn.setFixedHeight(40)  # 减少高度
        self.login_btn.setStyleSheet(primary_button_style)
        self.login_btn.clicked.connect(self.verify_login)

        # 购买按钮
        self.buy_btn = QPushButton("💳 购买卡密")
        self.buy_btn.setFixedHeight(36)  # 减少高度
        self.buy_btn.setStyleSheet(secondary_button_style)
        self.buy_btn.clicked.connect(self.open_buy_page)

        # 检查更新按钮
        self.update_btn = QPushButton("🔄 检查更新")
        self.update_btn.setFixedHeight(36)  # 减少高度
        self.update_btn.setStyleSheet(secondary_button_style)
        self.update_btn.clicked.connect(self.check_for_updates)

        button_layout.addStretch()
        button_layout.addWidget(self.login_btn)
        button_layout.addWidget(self.buy_btn)
        button_layout.addWidget(self.update_btn)
        button_layout.addStretch()



        main_layout.addWidget(button_container)
        main_layout.addSpacing(8)  # 减少按钮和底部区域间距

        # 底部状态区域
        footer_widget = QWidget()
        footer_widget.setStyleSheet("""
            QWidget {
                background: transparent;
                border: none;
            }
        """)
        footer_layout = QVBoxLayout(footer_widget)
        footer_layout.setContentsMargins(0, 8, 0, 5)  # 适当的底部区域内边距
        footer_layout.setSpacing(5)  # 适当的底部组件间距

        # 状态信息
        self.status_label = QLabel("💡 请输入您的授权卡密进行登录")
        self.status_label.setStyleSheet("""
            QLabel {
                color: #6c757d;
                font-family: 'Microsoft YaHei';
                font-size: 12px;
                text-align: center;
                background: transparent;
                border: none;
                padding: 4px;
            }
        """)
        footer_layout.addWidget(self.status_label)

        # 版本信息容器
        version_container = QHBoxLayout()

        # 左侧：软件信息
        software_info = QLabel("头条自媒体工具")
        software_info.setStyleSheet("""
            QLabel {
                color: #adb5bd;
                font-family: 'Microsoft YaHei';
                font-size: 9px;
                background: transparent;
                border: none;
                padding: 2px;
            }
        """)

        # 右侧：版本信息
        version_label = QLabel(f"服务器版本: v{VERSION}")
        version_label.setStyleSheet("""
            QLabel {
                color: #adb5bd;
                font-family: 'Microsoft YaHei';
                font-size: 9px;
                background: transparent;
                border: none;
                padding: 2px;
            }
        """)

        version_container.addWidget(software_info)
        version_container.addStretch()
        version_container.addWidget(version_label)
        footer_layout.addLayout(version_container)

        main_layout.addWidget(footer_widget)
        # 移除拉伸空间，确保所有内容都能显示

        # 设置主布局
        self.setLayout(main_layout)

        # 自动加载保存的卡密
        self.load_saved_card()

        # 设置回车键响应
        self.card_edit.returnPressed.connect(self.verify_login)



    def verify_login(self):
        """验证登录"""
        card_number = self.card_edit.text().strip()

        if not card_number:
            QMessageBox.warning(self, "提示", "请输入卡密")
            return

        self.status_label.setText("正在验证卡密，请稍候...")
        self.login_btn.setEnabled(False)
        self.card_edit.setEnabled(False)

        # 启动验证过程
        valid, result = verify_license(card_number)

        # 获取记住登录状态
        remember_login = self.remember_checkbox.isChecked()

        if valid:
            # 加载并更新配置
            config = load_config()
            config['remember_login'] = remember_login
            save_config(config)

            # 设置login_success为True
            self.login_success = True
            self.signals.login_success.emit(card_number, result)

            # 显示成功信息并提示是否记住登录状态
            if remember_login:
                QMessageBox.information(self, "验证成功", f"卡密验证成功!\n到期时间: {result}\n\n已记住登录状态，下次启动将自动登录。")
            else:
                QMessageBox.information(self, "验证成功", f"卡密验证成功!\n到期时间: {result}")

            # 关闭对话框
            self.accept()
        else:
            # 恢复UI状态
            self.login_btn.setEnabled(True)
            self.card_edit.setEnabled(True)

            # 显示错误信息
            QMessageBox.warning(self, "验证失败", f"卡密验证失败!\n{result}")
            self.status_label.setText(f"验证失败: {result}")
            self.signals.login_failed.emit(result)

    def check_for_updates(self):
        """检查更新"""
        try:
            self.status_label.setText("正在检查更新...")
            QApplication.processEvents()  # 确保UI更新

            try:
                result = get_server_update_info()
                # 确保返回值是一个元组，并且有两个元素
                if result is None:
                    success = False
                    server_version = "获取更新信息失败: 服务器未返回有效数据"
                elif not isinstance(result, tuple) or len(result) < 2:
                    success = False
                    server_version = f"获取更新信息失败: 返回格式错误 {result}"
                else:
                    success, server_version = result
            except Exception as e:
                success = False
                server_version = f"获取更新信息失败: {str(e)}"

            if success and server_version != APP_VERSION:
                self.status_label.setText(f"发现新版本: v{server_version}")
                update_msg = f"发现新版本: v{server_version}，当前版本: v{APP_VERSION}。是否立即更新?"
                if QMessageBox.question(self, "发现新版本", update_msg,
                                      QMessageBox.Yes | QMessageBox.No) == QMessageBox.Yes:
                    # 实现自动下载更新功能
                    try:
                        self.status_label.setText("正在准备下载更新...")
                        QApplication.processEvents()  # 确保UI更新

                        # 获取下载地址
                        download_url = self.get_download_url(server_version)
                        if not download_url:
                            print("无法获取更新下载地址")
                            QMessageBox.warning(self, "更新失败",
                                              "无法获取更新下载地址。错误原因可能是:\n"
                                              "1. 网络连接问题\n"
                                              "2. 服务器暂时不可用\n"
                                              "3. 软件标识有误\n\n"
                                              "请稍后再试或联系客服获取最新版本。")
                            self.status_label.setText("更新失败: 无法获取下载地址")
                            return

                        # 创建下载进度对话框
                        progress_dialog = QProgressDialog("正在下载新版本到当前目录...", "取消", 0, 100, None)
                        progress_dialog.setWindowTitle("下载进度")
                        progress_dialog.setMinimumDuration(0)
                        # 不使用setWindowModality，改用其他方式实现模态效果
                        progress_dialog.setModal(True)
                        progress_dialog.setValue(0)

                        # 让窗口适应内容大小
                        progress_dialog.setFixedSize(400, 100)

                        # 显示进度条
                        progress_dialog.show()

                        # 确保应用处理事件
                        app = QApplication.instance()
                        if app:
                            app.processEvents()

                        # 为了模拟下载进度，使用单独的线程进行下载
                        import threading

                        # 下载状态共享变量
                        download_status = {
                            "path": None,
                            "error": None,
                            "completed": False,
                            "total_size": 0,
                            "current_size": 0,
                            "retry_count": 0,
                            "status_message": "正在准备下载..."
                        }

                        # 下载线程函数
                        def download_thread_func():
                            try:
                                # 获取文件总大小
                                session = requests.Session()
                                response = session.head(download_url, timeout=15)
                                total_size = int(response.headers.get('content-length', 0))
                                download_status["total_size"] = total_size

                                # 使用当前工作目录作为下载位置
                                current_dir = os.getcwd()

                                # 创建标准命名的文件名（使用软件原名）
                                file_name = f"头条自媒体自动存稿工具_v{server_version}.exe"
                                download_path = os.path.join(current_dir, file_name)
                                temp_path = f"{download_path}.tmp"

                                # 检查是否已有部分下载的文件
                                downloaded_size = 0
                                if os.path.exists(temp_path):
                                    downloaded_size = os.path.getsize(temp_path)
                                    download_status["current_size"] = downloaded_size
                                    download_status["status_message"] = f"发现已下载部分: {downloaded_size/1024/1024:.2f} MB"
                                    if downloaded_size >= total_size and total_size > 0:
                                        download_status["status_message"] = "文件已完全下载，正在校验..."
                                        os.rename(temp_path, download_path)
                                        download_status["path"] = download_path
                                        download_status["completed"] = True
                                        return

                                # 重试次数和间隔设置
                                max_retries = 5
                                retry_delay = 3  # 秒
                                retry_count = 0

                                # 断点续传 - 设置请求头，添加更多浏览器标识信息
                                headers = {
                                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                                    'Accept-Encoding': 'gzip, deflate, br',
                                    'Connection': 'keep-alive',
                                    'Referer': 'http://api.1wxyun.com/'
                                }
                                if downloaded_size > 0:
                                    headers['Range'] = f'bytes={downloaded_size}-'
                                    download_status["status_message"] = f"设置断点续传，从 {downloaded_size/1024/1024:.2f} MB 继续"

                                # 使用with上下文自动关闭文件
                                with open(temp_path, 'ab' if downloaded_size > 0 else 'wb') as f:
                                    while retry_count < max_retries:
                                        try:
                                            # 设置较短的超时时间以便及时检测连接问题
                                            # 使用不同的下载方法尝试
                                            try:
                                                # 方法1: 使用标准会话对象
                                                response = session.get(download_url, headers=headers,
                                                                    stream=True, timeout=(5, 30))
                                                response.raise_for_status()
                                            except requests.RequestException as e1:
                                                print(f"标准下载方法失败: {str(e1)}，尝试备用方法...")

                                                # 方法2: 创建新的会话对象，使用不同的请求头
                                                try:
                                                    alt_session = requests.Session()
                                                    alt_headers = headers.copy()
                                                    alt_headers['User-Agent'] = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) Chrome/120.0.0.0 Safari/537.36'
                                                    alt_headers['Referer'] = 'https://www.baidu.com/'

                                                    response = alt_session.get(download_url, headers=alt_headers,
                                                                            stream=True, timeout=(5, 30))
                                                    response.raise_for_status()
                                                except requests.RequestException as e2:
                                                    print(f"备用下载方法也失败: {str(e2)}，抛出异常...")
                                                    raise e2

                                            # 计算当前块起始位置
                                            current_size = downloaded_size

                                            # 使用更小的块大小，提高稳定性
                                            chunk_size = 1024 * 1024  # 1MB 块
                                            for chunk in response.iter_content(chunk_size=chunk_size):
                                                if chunk:
                                                    f.write(chunk)
                                                    current_size += len(chunk)
                                                    download_status["current_size"] = current_size
                                                    # 刷新文件缓冲，确保写入磁盘
                                                    f.flush()
                                                    os.fsync(f.fileno())

                                            # 下载完成，退出循环
                                            break
                                        except (requests.RequestException, IOError) as e:
                                            retry_count += 1
                                            download_status["retry_count"] = retry_count
                                            download_status["status_message"] = f"下载中断! 正在进行第 {retry_count}/{max_retries} 次重试..."

                                            if retry_count >= max_retries:
                                                download_status["status_message"] = "达到最大重试次数，下载失败"
                                                raise Exception(f"下载失败，已重试 {max_retries} 次: {str(e)}")

                                            # 更新已下载大小
                                            if os.path.exists(temp_path):
                                                downloaded_size = os.path.getsize(temp_path)
                                                download_status["current_size"] = downloaded_size
                                                headers['Range'] = f'bytes={downloaded_size}-'

                                            # 等待一段时间后重试
                                            time.sleep(retry_delay)

                                # 检查文件完整性
                                if total_size > 0:
                                    final_size = os.path.getsize(temp_path)
                                    if final_size < total_size:
                                        raise Exception(f"下载文件不完整: {final_size}/{total_size} 字节")
                                    download_status["status_message"] = "文件下载完成，正在校验..."

                                # 重命名临时文件为正式文件名
                                if os.path.exists(download_path):
                                    os.remove(download_path)
                                os.rename(temp_path, download_path)

                                download_status["path"] = download_path
                                download_status["status_message"] = "下载完成!"
                            except Exception as e:
                                download_status["error"] = str(e)
                                download_status["status_message"] = f"下载错误: {str(e)}"
                            finally:
                                download_status["completed"] = True

                        # 启动下载线程
                        download_thread = threading.Thread(target=download_thread_func)
                        download_thread.daemon = True
                        download_thread.start()

                        # 进度更新定时器
                        while not download_status["completed"] and not progress_dialog.wasCanceled():
                            # 计算实际进度
                            current_size = download_status["current_size"]
                            total_size = download_status["total_size"]

                            if total_size > 0:
                                # 计算真实进度
                                progress = min(int((current_size / total_size) * 100), 99)
                                progress_dialog.setValue(progress)

                                # 显示下载速度和状态
                                status_message = download_status["status_message"]
                                size_info = f"[{current_size/1024/1024:.2f}/{total_size/1024/1024:.2f} MB] {progress}%"
                                retry_info = ""
                                if download_status["retry_count"] > 0:
                                    retry_info = f" (已重试: {download_status['retry_count']}次)"

                                progress_dialog.setLabelText(f"{status_message}{retry_info}\n{size_info}")
                            else:
                                # 如果无法获取总大小，使用模拟进度
                                progress_dialog.setValue(min(download_status["retry_count"] * 10 + 10, 90))
                                progress_dialog.setLabelText(f"{download_status['status_message']}\n已下载: {current_size/1024/1024:.2f} MB")

                            # 处理事件
                            if app:
                                app.processEvents()

                            # 等待100毫秒
                            time.sleep(0.1)

                        # 如果用户取消了下载
                        if progress_dialog.wasCanceled():
                            print("用户取消下载")
                            self.status_label.setText("更新已取消")
                            return

                        # 检查下载结果
                        if download_status["error"]:
                            print(f"下载更新失败: {download_status['error']}")
                            QMessageBox.warning(self, "更新失败", f"下载更新失败: {download_status['error']}\n请稍后重试或访问官网手动下载更新。")
                            self.status_label.setText(f"下载失败: {download_status['error']}")
                            return

                        download_path = download_status["path"]
                        if download_path:
                            # 设置进度为100%
                            progress_dialog.setValue(100)
                            progress_dialog.setLabelText("下载完成，准备打开下载目录...")
                            self.status_label.setText("下载完成，准备打开下载目录...")

                            # 确保app不为None
                            if app:
                                app.processEvents()

                            # 关闭进度对话框
                            progress_dialog.close()

                            # 安装更新
                            self.install_update(download_path)
                        else:
                            QMessageBox.warning(self, "更新失败", "下载更新失败，请访问官网手动下载更新。")
                            self.status_label.setText("下载更新失败")
                    except Exception as e:
                        QMessageBox.warning(self, "更新失败", f"更新过程中出错: {str(e)}\n请访问官网手动下载更新。")
                        self.status_label.setText(f"更新错误: {str(e)}")
            elif success:
                self.status_label.setText("当前已是最新版本")
            else:
                self.status_label.setText(f"检查更新失败: {server_version}")
        except Exception as e:
            print(f"检查更新失败: {str(e)}")
            # 更新检查失败不影响正常登录，所以不显示错误消息
            self.status_label.setText("检查更新失败")

    def get_download_url(self, version=None):
        """获取更新下载地址，支持双接口自动切换

        Args:
            version: 版本号参数（在此API中实际未使用，但保留参数以兼容现有调用）
        """
        try:
            print("\n===== 开始获取下载地址 =====")

            # 构建正确的请求参数，只使用Softid
            post_data = {
                "Softid": SOFT_KEY
            }

            print(f"请求参数: Softid={SOFT_KEY}")

            # 发送请求，添加更多浏览器标识信息
            headers = {
                "Content-Type": "application/x-www-form-urlencoded",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
                "Accept-Encoding": "gzip, deflate, br",
                "Connection": "keep-alive",
                "Cache-Control": "no-cache",
                "Pragma": "no-cache"
            }

            # 使用通用API请求函数
            success, result, interface_used = make_api_request('download', post_data, headers=headers, timeout=15)

            if not success:
                print(f"获取下载地址失败: {result}")
                print("===== 获取下载地址结束 =====\n")
                return None

            download_url = result

            # 解析返回数据
            print(f"API返回值: {download_url[:30]}{'...' if len(download_url) > 30 else ''}")

            if interface_used:
                print(f"下载地址获取成功，使用接口: {API_INTERFACES[interface_used]['name']}")

            # 检查URL是否有效
            if download_url.startswith("http"):
                print("获取到有效的下载地址")
                print("===== 下载地址获取成功 =====\n")
                return download_url
            else:
                print(f"返回值不是有效的URL: {download_url}")
                if download_url.startswith("-"):
                    print(f"返回了错误码: {download_url}")
                print("===== 下载地址获取失败 =====\n")
                return None
        except Exception as e:
            print(f"获取下载地址失败: {str(e)}")
            import traceback
            print(traceback.format_exc())
            print("===== 下载地址获取失败 =====\n")
            return None

    def install_update(self, file_path):
        """安装更新

        Args:
            file_path: 更新文件路径
        """
        try:
            # 打开目录
            import os
            current_dir = os.path.dirname(os.path.abspath(file_path))

            # 创建自定义对话框，提供更明确的步骤指导
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QLabel, QPushButton, QHBoxLayout

            # 创建对话框
            dialog = QDialog(None)
            dialog.setWindowTitle("更新已下载完成")
            dialog.setMinimumWidth(500)

            # 创建布局
            main_layout = QVBoxLayout()

            # 标题
            title_label = QLabel(f"<h2>发现新版本：头条自媒体自动存稿工具 v{os.path.basename(file_path).split('_v')[1].replace('.exe', '')}</h2>")
            title_label.setStyleSheet("font-weight: bold;")
            main_layout.addWidget(title_label)

            # 说明文本
            info_text = f"""
            <p>新版本已下载到当前目录：</p>
            <p style="color: blue;">{file_path}</p>
            <p>请按照以下步骤完成更新：</p>
            <ol>
                <li>关闭当前软件</li>
                <li>删除原版本软件</li>
                <li>运行新下载的软件</li>
            </ol>
            """
            info_label = QLabel(info_text)
            # 使用HTML标签直接设置富文本，避免使用Qt.RichText
            # 将info_text包装在HTML标签中
            info_label.setText(f"<html>{info_text}</html>")
            info_label.setWordWrap(True)
            main_layout.addWidget(info_label)

            # 按钮布局
            button_layout = QHBoxLayout()

            # 打开下载目录按钮
            open_dir_btn = QPushButton("打开下载目录")
            open_dir_btn.setMinimumHeight(40)

            def open_download_dir():
                # 打开文件资源管理器并选中文件
                import subprocess
                if os.name == 'nt':  # Windows
                    subprocess.Popen(f'explorer /select,"{file_path}"')
                elif os.name == 'posix':  # Linux/Mac
                    subprocess.Popen(['xdg-open', os.path.dirname(file_path)])
                dialog.accept()

            open_dir_btn.clicked.connect(open_download_dir)
            button_layout.addWidget(open_dir_btn)

            # 稍后更新按钮
            later_btn = QPushButton("稍后更新")
            later_btn.setMinimumHeight(40)
            later_btn.clicked.connect(dialog.reject)
            button_layout.addWidget(later_btn)

            # 添加按钮布局
            main_layout.addLayout(button_layout)

            # 设置对话框布局
            dialog.setLayout(main_layout)

            # 显示对话框
            result = dialog.exec_()

            # 如果用户选择打开下载目录，则打开目录后退出软件
            if result == QDialog.Accepted:
                # 在这里不需要额外代码，因为open_download_dir函数已经会打开文件资源管理器
                # 提示用户软件将退出
                QMessageBox.information(None, "软件将退出", "请运行新下载的软件版本。\n当前程序即将关闭。")
                os._exit(0)

            return True
        except Exception as e:
            QMessageBox.warning(None, "更新处理失败", f"处理更新过程中出错: {str(e)}\n\n您仍可以手动打开位置：\n{file_path}")
            return False

    def open_buy_page(self):
        """打开购买页面"""
        try:
            import webbrowser
            import os

            # 获取当前脚本所在目录
            current_dir = os.path.dirname(os.path.abspath(__file__))

            # 构建kami_purchase.html的完整路径
            buy_page_path = os.path.join(current_dir, "kami_purchase.html")

            # 检查文件是否存在
            if not os.path.exists(buy_page_path):
                QMessageBox.warning(self, "文件不存在", "卡密购买页面文件不存在")
                return

            # 使用file://协议打开本地HTML文件
            # 先替换路径分隔符，再进行字符串格式化，避免在f字符串中使用转义序列
            path_with_forward_slashes = buy_page_path.replace('\\', '/')
            file_url = f"file:///{path_with_forward_slashes}"
            webbrowser.open(file_url)

        except Exception as e:
            QMessageBox.warning(self, "打开失败", f"打开购买页面失败: {str(e)}")



    def closeEvent(self, event):
        """关闭事件处理"""
        if not self.login_success:
            reply = QMessageBox.question(self, "确认退出",
                                        "您尚未完成验证，确定要退出吗？",
                                        QMessageBox.Yes | QMessageBox.No)
            if reply == QMessageBox.Yes:
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()

    def load_saved_card(self):
        """加载保存的卡密信息"""
        config = load_config()
        if config:
            # 优先使用card字段，兼容旧版本的card_number字段
            card_number = config.get('card') or config.get('card_number', '')
            if card_number:
                self.card_edit.setText(card_number)
                print(f"自动填充卡密: {card_number}")

            # 默认记住登录状态
            remember_login = config.get('remember_login')
            if remember_login is not None:
                self.remember_checkbox.setChecked(remember_login)
                print(f"设置记住登录状态为: {remember_login}")

    def on_interface_combo_changed(self, index):
        """接口下拉菜单改变时的处理"""
        try:
            interface_data = self.interface_combo.itemData(index)
            if interface_data:
                api_manager.switch_interface(interface_data)
                print(f"用户选择接口: {interface_data}")

                # 更新状态显示
                self.update_interface_status_display()

        except Exception as e:
            print(f"切换接口失败: {e}")

    def on_auto_switch_changed(self, checked):
        """自动切换选项改变时的处理"""
        try:
            api_manager.auto_switch = checked
            api_manager.save_interface_config()
            print(f"自动切换设置为: {checked}")

            # 更新状态显示
            self.update_interface_status_display()

        except Exception as e:
            print(f"设置自动切换失败: {e}")

    def update_interface_status_display(self):
        """更新接口状态显示 - 极简显示"""
        try:
            current_interface = api_manager.current_interface
            current_name = "1" if current_interface == 'interface1' else "2"
            auto_switch_text = "✓" if api_manager.auto_switch else "✗"

            # 获取接口状态
            interface1_status = api_manager.interface_status['interface1']
            interface2_status = api_manager.interface_status['interface2']

            # 使用极简的状态显示
            interface1_icon = "●" if interface1_status['available'] else "○"
            interface2_icon = "●" if interface2_status['available'] else "○"

            # 超紧凑显示
            status_text = f"当前:{current_name} 自动:{auto_switch_text} 状态:{interface1_icon}{interface2_icon}"
            self.interface_status_label.setText(status_text)

        except Exception:
            self.interface_status_label.setText("状态异常")

    def copy_machine_code(self):
        """复制机器码到剪贴板"""
        try:
            # 使用系统剪贴板 - 最简单的方法
            import subprocess

            # 在Windows上使用clip命令
            if os.name == 'nt':
                # 创建一个临时进程将文本发送到剪贴板
                process = subprocess.Popen('clip', shell=True, stdin=subprocess.PIPE)
                process.communicate(input=self.real_machine_code.encode('utf-8'))
                success = process.returncode == 0
            # 在macOS上使用pbcopy
            elif os.name == 'posix' and 'darwin' in sys.platform:
                process = subprocess.Popen('pbcopy', shell=True, stdin=subprocess.PIPE)
                process.communicate(input=self.real_machine_code.encode('utf-8'))
                success = process.returncode == 0
            # 在Linux上使用xclip或xsel
            elif os.name == 'posix':
                try:
                    # 尝试xclip
                    process = subprocess.Popen(['xclip', '-selection', 'clipboard'], stdin=subprocess.PIPE)
                    process.communicate(input=self.real_machine_code.encode('utf-8'))
                    success = process.returncode == 0
                except FileNotFoundError:
                    try:
                        # 尝试xsel
                        process = subprocess.Popen(['xsel', '--clipboard', '--input'], stdin=subprocess.PIPE)
                        process.communicate(input=self.real_machine_code.encode('utf-8'))
                        success = process.returncode == 0
                    except FileNotFoundError:
                        success = False
            else:
                success = False

            if success:
                # 显示提示
                self.status_label.setText("账号已复制到剪贴板")
                # 2秒后恢复状态提示
                QTimer.singleShot(2000, lambda: self.status_label.setText("请输入卡密进行登录"))
            else:
                self.status_label.setText("复制失败，请手动复制")

        except Exception as e:
            print(f"复制机器码时出错: {str(e)}")
            self.status_label.setText("复制失败，请手动复制")














class KamiManager:
    """卡密管理器"""

    def __init__(self):
        """初始化卡密管理器"""
        self.signals = SignalHub()



    def clear_config(self):
        """清除卡密配置，用于调试"""
        try:
            if os.path.exists(CONFIG_FILE):
                os.remove(CONFIG_FILE)
                print(f"已删除卡密配置文件: {CONFIG_FILE}")
                return True
            else:
                print(f"卡密配置文件不存在: {CONFIG_FILE}")
                return False
        except Exception as e:
            print(f"删除配置文件失败: {str(e)}")
            return False

    def show_login_dialog(self, auto_login=False):
        """显示登录对话框

        Args:
            auto_login: 是否自动登录（如果已经有有效卡密）
        """
        dialog = LoginDialog()

        # 如果已经验证成功并启用自动登录，自动点击登录按钮
        if auto_login and dialog.card_edit.text().strip():
            print("检测到有效卡密并启用自动登录，自动点击登录按钮")
            QTimer.singleShot(500, dialog.verify_login)

        result = dialog.exec_()

        if result == QDialog.Accepted and dialog.login_success:
            card_number = dialog.card_edit.text().strip()
            config = load_config()
            expiry_time = config.get('expiry_time', '未知')
            return True, card_number, expiry_time
        else:
            return False, "", "网络连接异常"

    def check_license(self):
        """检查卡密是否有效"""
        # 首先执行防破解检测
        print("\n===== 开始防破解检测 =====")
        detector = AntiCrackDetector()
        detection_passed, detection_results = detector.perform_full_check()

        if not detection_passed:
            print("防破解检测失败:")
            for result in detection_results:
                print(f"  - {result}")

            # 如果配置为检测到后立即退出
            if ANTI_CRACK_CONFIG['exit_on_detect']:
                error_msg = "安全检测失败，程序将退出。\n检测到以下问题:\n" + "\n".join(detection_results[:3])
                if len(detection_results) > 3:
                    error_msg += f"\n... 还有 {len(detection_results) - 3} 个问题"

                # 显示错误信息后退出
                try:
                    from PyQt5.QtWidgets import QMessageBox, QApplication
                    app = QApplication.instance()
                    if app:
                        QMessageBox.critical(None, "安全检测失败", error_msg)
                        app.processEvents()
                except Exception:
                    pass

                # 强制退出
                os._exit(1)

            print("===== 防破解检测失败 =====\n")
            return False, "安全检测失败，程序无法继续运行"

        print("防破解检测通过")
        print("===== 防破解检测完成 =====\n")

        # 先尝试从配置中加载卡密信息并验证
        print("\n===== 开始检查卡密许可 =====")
        config = load_config()

        # 打印配置信息进行调试
        print(f"加载的配置信息: {config}")

        # 如果配置为空或没有卡密信息
        if not config or not config.get('card'):
            print("配置为空或没有卡密信息，需要显示登录界面")
            login_result = self.show_login_dialog(auto_login=False)
            # 如果是三元组，只返回第一个和第三个元素
            if len(login_result) == 3:
                print(f"登录结果: {login_result[0]}, 到期时间: {login_result[2]}")
                print("===== 卡密检查完成 =====\n")
                return login_result[0], login_result[2]
            print(f"登录结果: {login_result}")
            print("===== 卡密检查完成 =====\n")
            return login_result

        # 检查是否启用记住登录
        remember_login = config.get('remember_login')
        print(f"记住登录状态: {remember_login}")

        # 默认为True，确保向后兼容
        if remember_login is None:
            remember_login = True
            config['remember_login'] = True
            save_config(config)
            print("设置默认记住登录状态为True")

        if remember_login:
            # 尝试验证现有卡密
            try:
                card = config.get('card')
                print(f"尝试自动验证已保存的卡密: {card}")
                valid, result = self.silent_verify_license(card)

                if valid:
                    print(f"自动验证成功，到期时间: {result}，将自动进入主界面")
                    print("===== 卡密检查完成 =====\n")
                    return True, result
                else:
                    print(f"自动验证失败: {result}，需要显示登录界面")
                    # 传递auto_login=True参数，让它自动填充卡密并点击登录
                    login_result = self.show_login_dialog(auto_login=True)
                    if len(login_result) == 3:
                        print(f"登录结果: {login_result[0]}, 到期时间: {login_result[2]}")
                        print("===== 卡密检查完成 =====\n")
                        return login_result[0], login_result[2]
                    print(f"登录结果: {login_result}")
                    print("===== 卡密检查完成 =====\n")
                    return login_result
            except Exception as e:
                print(f"自动验证过程发生错误: {str(e)}")
                import traceback
                print(traceback.format_exc())
                login_result = self.show_login_dialog(auto_login=True)
                if len(login_result) == 3:
                    print(f"登录结果: {login_result[0]}, 到期时间: {login_result[2]}")
                    print("===== 卡密检查完成 =====\n")
                    return login_result[0], login_result[2]
                print(f"登录结果: {login_result}")
                print("===== 卡密检查完成 =====\n")
                return login_result
        else:
            print("未启用记住登录功能，需要显示登录界面")
            login_result = self.show_login_dialog(auto_login=False)
            if len(login_result) == 3:
                print(f"登录结果: {login_result[0]}, 到期时间: {login_result[2]}")
                print("===== 卡密检查完成 =====\n")
                return login_result[0], login_result[2]
            print(f"登录结果: {login_result}")
            print("===== 卡密检查完成 =====\n")
            return login_result

    def silent_verify_license(self, card_number):
        """静默验证卡密，无需显示界面"""
        try:
            # 调试信息
            print(f"\n===== 开始静默验证卡密 =====")
            print(f"卡密: {card_number}")

            # 检查网络连接
            if not check_network_connection():
                print(f"静默验证失败: 网络连接异常")
                return False, "网络连接异常，无法验证卡密"

            print(f"网络连接正常")

            # 获取机器码
            machine_code = get_machine_code()
            print(f"机器码: {machine_code}")

            # 获取token
            print(f"正在获取token...")
            token = login(card_number, machine_code)
            if not token:
                print(f"静默验证失败: 无法获取token")
                return False, "卡密验证失败，请检查网络连接或卡密是否正确"

            print(f"获取token成功: {token[:5]}***")

            # 检查到期时间
            print(f"正在检查到期时间...")
            try:
                result = check_card_expiry(card_number)
                # 确保返回值是一个元组，并且有两个元素
                if result is None:
                    print(f"静默验证失败: 服务器未返回有效数据")
                    return False, "检查到期时间失败: 服务器未返回有效数据"

                if not isinstance(result, tuple) or len(result) < 2:
                    print(f"静默验证失败: 返回格式错误 {result}")
                    return False, f"检查到期时间失败: 返回格式错误 {result}"

                valid, expiry_info = result
                if not valid:
                    print(f"静默验证失败: {expiry_info}")
                    return False, expiry_info
            except Exception as e:
                print(f"检查到期时间失败: {str(e)}")
                return False, f"检查到期时间失败: {str(e)}"

            print(f"到期时间验证成功: {expiry_info}")

            # 更新配置中的到期时间
            config = load_config()
            config["expiry_time"] = expiry_info
            # 确保remember_login设置为True
            config["remember_login"] = True

            print(f"正在保存配置...")
            save_result = save_config(config)
            print(f"配置保存结果: {'成功' if save_result else '失败'}")

            print(f"静默验证成功，到期时间: {expiry_info}")
            print(f"===== 静默验证完成 =====\n")

            return True, expiry_info
        except Exception as e:
            print(f"静默验证异常: {str(e)}")
            import traceback
            print(traceback.format_exc())
            return False, f"验证过程发生错误: {str(e)}"

    def get_expiry_info(self):
        """获取到期信息"""
        config = load_config()
        if config and 'expiry_time' in config:
            return config['expiry_time']
        return "未知"

    def get_machine_code(self):
        """获取机器码"""
        return get_machine_code()

    def check_for_updates(self):
        """检查更新并处理更新流程

        Returns:
            tuple: (是否成功, 版本信息或错误消息)
        """
        try:
            print("\n===== 开始检查软件更新 =====")
            print(f"当前软件版本: {APP_VERSION}")
            try:
                result = get_server_update_info()
                # 确保返回值是一个元组，并且有两个元素
                if result is None:
                    success = False
                    server_version = "获取更新信息失败: 服务器未返回有效数据"
                elif not isinstance(result, tuple) or len(result) < 2:
                    success = False
                    server_version = f"获取更新信息失败: 返回格式错误 {result}"
                else:
                    success, server_version = result
            except Exception as e:
                success = False
                server_version = f"获取更新信息失败: {str(e)}"

            if not success:
                print(f"检查更新失败: {server_version}")
                return False, f"检查更新失败: {server_version}"

            print(f"当前版本: {APP_VERSION}, 服务器版本: {server_version}")

            # 简单比较：直接比较字符串是否相同
            if server_version != APP_VERSION:
                print(f"发现新版本: {server_version}")

                # 直接返回结果，让主窗口处理更新流程
                return True, server_version
            else:
                print("当前已是最新版本")
                QMessageBox.information(None, "检查更新", "当前已是最新版本。")
                return True, "当前已是最新版本"
        except Exception as e:
            print(f"检查更新过程中出错: {str(e)}")
            return False, f"检查更新过程中出错: {str(e)}"
        finally:
            print("===== 检查更新完成 =====\n")

    def get_download_url(self, version=None):
        """获取更新下载地址

        Args:
            version: 版本号参数（在此API中实际未使用，但保留参数以兼容现有调用）
        """
        try:
            print("\n===== 开始获取下载地址 =====")

            # 构建正确的请求参数，只使用Softid
            post_data = {
                "Softid": SOFT_KEY
            }

            print(f"请求下载API: {API_DOWNLOAD}")
            print(f"请求参数: Softid={SOFT_KEY}")

            # 发送请求
            headers = {
                "Content-Type": "application/x-www-form-urlencoded",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            }

            # 只尝试一次请求，不重试
            max_retries = 1
            for retry in range(max_retries):
                try:
                    print(f"尝试获取下载地址 (尝试 {retry+1}/{max_retries})...")

                    # 使用不同的请求方法
                    if retry == 0:
                        # 标准POST请求
                        response = requests.post(API_DOWNLOAD, data=post_data, headers=headers, timeout=15)
                    elif retry == 1:
                        # 使用会话对象
                        session = requests.Session()
                        response = session.post(API_DOWNLOAD, data=post_data, headers=headers, timeout=15)
                    else:
                        # 使用GET请求
                        get_url = f"{API_DOWNLOAD}&Softid={SOFT_KEY}"
                        response = requests.get(get_url, headers=headers, timeout=15)

                    response.raise_for_status()

                    # 如果成功，跳出循环
                    break
                except Exception as req_error:
                    print(f"请求失败 (尝试 {retry+1}): {str(req_error)}")
                    if retry == max_retries - 1:
                        # 最后一次尝试也失败，抛出异常
                        raise req_error

            # 解析返回数据
            download_url = response.text.strip()
            print(f"API返回值: {download_url[:30]}{'...' if len(download_url) > 30 else ''}")

            # 检查是否是错误码
            if download_url.startswith("-"):
                print(f"API返回错误码: {download_url}")
                return None

            # 如果返回的不是URL，尝试使用备用下载地址
            if not download_url.startswith("http"):
                print("返回值不是有效的URL，尝试使用备用下载地址")
                # 备用下载地址 - 可以根据需要修改
                backup_url = "https://www.lanzoux.com/b0cq4xvyh"
                print(f"使用备用下载地址: {backup_url}")
                return backup_url

            # 检查URL是否有效
            if download_url.startswith("http"):
                # 处理URL中的中文字符
                try:
                    # 检查URL是否包含非ASCII字符
                    if any(ord(c) > 127 for c in download_url):
                        print("URL包含中文或其他非ASCII字符，进行处理...")

                        # 解析URL
                        from urllib.parse import urlparse, quote, unquote, urlunparse

                        # 解析URL各部分
                        parsed_url = urlparse(download_url)

                        # 重新编码路径部分
                        path = parsed_url.path
                        # 先解码以避免重复编码
                        try:
                            path = unquote(path)
                        except Exception:
                            pass
                        # 编码路径中的非ASCII字符
                        path = quote(path, safe='/:')

                        # 重新编码查询参数
                        query = parsed_url.query
                        if query:
                            try:
                                query = unquote(query)
                            except Exception:
                                pass
                            query = quote(query, safe='=&')

                        # 重新组合URL
                        encoded_url = urlunparse((
                            parsed_url.scheme,
                            parsed_url.netloc,
                            path,
                            parsed_url.params,
                            query,
                            parsed_url.fragment
                        ))

                        print(f"原始URL: {download_url}")
                        print(f"编码后URL: {encoded_url}")
                        download_url = encoded_url
                except Exception as e:
                    print(f"URL编码处理失败: {str(e)}，将使用原始URL")

                # 测试URL是否可访问
                try:
                    print(f"测试下载URL是否可访问: {download_url}")
                    test_response = requests.head(download_url, timeout=10)
                    test_response.raise_for_status()
                    print(f"URL测试成功，状态码: {test_response.status_code}")
                except Exception as test_error:
                    print(f"URL测试失败: {str(test_error)}")
                    # 如果测试失败，但仍然返回URL，让浏览器下载选项来处理

                print("获取到有效的下载地址")
                print("===== 下载地址获取成功 =====\n")
                return download_url
            else:
                print(f"返回值不是有效的URL: {download_url}")
                if download_url.startswith("-"):
                    print(f"返回了错误码: {download_url}")
                print("===== 下载地址获取失败 =====\n")
                return None
        except Exception as e:
            print(f"获取下载地址失败: {str(e)}")
            import traceback
            print(traceback.format_exc())
            print("===== 下载地址获取失败 =====\n")
            return None

    def diagnose_login_issues(self):
        """诊断登录问题，返回可能的解决方案"""
        issues = []
        solutions = []

        # 检查配置文件是否存在
        if not os.path.exists(CONFIG_FILE):
            issues.append("卡密配置文件不存在")
            solutions.append("请重新登录验证卡密")
        else:
            # 检查配置文件是否可读
            try:
                config = load_config()
                if not config:
                    issues.append("卡密配置为空或无法解析")
                    solutions.append("配置文件可能已损坏，请重新登录验证卡密")
                else:
                    # 检查卡密信息
                    if not config.get('card'):
                        issues.append("卡密信息缺失")
                        solutions.append("请重新登录验证卡密")

                    # 检查记住登录状态
                    if not config.get('remember_login', True):
                        issues.append("记住登录状态已禁用")
                        solutions.append("请在登录时勾选'记住登录状态'选项")

                    # 检查卡密是否过期
                    try:
                        valid, result = check_expiry()
                        if not valid:
                            issues.append(f"卡密验证失败: {result}")
                            solutions.append("卡密可能已过期，请重新购买或续费")
                    except Exception:
                        issues.append("无法验证卡密有效性")
                        solutions.append("请检查网络连接是否正常")
            except Exception as e:
                issues.append(f"读取配置文件时出错: {str(e)}")
                solutions.append("配置文件可能已损坏，请删除配置文件后重新登录")

        # 检查权限问题
        try:
            test_file = os.path.join(os.path.dirname(CONFIG_FILE), "test_write.tmp")
            with open(test_file, 'w') as f:
                f.write("test")
            os.remove(test_file)
        except Exception as e:
            issues.append(f"目录写入权限不足: {str(e)}")
            solutions.append("请以管理员身份运行程序或将程序移至有写入权限的目录")

        # 检查网络连接
        if not check_network_connection():
            issues.append("网络连接异常")
            solutions.append("请检查您的网络连接是否正常")

        # 如果没有发现问题但仍然弹出登录框
        if not issues:
            issues.append("未发现明显问题，但仍弹出登录框")
            solutions.append("尝试方案: 1. 删除配置文件后重新登录 2. 使用管理员权限运行 3. 检查防火墙设置")

        return issues, solutions

    def download_update(self, download_url, version):
        """下载更新

        Args:
            download_url: 下载地址
            version: 新版本号

        Returns:
            下载文件路径
        """
        # 此函数已被整合到download_thread_func中，保留空实现以兼容可能的旧代码调用
        print("警告: 使用了旧版下载函数，此函数已被弃用")
        return None

    def install_update(self, file_path):
        """安装更新

        Args:
            file_path: 更新文件路径
        """
        try:
            # 打开目录
            import os
            current_dir = os.path.dirname(os.path.abspath(file_path))

            # 创建自定义对话框，提供更明确的步骤指导
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QLabel, QPushButton, QHBoxLayout

            # 创建对话框
            dialog = QDialog(None)
            dialog.setWindowTitle("更新已下载完成")
            dialog.setMinimumWidth(500)

            # 创建布局
            main_layout = QVBoxLayout()

            # 标题
            title_label = QLabel(f"<h2>发现新版本：头条自媒体自动存稿工具 v{os.path.basename(file_path).split('_v')[1].replace('.exe', '')}</h2>")
            title_label.setStyleSheet("font-weight: bold;")
            main_layout.addWidget(title_label)

            # 说明文本
            info_text = f"""
            <p>新版本已下载到当前目录：</p>
            <p style="color: blue;">{file_path}</p>
            <p>请按照以下步骤完成更新：</p>
            <ol>
                <li>关闭当前软件</li>
                <li>删除原版本软件</li>
                <li>运行新下载的软件</li>
            </ol>
            """
            info_label = QLabel()
            # 使用HTML标签直接设置富文本，避免使用Qt.RichText
            # 将info_text包装在HTML标签中
            info_label.setText(f"<html>{info_text}</html>")
            info_label.setWordWrap(True)
            main_layout.addWidget(info_label)

            # 按钮布局
            button_layout = QHBoxLayout()

            # 打开下载目录按钮
            open_dir_btn = QPushButton("打开下载目录")
            open_dir_btn.setMinimumHeight(40)

            def open_download_dir():
                # 打开文件资源管理器并选中文件
                import subprocess
                if os.name == 'nt':  # Windows
                    subprocess.Popen(f'explorer /select,"{file_path}"')
                elif os.name == 'posix':  # Linux/Mac
                    subprocess.Popen(['xdg-open', os.path.dirname(file_path)])
                dialog.accept()

            open_dir_btn.clicked.connect(open_download_dir)
            button_layout.addWidget(open_dir_btn)

            # 稍后更新按钮
            later_btn = QPushButton("稍后更新")
            later_btn.setMinimumHeight(40)
            later_btn.clicked.connect(dialog.reject)
            button_layout.addWidget(later_btn)

            # 添加按钮布局
            main_layout.addLayout(button_layout)

            # 设置对话框布局
            dialog.setLayout(main_layout)

            # 显示对话框
            result = dialog.exec_()

            # 如果用户选择打开下载目录，则打开目录后退出软件
            if result == QDialog.Accepted:
                # 在这里不需要额外代码，因为open_download_dir函数已经会打开文件资源管理器
                # 提示用户软件将退出
                QMessageBox.information(None, "软件将退出", "请运行新下载的软件版本。\n当前程序即将关闭。")
                os._exit(0)

            return True
        except Exception as e:
            QMessageBox.warning(None, "更新处理失败", f"处理更新过程中出错: {str(e)}\n\n您仍可以手动打开位置：\n{file_path}")
            return False


def force_login_dialog(reset_config=False, auto_login=True):
    """强制显示登录对话框，用于调试

    Args:
        reset_config: 是否重置配置文件
        auto_login: 是否自动登录
    """
    if reset_config:
        try:
            if os.path.exists(CONFIG_FILE):
                os.remove(CONFIG_FILE)
                print(f"已删除卡密配置文件: {CONFIG_FILE}")
        except Exception as e:
            print(f"删除配置文件失败: {str(e)}")

    app = QApplication(sys.argv) if not QApplication.instance() else QApplication.instance()
    dialog = LoginDialog()

    # 如果启用自动登录，尝试自动点击登录按钮
    if auto_login and dialog.card_edit.text().strip():
        print("启用自动登录，延迟500ms后自动点击登录按钮")
        QTimer.singleShot(500, dialog.verify_login)

    dialog.exec_()
    return dialog.login_success


# 在模块初始化时检查环境
def debug_environment():
    """打印环境调试信息，帮助排查路径和权限问题"""
    print("\n=== 卡密模块环境调试信息 ===")
    print(f"当前工作目录: {os.getcwd()}")
    print(f"脚本所在目录: {SCRIPT_DIR}")
    print(f"卡密配置文件: {CONFIG_FILE}")
    print(f"系统平台: {platform.platform()}")
    print(f"Python版本: {sys.version}")

    # 检查磁盘权限
    try:
        # 获取配置文件目录
        config_dir = os.path.dirname(CONFIG_FILE)
        if not os.path.exists(config_dir) and config_dir:
            try:
                os.makedirs(config_dir)
                print(f"成功创建配置目录: {config_dir}")
            except Exception as e:
                print(f"无法创建配置目录: {str(e)}")
        else:
            print(f"配置目录已存在: {config_dir}")

        # 尝试创建测试文件
        try:
            test_path = os.path.join(os.path.dirname(CONFIG_FILE), "test_write.tmp")
            with open(test_path, 'w') as f:
                f.write("test")
            print(f"成功在配置目录创建测试文件: {test_path}")
            os.remove(test_path)
            print(f"成功删除测试文件")
        except Exception as e:
            print(f"无法在配置目录写入文件: {str(e)}")
    except Exception as e:
        print(f"检查磁盘权限时出错: {str(e)}")

    # 检查配置文件状态
    if os.path.exists(CONFIG_FILE):
        try:
            config = load_config()
            print(f"卡密配置内容: {config}")
            if config and 'card' in config:
                print(f"当前卡密: {config.get('card')}")
                print(f"记住登录状态: {config.get('remember_login', '未设置')}")
                print(f"到期时间: {config.get('expiry_time', '未知')}")
            else:
                print("卡密配置为空或无效")
        except Exception as e:
            print(f"读取配置文件失败: {str(e)}")

    print("=== 调试信息结束 ===\n")

# 在模块加载时执行调试
debug_environment()


# 测试代码
if __name__ == "__main__":
    app = QApplication(sys.argv)

    # 创建卡密管理器
    kami_manager = KamiManager()

    # 显示登录对话框
    success, card, expiry = kami_manager.show_login_dialog()

    if success:
        print(f"登录成功: 卡密={card}, 到期时间={expiry}")
    else:
        print("登录失败")

    sys.exit(app.exec_())

def get_server_update_info(max_retries=3):
    """获取服务器更新信息，添加重试机制

    Args:
        max_retries: 最大重试次数

    返回:
        tuple: (是否成功, 服务器版本号或错误信息)

    这个函数分离了服务器通信与版本比较逻辑，
    使用服务器版本API获取版本信息，但用于与APP_VERSION比较
    """
    import time

    for retry in range(max_retries):
        try:
            # 获取登录token
            config = load_config()
            if not config.get("card") or not config.get("machine_code"):
                return False, "未找到有效的卡密信息"

            token = login(config["card"], config["machine_code"])
            if not token:
                # 如果不是最后一次重试，则继续重试
                if retry < max_retries - 1:
                    print(f"获取token失败，正在重试 ({retry+1}/{max_retries})...")
                    time.sleep(1)  # 等待1秒后重试
                    continue
                return False, "获取token失败"

            post_data = {
                "Softid": SOFT_KEY,  # 软件标识
                "UserName": config.get("card", ""),  # 使用卡密作为用户名
                "Token": token,  # 使用登录返回的token
                "Version": VERSION  # 当前服务器版本号标识
            }

            # 打印请求参数
            print(f"版本检查参数: {post_data}")

            # 使用通用API请求函数
            success, result, interface_used = make_api_request('version', post_data, timeout=15, max_retries=1)

            if success:
                print(f"API返回值: {result}")  # 打印API返回值

                # 检查错误码
                if result.startswith("-"):
                    error_code = result
                    # 如果不是最后一次重试，则继续重试
                    if retry < max_retries - 1:
                        print(f"获取版本号失败: {error_code}，正在重试 ({retry+1}/{max_retries})...")
                        time.sleep(1)  # 等待1秒后重试
                        continue
                    return False, f"获取版本号失败: {error_code}"

                if interface_used:
                    print(f"版本检查成功，使用接口: {API_INTERFACES[interface_used]['name']}")
                return True, result
            else:
                # 如果不是最后一次重试，则继续重试
                if retry < max_retries - 1:
                    print(f"版本检查失败: {result}，正在重试 ({retry+1}/{max_retries})...")
                    time.sleep(1)  # 等待1秒后重试
                    continue
                return False, f"版本检查失败: {result}"

        except requests.RequestException as e:
            # 如果不是最后一次重试，则继续重试
            if retry < max_retries - 1:
                print(f"请求版本信息失败: {str(e)}，正在重试 ({retry+1}/{max_retries})...")
                time.sleep(1)  # 等待1秒后重试
                continue
            return False, f"获取版本号失败: {str(e)}"
        except Exception as e:
            # 如果不是最后一次重试，则继续重试
            if retry < max_retries - 1:
                print(f"检查版本时发生错误: {str(e)}，正在重试 ({retry+1}/{max_retries})...")
                time.sleep(1)  # 等待1秒后重试
                continue
            return False, f"检查版本时发生错误: {str(e)}"


def get_server_update_info_async(callback, max_retries=3):
    """异步获取服务器更新信息

    Args:
        callback: 回调函数，接收(success, version_or_error)参数
        max_retries: 最大重试次数
    """
    import threading

    def _thread_func():
        try:
            # 使用带重试机制的函数
            result = get_server_update_info(max_retries=max_retries)
            # 在主线程中执行回调
            try:
                # 使用导入的QApplication而不是QApplication.instance()
                from PyQt5.QtWidgets import QApplication
                QApplication.processEvents()
            except Exception as e:
                print(f"处理事件失败: {str(e)}")

            # 确保result不为None
            if result is not None and isinstance(result, tuple) and len(result) >= 2:
                callback(result[0], result[1])
            else:
                callback(False, "获取更新信息失败")
        except Exception as e:
            print(f"异步获取更新信息时出错: {str(e)}")
            callback(False, f"异步获取更新信息时出错: {str(e)}")

    # 创建并启动线程
    thread = threading.Thread(target=_thread_func)
    thread.daemon = True
    thread.start()

    return thread


def initialize_anti_crack_protection():
    """初始化防破解保护"""
    try:
        print("\n===== 初始化防破解保护 =====")

        # 创建防破解检测器
        detector = AntiCrackDetector()

        # 执行全面检测
        detection_passed, detection_results = detector.perform_full_check()

        if not detection_passed:
            print("防破解检测失败:")
            for result in detection_results:
                print(f"  - {result}")

            # 如果配置为检测到后立即退出
            if ANTI_CRACK_CONFIG['exit_on_detect']:
                error_msg = "安全检测失败，程序将退出。\n\n检测到以下安全问题:\n" + "\n".join(detection_results[:5])
                if len(detection_results) > 5:
                    error_msg += f"\n... 还有 {len(detection_results) - 5} 个问题"

                error_msg += "\n\n请在安全环境中运行本程序。"

                # 显示错误信息
                try:
                    from PyQt5.QtWidgets import QMessageBox, QApplication
                    app = QApplication.instance()
                    if not app:
                        app = QApplication([])

                    msg_box = QMessageBox()
                    msg_box.setIcon(QMessageBox.Critical)
                    msg_box.setWindowTitle("安全检测失败")
                    msg_box.setText(error_msg)
                    msg_box.setStandardButtons(QMessageBox.Ok)
                    msg_box.exec_()

                    if app:
                        app.processEvents()
                except Exception:
                    print(error_msg)

                print("===== 防破解保护初始化失败，程序退出 =====\n")
                # 强制退出
                os._exit(1)

            print("===== 防破解保护初始化失败 =====\n")
            return False, detection_results

        print("防破解检测通过，程序可以安全运行")
        print("===== 防破解保护初始化完成 =====\n")
        return True, []

    except Exception as e:
        print(f"初始化防破解保护时发生错误: {str(e)}")
        print("===== 防破解保护初始化异常 =====\n")
        return False, [f"初始化错误: {str(e)}"]


def start_background_protection():
    """启动后台保护监控"""
    def protection_monitor():
        """后台保护监控线程"""
        while True:
            try:
                time.sleep(30)  # 每30秒检测一次

                detector = AntiCrackDetector()
                detection_passed, detection_results = detector.perform_full_check()

                if not detection_passed:
                    print("后台检测发现安全威胁:")
                    for result in detection_results:
                        print(f"  - {result}")

                    if ANTI_CRACK_CONFIG['exit_on_detect']:
                        print("检测到安全威胁，程序将退出")
                        os._exit(1)

            except Exception as e:
                print(f"后台保护监控异常: {str(e)}")
                time.sleep(60)  # 异常时等待更长时间

    # 启动后台监控线程
    monitor_thread = threading.Thread(target=protection_monitor, daemon=True)
    monitor_thread.start()
    print("后台保护监控已启动")


def create_vmp_protection():
    """创建VMP保护配置"""
    vmp_config = {
        "protection_level": "强化型",
        "anti_debug": True,
        "anti_vm": True,
        "anti_dump": True,
        "code_virtualization": True,
        "import_protection": True,
        "resource_protection": True,
        "string_encryption": True,
        "control_flow_obfuscation": True,
        "api_redirection": True,
        "memory_protection": True,
        "integrity_check": True,
        "license_check": True,
        "system_drive_only": True,
        "blacklist_check": True
    }

    print("VMP保护配置:")
    for key, value in vmp_config.items():
        print(f"  {key}: {value}")

    return vmp_config


def apply_runtime_protection():
    """应用运行时保护"""
    try:
        # 检测调试器
        if ctypes.windll.kernel32.IsDebuggerPresent():
            print("检测到调试器，程序退出")
            os._exit(1)

        # 检测虚拟机
        detector = AntiCrackDetector()
        if detector.check_virtual_machine():
            print("检测到虚拟机环境，程序退出")
            os._exit(1)

        # 检测危险工具
        if detector.check_debugging_tools():
            print("检测到危险调试工具，程序退出")
            os._exit(1)

        # 检测系统盘
        if not detector.check_system_drive():
            print("程序必须在系统盘运行")
            os._exit(1)

        print("运行时保护检查通过")
        return True

    except Exception as e:
        print(f"运行时保护检查异常: {str(e)}")
        return False