#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
压力测试脚本 - 测试修复后的加载按钮在极端情况下的稳定性
"""

import sys
import os
import time
import threading
import logging
from concurrent.futures import ThreadPoolExecutor

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

class StressTestAccountTab:
    """模拟账号标签页进行压力测试"""
    
    def __init__(self):
        self.is_loading = False
        self.background_loading_active = False
        self._button_click_in_progress = False
        self.account_loader = None
        self.progress_dialog = None
        
        # 统计数据
        self.total_clicks = 0
        self.successful_clicks = 0
        self.ignored_clicks = 0
        self.error_clicks = 0
        
        # 线程锁
        self._lock = threading.Lock()
        
    def on_load_account_clicked(self):
        """模拟按钮点击处理 - 线程安全版本"""
        with self._lock:
            self.total_clicks += 1

            # 检查是否正在处理
            if hasattr(self, '_button_click_in_progress') and self._button_click_in_progress:
                self.ignored_clicks += 1
                return False

            # 设置处理标志
            self._button_click_in_progress = True

            # 检查加载状态
            if self.is_loading:
                self.ignored_clicks += 1
                self._button_click_in_progress = False
                return False

            if self.background_loading_active:
                self.ignored_clicks += 1
                self._button_click_in_progress = False
                return False

        # 在锁外进行耗时操作
        try:
            # 模拟处理时间（随机0-50ms）
            import random
            processing_time = random.uniform(0.001, 0.05)
            time.sleep(processing_time)

            # 模拟随机错误（5%概率）
            if random.random() < 0.05:
                raise Exception("模拟的随机错误")

            with self._lock:
                self.successful_clicks += 1
                self._button_click_in_progress = False
                return True

        except Exception as e:
            with self._lock:
                self.error_clicks += 1
                self._button_click_in_progress = False
                return False
    
    def get_stats(self):
        """获取统计信息"""
        with self._lock:
            return {
                'total_clicks': self.total_clicks,
                'successful_clicks': self.successful_clicks,
                'ignored_clicks': self.ignored_clicks,
                'error_clicks': self.error_clicks,
                'success_rate': self.successful_clicks / max(self.total_clicks, 1) * 100,
                'ignore_rate': self.ignored_clicks / max(self.total_clicks, 1) * 100
            }

def single_thread_rapid_test():
    """单线程快速点击测试"""
    print("🔥 单线程快速点击测试")
    print("-" * 30)
    
    tab = StressTestAccountTab()
    
    # 快速点击1000次
    start_time = time.time()
    for i in range(1000):
        tab.on_load_account_clicked()
        if i % 100 == 0:
            print(f"  进度: {i}/1000")
    
    end_time = time.time()
    stats = tab.get_stats()
    
    print(f"单线程测试结果:")
    print(f"  总点击: {stats['total_clicks']}")
    print(f"  成功: {stats['successful_clicks']}")
    print(f"  忽略: {stats['ignored_clicks']}")
    print(f"  错误: {stats['error_clicks']}")
    print(f"  成功率: {stats['success_rate']:.1f}%")
    print(f"  忽略率: {stats['ignore_rate']:.1f}%")
    print(f"  耗时: {end_time - start_time:.3f}秒")
    
    return stats['success_rate'] > 80  # 成功率大于80%认为通过

def multi_thread_test():
    """多线程并发点击测试"""
    print("\n🚀 多线程并发点击测试")
    print("-" * 30)
    
    tab = StressTestAccountTab()
    
    def worker_thread(thread_id, clicks_per_thread):
        """工作线程函数"""
        for i in range(clicks_per_thread):
            tab.on_load_account_clicked()
            time.sleep(0.001)  # 1ms间隔
    
    # 10个线程，每个线程点击100次
    thread_count = 10
    clicks_per_thread = 100
    
    start_time = time.time()
    
    with ThreadPoolExecutor(max_workers=thread_count) as executor:
        futures = []
        for i in range(thread_count):
            future = executor.submit(worker_thread, i, clicks_per_thread)
            futures.append(future)
        
        # 等待所有线程完成
        for future in futures:
            future.result()
    
    end_time = time.time()
    stats = tab.get_stats()
    
    print(f"多线程测试结果:")
    print(f"  线程数: {thread_count}")
    print(f"  每线程点击: {clicks_per_thread}")
    print(f"  总点击: {stats['total_clicks']}")
    print(f"  成功: {stats['successful_clicks']}")
    print(f"  忽略: {stats['ignored_clicks']}")
    print(f"  错误: {stats['error_clicks']}")
    print(f"  成功率: {stats['success_rate']:.1f}%")
    print(f"  忽略率: {stats['ignore_rate']:.1f}%")
    print(f"  耗时: {end_time - start_time:.3f}秒")
    
    # 多线程环境下，忽略率应该较高（说明防重复机制有效）
    return stats['ignore_rate'] > 50  # 忽略率大于50%认为防重复机制有效

def extreme_rapid_test():
    """极端快速点击测试"""
    print("\n⚡ 极端快速点击测试")
    print("-" * 30)
    
    tab = StressTestAccountTab()
    
    # 无延迟快速点击10000次
    start_time = time.time()
    for i in range(10000):
        tab.on_load_account_clicked()
    
    end_time = time.time()
    stats = tab.get_stats()
    
    print(f"极端快速测试结果:")
    print(f"  总点击: {stats['total_clicks']}")
    print(f"  成功: {stats['successful_clicks']}")
    print(f"  忽略: {stats['ignored_clicks']}")
    print(f"  错误: {stats['error_clicks']}")
    print(f"  成功率: {stats['success_rate']:.1f}%")
    print(f"  忽略率: {stats['ignore_rate']:.1f}%")
    print(f"  耗时: {end_time - start_time:.3f}秒")
    print(f"  平均每次点击: {(end_time - start_time) / stats['total_clicks'] * 1000:.3f}ms")
    
    return True  # 只要不崩溃就算通过

def memory_leak_test():
    """内存泄漏测试"""
    print("\n💾 内存泄漏测试")
    print("-" * 30)
    
    try:
        import psutil
        process = psutil.Process()
        
        initial_memory = process.memory_info().rss / 1024 / 1024
        print(f"初始内存: {initial_memory:.2f} MB")
        
        # 创建多个实例并进行大量操作
        tabs = []
        for i in range(10):
            tab = StressTestAccountTab()
            tabs.append(tab)
            
            # 每个实例进行1000次点击
            for j in range(1000):
                tab.on_load_account_clicked()
        
        mid_memory = process.memory_info().rss / 1024 / 1024
        print(f"操作后内存: {mid_memory:.2f} MB")
        
        # 清理
        del tabs
        import gc
        gc.collect()
        
        final_memory = process.memory_info().rss / 1024 / 1024
        print(f"清理后内存: {final_memory:.2f} MB")
        
        memory_increase = final_memory - initial_memory
        print(f"内存增长: {memory_increase:.2f} MB")
        
        return memory_increase < 20  # 内存增长小于20MB认为正常
        
    except ImportError:
        print("psutil不可用，跳过内存测试")
        return True

def main():
    """主测试函数"""
    print("🧪 加载按钮压力测试")
    print("=" * 50)
    
    tests = [
        ("单线程快速点击测试", single_thread_rapid_test),
        ("多线程并发点击测试", multi_thread_test),
        ("极端快速点击测试", extreme_rapid_test),
        ("内存泄漏测试", memory_leak_test)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {str(e)}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 50)
    print(f"🏁 压力测试完成: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有压力测试通过！修复非常稳定！")
        return 0
    else:
        print("⚠️  部分测试失败，可能需要进一步优化")
        return 1

if __name__ == "__main__":
    sys.exit(main())
