#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试更多网创项目选项卡
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QTabWidget
from app.components.more_projects_tab import MoreProjectsTab

class TestWindow(QMainWindow):
    """测试窗口"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("测试更多网创项目选项卡 - 紧凑版")
        self.setGeometry(100, 100, 1000, 700)  # 调整为更合适的尺寸

        # 设置窗口样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QTabWidget::pane {
                border: 1px solid #c0c0c0;
                border-radius: 4px;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #e1e1e1;
                border: 1px solid #c0c0c0;
                border-bottom-color: #c0c0c0;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
                min-width: 8ex;
                padding: 10px 15px;
                margin-right: 2px;
                font-size: 13px;
                font-weight: bold;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom-color: white;
                color: #2c3e50;
            }
            QTabBar::tab:hover {
                background-color: #d4edda;
            }
        """)

        # 创建选项卡控件
        tab_widget = QTabWidget()

        # 添加更多网创项目选项卡
        more_projects_tab = MoreProjectsTab()
        tab_widget.addTab(more_projects_tab, "🌟 更多网创项目")

        self.setCentralWidget(tab_widget)

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyle('Fusion')
    
    # 创建测试窗口
    window = TestWindow()
    window.show()
    
    # 运行应用
    sys.exit(app.exec_())

if __name__ == '__main__':
    main()
