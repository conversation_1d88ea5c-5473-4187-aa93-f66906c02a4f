#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
监控窗口主界面 - 实时监控批量存稿工具
"""

import os
import sys
import logging
from datetime import datetime
from PyQt5.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QTextEdit, QLabel, QPushButton, QFrame, QSplitter, QTabWidget,
    QTableWidget, QTableWidgetItem, QProgressBar, QGroupBox,
    QLineEdit, QComboBox, QCheckBox, QSpinBox, QApplication, QMessageBox
)
from PyQt5.QtCore import Qt, QTimer, pyqtSlot
from PyQt5.QtGui import QFont, QColor, QPalette, QIcon

# 尝试导入pyqtgraph，如果失败则使用简单的替代方案
try:
    import pyqtgraph as pg
    PYQTGRAPH_AVAILABLE = True
except ImportError:
    PYQTGRAPH_AVAILABLE = False
    print("警告: pyqtgraph未安装，图表功能将被禁用")

from .real_time_monitor import RealTimeMonitor

class MonitorWindow(QMainWindow):
    """监控主窗口"""
    
    def __init__(self):
        super().__init__()
        self.monitor = RealTimeMonitor()
        self.log_count = {'INFO': 0, 'WARNING': 0, 'ERROR': 0, 'DEBUG': 0}
        self.init_ui()
        self.setup_connections()
        self.setup_timers()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("批量存稿工具实时监控系统")
        self.setGeometry(100, 100, 1400, 900)
        self.setMinimumSize(1200, 800)
        
        # 设置窗口图标
        # self.setWindowIcon(QIcon("app/resources/monitor_icon.png"))
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)
        
        # 创建工具栏
        self.create_toolbar(main_layout)
        
        # 创建状态指示器
        self.create_status_indicators(main_layout)
        
        # 创建主要内容区域
        self.create_main_content(main_layout)
        
        # 创建状态栏
        self.create_status_bar()
        
        # 应用样式
        self.apply_styles()
        
    def create_toolbar(self, parent_layout):
        """创建工具栏"""
        toolbar_frame = QFrame()
        toolbar_frame.setFrameStyle(QFrame.StyledPanel)
        toolbar_frame.setMaximumHeight(60)
        
        toolbar_layout = QHBoxLayout(toolbar_frame)
        
        # 监控控制按钮
        self.start_btn = QPushButton("🚀 开始监控")
        self.start_btn.clicked.connect(self.start_monitoring)
        self.start_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                font-weight: bold;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        
        self.stop_btn = QPushButton("⏹️ 停止监控")
        self.stop_btn.clicked.connect(self.stop_monitoring)
        self.stop_btn.setEnabled(False)
        self.stop_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                font-weight: bold;
                padding: 8px 16px;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
            QPushButton:disabled {
                background-color: #6c757d;
            }
        """)
        
        # 清空日志按钮
        self.clear_logs_btn = QPushButton("🗑️ 清空日志")
        self.clear_logs_btn.clicked.connect(self.clear_logs)
        
        # 导出数据按钮
        self.export_btn = QPushButton("📊 导出数据")
        self.export_btn.clicked.connect(self.export_data)
        
        # 设置按钮
        self.settings_btn = QPushButton("⚙️ 设置")
        self.settings_btn.clicked.connect(self.show_settings)
        
        toolbar_layout.addWidget(self.start_btn)
        toolbar_layout.addWidget(self.stop_btn)
        toolbar_layout.addSpacing(20)
        toolbar_layout.addWidget(self.clear_logs_btn)
        toolbar_layout.addWidget(self.export_btn)
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(self.settings_btn)
        
        parent_layout.addWidget(toolbar_frame)
        
    def create_status_indicators(self, parent_layout):
        """创建状态指示器"""
        status_frame = QFrame()
        status_frame.setFrameStyle(QFrame.StyledPanel)
        status_frame.setMaximumHeight(80)
        
        status_layout = QGridLayout(status_frame)
        
        # 监控状态
        self.monitoring_status = QLabel("⚪ 未监控")
        self.monitoring_status.setStyleSheet("font-weight: bold; font-size: 14px;")
        
        # CPU使用率
        self.cpu_label = QLabel("CPU: 0%")
        self.cpu_progress = QProgressBar()
        self.cpu_progress.setMaximum(100)
        self.cpu_progress.setStyleSheet("""
            QProgressBar {
                border: 1px solid #ccc;
                border-radius: 3px;
                text-align: center;
            }
            QProgressBar::chunk {
                background-color: #007bff;
                border-radius: 3px;
            }
        """)
        
        # 内存使用率
        self.memory_label = QLabel("内存: 0 MB")
        self.memory_progress = QProgressBar()
        self.memory_progress.setMaximum(100)
        self.memory_progress.setStyleSheet("""
            QProgressBar::chunk {
                background-color: #28a745;
            }
        """)
        
        # 线程数量
        self.thread_label = QLabel("线程: 0")
        
        # 日志统计
        self.log_stats_label = QLabel("日志: INFO:0 WARN:0 ERROR:0")
        
        # 布局
        status_layout.addWidget(QLabel("状态:"), 0, 0)
        status_layout.addWidget(self.monitoring_status, 0, 1)
        status_layout.addWidget(self.cpu_label, 0, 2)
        status_layout.addWidget(self.cpu_progress, 0, 3)
        status_layout.addWidget(self.memory_label, 1, 0)
        status_layout.addWidget(self.memory_progress, 1, 1)
        status_layout.addWidget(self.thread_label, 1, 2)
        status_layout.addWidget(self.log_stats_label, 1, 3)
        
        parent_layout.addWidget(status_frame)
        
    def create_main_content(self, parent_layout):
        """创建主要内容区域"""
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        
        # 左侧：日志和图表
        left_widget = self.create_left_panel()
        
        # 右侧：系统信息和统计
        right_widget = self.create_right_panel()
        
        splitter.addWidget(left_widget)
        splitter.addWidget(right_widget)
        splitter.setSizes([800, 400])
        
        parent_layout.addWidget(splitter)
        
    def create_left_panel(self):
        """创建左侧面板"""
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        
        # 创建标签页
        tab_widget = QTabWidget()
        
        # 日志标签页
        log_tab = self.create_log_tab()
        tab_widget.addTab(log_tab, "📋 实时日志")
        
        # 图表标签页
        chart_tab = self.create_chart_tab()
        tab_widget.addTab(chart_tab, "📈 性能图表")
        
        # 异常标签页
        exception_tab = self.create_exception_tab()
        tab_widget.addTab(exception_tab, "⚠️ 异常监控")

        # 用户交互标签页
        interaction_tab = self.create_interaction_tab()
        tab_widget.addTab(interaction_tab, "👆 用户交互")

        # 状态监控标签页
        state_tab = self.create_state_tab()
        tab_widget.addTab(state_tab, "📊 状态监控")
        
        left_layout.addWidget(tab_widget)
        
        return left_widget
        
    def create_log_tab(self):
        """创建日志标签页"""
        log_widget = QWidget()
        log_layout = QVBoxLayout(log_widget)
        
        # 日志过滤器
        filter_frame = QFrame()
        filter_layout = QHBoxLayout(filter_frame)
        
        filter_layout.addWidget(QLabel("过滤:"))
        
        self.log_level_combo = QComboBox()
        self.log_level_combo.addItems(["全部", "INFO", "WARNING", "ERROR", "DEBUG"])
        self.log_level_combo.currentTextChanged.connect(self.filter_logs)
        
        self.log_search_edit = QLineEdit()
        self.log_search_edit.setPlaceholderText("搜索日志内容...")
        self.log_search_edit.textChanged.connect(self.filter_logs)
        
        self.auto_scroll_check = QCheckBox("自动滚动")
        self.auto_scroll_check.setChecked(True)
        
        filter_layout.addWidget(self.log_level_combo)
        filter_layout.addWidget(self.log_search_edit)
        filter_layout.addWidget(self.auto_scroll_check)
        filter_layout.addStretch()
        
        log_layout.addWidget(filter_frame)
        
        # 日志显示区域
        self.log_display = QTextEdit()
        self.log_display.setReadOnly(True)
        self.log_display.setFont(QFont("Consolas", 9))
        self.log_display.setStyleSheet("""
            QTextEdit {
                background-color: #1e1e1e;
                color: #ffffff;
                border: 1px solid #555;
            }
        """)
        
        log_layout.addWidget(self.log_display)
        
        return log_widget
        
    def create_chart_tab(self):
        """创建图表标签页"""
        chart_widget = QWidget()
        chart_layout = QVBoxLayout(chart_widget)

        if PYQTGRAPH_AVAILABLE:
            # 使用pyqtgraph创建性能图表
            self.cpu_chart = pg.PlotWidget(title="CPU使用率 (%)")
            self.cpu_chart.setLabel('left', 'CPU (%)')
            self.cpu_chart.setLabel('bottom', '时间')
            self.cpu_chart.showGrid(x=True, y=True)

            self.memory_chart = pg.PlotWidget(title="内存使用量 (MB)")
            self.memory_chart.setLabel('left', '内存 (MB)')
            self.memory_chart.setLabel('bottom', '时间')
            self.memory_chart.showGrid(x=True, y=True)

            chart_layout.addWidget(self.cpu_chart)
            chart_layout.addWidget(self.memory_chart)
        else:
            # 如果pyqtgraph不可用，显示简单的文本信息
            info_label = QLabel("图表功能需要安装 pyqtgraph 库\n\n请运行: pip install pyqtgraph")
            info_label.setAlignment(Qt.AlignCenter)
            info_label.setStyleSheet("""
                QLabel {
                    background-color: #f8f9fa;
                    border: 2px dashed #dee2e6;
                    border-radius: 8px;
                    padding: 20px;
                    font-size: 14px;
                    color: #6c757d;
                }
            """)
            chart_layout.addWidget(info_label)

            # 创建占位符
            self.cpu_chart = None
            self.memory_chart = None

        # 初始化图表数据
        self.cpu_data = []
        self.memory_data = []
        self.time_data = []

        return chart_widget
        
    def create_exception_tab(self):
        """创建异常监控标签页"""
        exception_widget = QWidget()
        exception_layout = QVBoxLayout(exception_widget)
        
        # 异常统计
        stats_frame = QGroupBox("异常统计")
        stats_layout = QGridLayout(stats_frame)
        
        self.crash_count_label = QLabel("闪退次数: 0")
        self.exception_count_label = QLabel("异常次数: 0")
        self.memory_leak_label = QLabel("内存泄漏: 未检测到")
        
        stats_layout.addWidget(self.crash_count_label, 0, 0)
        stats_layout.addWidget(self.exception_count_label, 0, 1)
        stats_layout.addWidget(self.memory_leak_label, 1, 0, 1, 2)
        
        exception_layout.addWidget(stats_frame)
        
        # 异常详情表格
        self.exception_table = QTableWidget()
        self.exception_table.setColumnCount(4)
        self.exception_table.setHorizontalHeaderLabels(["时间", "类型", "消息", "详情"])
        self.exception_table.horizontalHeader().setStretchLastSection(True)
        
        exception_layout.addWidget(self.exception_table)
        
        return exception_widget

    def create_interaction_tab(self):
        """创建用户交互标签页"""
        interaction_widget = QWidget()
        interaction_layout = QVBoxLayout(interaction_widget)

        # 交互统计
        stats_frame = QGroupBox("交互统计")
        stats_layout = QGridLayout(stats_frame)

        self.button_click_count_label = QLabel("按钮点击: 0")
        self.operation_count_label = QLabel("操作执行: 0")
        self.current_operation_label = QLabel("当前操作: 无")

        stats_layout.addWidget(self.button_click_count_label, 0, 0)
        stats_layout.addWidget(self.operation_count_label, 0, 1)
        stats_layout.addWidget(self.current_operation_label, 1, 0, 1, 2)

        interaction_layout.addWidget(stats_frame)

        # 交互历史表格
        self.interaction_table = QTableWidget()
        self.interaction_table.setColumnCount(5)
        self.interaction_table.setHorizontalHeaderLabels(["时间", "类型", "操作", "窗口", "详情"])
        if hasattr(self.interaction_table, 'horizontalHeader'):
            header = self.interaction_table.horizontalHeader()
            if hasattr(header, 'setStretchLastSection'):
                header.setStretchLastSection(True)

        interaction_layout.addWidget(self.interaction_table)

        return interaction_widget

    def create_state_tab(self):
        """创建状态监控标签页"""
        state_widget = QWidget()
        state_layout = QVBoxLayout(state_widget)

        # 状态概览
        overview_frame = QGroupBox("状态概览")
        overview_layout = QGridLayout(overview_frame)

        self.active_tasks_label = QLabel("活动任务: 0")
        self.thread_count_label = QLabel("活动线程: 0")
        self.browser_status_label = QLabel("浏览器状态: 未知")
        self.network_status_label = QLabel("网络状态: 未知")

        overview_layout.addWidget(self.active_tasks_label, 0, 0)
        overview_layout.addWidget(self.thread_count_label, 0, 1)
        overview_layout.addWidget(self.browser_status_label, 1, 0)
        overview_layout.addWidget(self.network_status_label, 1, 1)

        state_layout.addWidget(overview_frame)

        # 状态变化历史
        self.state_table = QTableWidget()
        self.state_table.setColumnCount(5)
        self.state_table.setHorizontalHeaderLabels(["时间", "模块", "状态", "旧值", "新值"])
        if hasattr(self.state_table, 'horizontalHeader'):
            header = self.state_table.horizontalHeader()
            if hasattr(header, 'setStretchLastSection'):
                header.setStretchLastSection(True)

        state_layout.addWidget(self.state_table)

        return state_widget
        
    def create_right_panel(self):
        """创建右侧面板"""
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        
        # 系统信息
        system_group = QGroupBox("系统信息")
        system_layout = QVBoxLayout(system_group)
        
        self.system_info_label = QLabel("正在获取系统信息...")
        self.system_info_label.setWordWrap(True)
        system_layout.addWidget(self.system_info_label)
        
        right_layout.addWidget(system_group)
        
        # 进程信息
        process_group = QGroupBox("进程信息")
        process_layout = QVBoxLayout(process_group)
        
        self.process_info_label = QLabel("正在获取进程信息...")
        self.process_info_label.setWordWrap(True)
        process_layout.addWidget(self.process_info_label)
        
        right_layout.addWidget(process_group)
        
        # 头条进程状态
        toutiao_group = QGroupBox("头条存稿工具状态")
        toutiao_layout = QVBoxLayout(toutiao_group)

        self.toutiao_status_label = QLabel("🔍 正在搜索头条存稿工具进程...")
        self.toutiao_status_label.setWordWrap(True)
        self.toutiao_status_label.setStyleSheet("""
            QLabel {
                padding: 10px;
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
            }
        """)
        toutiao_layout.addWidget(self.toutiao_status_label)

        right_layout.addWidget(toutiao_group)

        # 任务状态
        task_group = QGroupBox("任务状态")
        task_layout = QVBoxLayout(task_group)

        self.task_status_label = QLabel("暂无任务信息")
        self.task_status_label.setWordWrap(True)
        task_layout.addWidget(self.task_status_label)

        right_layout.addWidget(task_group)
        
        right_layout.addStretch()
        
        return right_widget

    def create_status_bar(self):
        """创建状态栏"""
        status_bar = self.statusBar()

        # 监控时间标签
        self.monitor_time_label = QLabel("监控时间: 00:00:00")
        status_bar.addWidget(self.monitor_time_label)

        # 分隔符
        status_bar.addPermanentWidget(QLabel(" | "))

        # 日志数量标签
        self.log_count_label = QLabel("日志数量: 0")
        status_bar.addPermanentWidget(self.log_count_label)

        # 分隔符
        status_bar.addPermanentWidget(QLabel(" | "))

        # 状态标签
        self.status_label = QLabel("就绪")
        status_bar.addPermanentWidget(self.status_label)

    def apply_styles(self):
        """应用样式"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 3px;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
            QPushButton:pressed {
                background-color: #004085;
            }
            QFrame {
                background-color: white;
                border-radius: 5px;
            }
        """)

    def setup_connections(self):
        """设置信号连接"""
        # 系统监控信号
        self.monitor.system_monitor.cpu_usage_updated.connect(self.update_cpu_usage)
        self.monitor.system_monitor.memory_usage_updated.connect(self.update_memory_usage)
        self.monitor.system_monitor.thread_count_updated.connect(self.update_thread_count)
        self.monitor.system_monitor.process_status_updated.connect(self.update_process_status)

        # 日志监控信号
        self.monitor.log_monitor.log_message_received.connect(self.add_log_message)
        self.monitor.log_monitor.crash_detected_from_log.connect(self.handle_log_crash_detected)
        self.monitor.log_monitor.exception_detected_from_log.connect(self.handle_log_exception_detected)
        self.monitor.log_monitor.process_exit_detected.connect(self.handle_log_process_exit)
        self.monitor.log_monitor.crash_detected_from_log.connect(self.handle_log_crash_detected)
        self.monitor.log_monitor.exception_detected_from_log.connect(self.handle_log_exception_detected)
        self.monitor.log_monitor.process_exit_detected.connect(self.handle_log_process_exit)

        # 闪退检测信号
        self.monitor.crash_detector.crash_detected.connect(self.handle_crash_detected)
        self.monitor.crash_detector.exception_detected.connect(self.handle_exception_detected)
        self.monitor.crash_detector.memory_leak_detected.connect(self.handle_memory_leak)

        # 头条进程监控信号
        self.monitor.toutiao_monitor.process_found.connect(self.handle_toutiao_process_found)
        self.monitor.toutiao_monitor.process_lost.connect(self.handle_toutiao_process_lost)
        self.monitor.toutiao_monitor.process_status_updated.connect(self.handle_toutiao_process_updated)

        # 深度监控信号
        self.monitor.deep_crash_detector.exception_caught.connect(self.handle_deep_exception)
        self.monitor.deep_crash_detector.crash_detected.connect(self.handle_deep_crash)
        self.monitor.deep_crash_detector.process_terminated.connect(self.handle_process_terminated)

        self.monitor.user_monitor.button_clicked.connect(self.handle_button_clicked)
        self.monitor.user_monitor.operation_started.connect(self.handle_operation_started)
        self.monitor.user_monitor.operation_completed.connect(self.handle_operation_completed)

        self.monitor.state_monitor.state_changed.connect(self.handle_state_changed)
        self.monitor.state_monitor.task_status_updated.connect(self.handle_task_status_updated)
        self.monitor.state_monitor.browser_event.connect(self.handle_browser_event)

    def setup_timers(self):
        """设置定时器"""
        # 监控时间更新定时器
        self.monitor_timer = QTimer()
        self.monitor_timer.timeout.connect(self.update_monitor_time)
        self.monitor_start_time = None

        # 图表更新定时器
        self.chart_timer = QTimer()
        self.chart_timer.timeout.connect(self.update_charts)
        self.chart_timer.start(1000)  # 每秒更新一次图表

    @pyqtSlot()
    def start_monitoring(self):
        """开始监控"""
        try:
            # 添加日志文件监控
            log_files = [
                "logs/app.log",
                "logs/error.log",
                "logs/debug.log"
            ]

            for log_file in log_files:
                if os.path.exists(log_file):
                    self.monitor.add_log_file(log_file)

            # 启动监控
            self.monitor.start_all_monitoring()

            # 更新UI状态
            self.start_btn.setEnabled(False)
            self.stop_btn.setEnabled(True)
            self.monitoring_status.setText("🟢 监控中")
            self.monitoring_status.setStyleSheet("color: green; font-weight: bold; font-size: 14px;")

            # 启动监控时间计时器
            self.monitor_start_time = datetime.now()
            self.monitor_timer.start(1000)

            self.status_label.setText("监控已启动")

        except Exception as e:
            self.status_label.setText(f"启动监控失败: {str(e)}")

    @pyqtSlot()
    def stop_monitoring(self):
        """停止监控"""
        try:
            # 停止监控
            self.monitor.stop_all_monitoring()

            # 更新UI状态
            self.start_btn.setEnabled(True)
            self.stop_btn.setEnabled(False)
            self.monitoring_status.setText("⚪ 未监控")
            self.monitoring_status.setStyleSheet("color: gray; font-weight: bold; font-size: 14px;")

            # 停止监控时间计时器
            self.monitor_timer.stop()

            self.status_label.setText("监控已停止")

        except Exception as e:
            self.status_label.setText(f"停止监控失败: {str(e)}")

    @pyqtSlot()
    def clear_logs(self):
        """清空日志"""
        self.log_display.clear()
        self.log_count = {'INFO': 0, 'WARNING': 0, 'ERROR': 0, 'DEBUG': 0}
        self.update_log_stats()
        self.status_label.setText("日志已清空")

    @pyqtSlot()
    def export_data(self):
        """导出监控数据"""
        try:
            from PyQt5.QtWidgets import QFileDialog

            filename, _ = QFileDialog.getSaveFileName(
                self, "导出监控数据",
                f"monitor_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
                "文本文件 (*.txt);;所有文件 (*)"
            )

            if filename:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write("批量存稿工具监控数据导出\n")
                    f.write(f"导出时间: {datetime.now()}\n")
                    f.write("=" * 50 + "\n\n")

                    # 导出日志统计
                    f.write("日志统计:\n")
                    for level, count in self.log_count.items():
                        f.write(f"  {level}: {count}\n")
                    f.write("\n")

                    # 导出日志内容
                    f.write("日志内容:\n")
                    f.write(self.log_display.toPlainText())

                self.status_label.setText(f"数据已导出到: {filename}")

        except Exception as e:
            self.status_label.setText(f"导出失败: {str(e)}")

    @pyqtSlot()
    def show_settings(self):
        """显示设置对话框"""
        # 这里可以实现设置对话框
        self.status_label.setText("设置功能待实现")

    @pyqtSlot()
    def filter_logs(self):
        """过滤日志"""
        # 这里可以实现日志过滤功能
        pass

    @pyqtSlot(float)
    def update_cpu_usage(self, cpu_percent):
        """更新CPU使用率"""
        self.cpu_label.setText(f"CPU: {cpu_percent:.1f}%")
        self.cpu_progress.setValue(int(cpu_percent))

        # 添加数据到图表
        import time
        current_time = time.time()
        self.cpu_data.append(cpu_percent)
        self.time_data.append(current_time)

        # 根据使用率设置颜色
        if cpu_percent > 80:
            self.cpu_progress.setStyleSheet("QProgressBar::chunk { background-color: #dc3545; }")
        elif cpu_percent > 60:
            self.cpu_progress.setStyleSheet("QProgressBar::chunk { background-color: #ffc107; }")
        else:
            self.cpu_progress.setStyleSheet("QProgressBar::chunk { background-color: #007bff; }")

    @pyqtSlot(float, float)
    def update_memory_usage(self, memory_mb, total_mb):
        """更新内存使用量"""
        self.memory_label.setText(f"内存: {memory_mb:.1f} MB")
        memory_percent = (memory_mb / total_mb) * 100 if total_mb > 0 else 0
        self.memory_progress.setValue(int(memory_percent))

        # 添加数据到图表
        self.memory_data.append(memory_mb)

        # 根据使用率设置颜色
        if memory_percent > 80:
            self.memory_progress.setStyleSheet("QProgressBar::chunk { background-color: #dc3545; }")
        elif memory_percent > 60:
            self.memory_progress.setStyleSheet("QProgressBar::chunk { background-color: #ffc107; }")
        else:
            self.memory_progress.setStyleSheet("QProgressBar::chunk { background-color: #28a745; }")

    @pyqtSlot(int)
    def update_thread_count(self, thread_count):
        """更新线程数量"""
        self.thread_label.setText(f"线程: {thread_count}")

    @pyqtSlot(dict)
    def update_process_status(self, process_status):
        """更新进程状态"""
        info_text = f"""
PID: {process_status.get('pid', 'N/A')}
状态: {process_status.get('status', 'N/A')}
创建时间: {process_status.get('create_time', 'N/A')}
CPU使用率: {process_status.get('cpu_percent', 0):.1f}%
内存使用率: {process_status.get('memory_percent', 0):.1f}%
线程数: {process_status.get('num_threads', 0)}
文件描述符: {process_status.get('num_fds', 0)}
        """.strip()

        self.process_info_label.setText(info_text)

    @pyqtSlot(str, str, str)
    def add_log_message(self, timestamp, level, message):
        """添加日志消息"""
        # 更新日志统计
        if level in self.log_count:
            self.log_count[level] += 1
        else:
            self.log_count['INFO'] += 1

        self.update_log_stats()

        # 设置日志颜色
        color_map = {
            'ERROR': '#ff4444',
            'WARNING': '#ffaa00',
            'INFO': '#ffffff',
            'DEBUG': '#888888'
        }

        color = color_map.get(level, '#ffffff')

        # 添加到日志显示区域
        formatted_message = f'<span style="color: {color};">[{timestamp}] [{level}] {message}</span>'
        self.log_display.append(formatted_message)

        # 自动滚动到底部
        if self.auto_scroll_check.isChecked():
            scrollbar = self.log_display.verticalScrollBar()
            if scrollbar:
                scrollbar.setValue(scrollbar.maximum())

    def update_log_stats(self):
        """更新日志统计"""
        stats_text = f"日志: INFO:{self.log_count['INFO']} WARN:{self.log_count['WARNING']} ERROR:{self.log_count['ERROR']}"
        self.log_stats_label.setText(stats_text)

        total_logs = sum(self.log_count.values())
        self.log_count_label.setText(f"日志数量: {total_logs}")

    @pyqtSlot()
    def update_monitor_time(self):
        """更新监控时间"""
        if self.monitor_start_time:
            elapsed = datetime.now() - self.monitor_start_time
            hours, remainder = divmod(elapsed.total_seconds(), 3600)
            minutes, seconds = divmod(remainder, 60)
            time_str = f"{int(hours):02d}:{int(minutes):02d}:{int(seconds):02d}"
            self.monitor_time_label.setText(f"监控时间: {time_str}")

    @pyqtSlot()
    def update_charts(self):
        """更新图表"""
        if not PYQTGRAPH_AVAILABLE or not hasattr(self, 'cpu_data') or not hasattr(self, 'memory_data'):
            return

        # 限制数据点数量
        max_points = 60
        if len(self.cpu_data) > max_points:
            self.cpu_data = self.cpu_data[-max_points:]
            self.memory_data = self.memory_data[-max_points:]
            self.time_data = self.time_data[-max_points:]

        # 更新图表
        if self.cpu_chart and self.cpu_data:
            self.cpu_chart.clear()
            self.cpu_chart.plot(self.time_data, self.cpu_data, pen='b')

        if self.memory_chart and self.memory_data:
            self.memory_chart.clear()
            self.memory_chart.plot(self.time_data, self.memory_data, pen='g')

    @pyqtSlot(dict)
    def handle_crash_detected(self, crash_info):
        """处理检测到的闪退"""
        self.status_label.setText("⚠️ 检测到程序闪退")

        # 添加到异常表格
        row = self.exception_table.rowCount()
        self.exception_table.insertRow(row)

        self.exception_table.setItem(row, 0, QTableWidgetItem(str(crash_info.get('timestamp', ''))))
        self.exception_table.setItem(row, 1, QTableWidgetItem("CRASH"))
        self.exception_table.setItem(row, 2, QTableWidgetItem(crash_info.get('type', '')))
        self.exception_table.setItem(row, 3, QTableWidgetItem(crash_info.get('message', '')))

    @pyqtSlot(str, str)
    def handle_exception_detected(self, exc_type, exc_message):
        """处理检测到的异常"""
        self.status_label.setText(f"⚠️ 检测到异常: {exc_type}")

        # 添加到异常表格
        row = self.exception_table.rowCount()
        self.exception_table.insertRow(row)

        self.exception_table.setItem(row, 0, QTableWidgetItem(datetime.now().strftime('%H:%M:%S')))
        self.exception_table.setItem(row, 1, QTableWidgetItem("EXCEPTION"))
        self.exception_table.setItem(row, 2, QTableWidgetItem(exc_type))
        self.exception_table.setItem(row, 3, QTableWidgetItem(exc_message))

    @pyqtSlot(float)
    def handle_memory_leak(self, memory_growth):
        """处理内存泄漏检测"""
        self.memory_leak_label.setText(f"内存泄漏: +{memory_growth:.1f}MB")
        self.memory_leak_label.setStyleSheet("color: red; font-weight: bold;")
        self.status_label.setText(f"⚠️ 检测到内存泄漏: +{memory_growth:.1f}MB")

    @pyqtSlot(dict)
    def handle_toutiao_process_found(self, process_info):
        """处理发现头条进程"""
        pid = process_info.get('pid', 'N/A')
        name = process_info.get('name', 'N/A')
        create_time = process_info.get('create_time', 'N/A')

        status_text = f"""
🎯 已检测到头条存稿工具！

进程信息：
• PID: {pid}
• 进程名: {name}
• 启动时间: {create_time}
• 状态: 正在运行
• CPU使用率: {process_info.get('cpu_percent', 0):.1f}%
• 内存使用: {process_info.get('memory_percent', 0):.1f}%
• 线程数: {process_info.get('num_threads', 0)}

✅ 监控已连接到头条存稿工具
        """.strip()

        self.toutiao_status_label.setText(status_text)
        self.toutiao_status_label.setStyleSheet("""
            QLabel {
                padding: 10px;
                background-color: #d4edda;
                border: 1px solid #c3e6cb;
                border-radius: 4px;
                color: #155724;
            }
        """)

        self.status_label.setText(f"🎯 已连接到头条存稿工具 (PID: {pid})")

    @pyqtSlot()
    def handle_toutiao_process_lost(self):
        """处理头条进程丢失"""
        status_text = """
⚠️ 头条存稿工具进程已丢失

可能原因：
• 程序已正常退出
• 程序发生崩溃
• 程序被强制终止

🔍 正在重新搜索进程...
        """.strip()

        self.toutiao_status_label.setText(status_text)
        self.toutiao_status_label.setStyleSheet("""
            QLabel {
                padding: 10px;
                background-color: #fff3cd;
                border: 1px solid #ffeaa7;
                border-radius: 4px;
                color: #856404;
            }
        """)

        self.status_label.setText("⚠️ 头条存稿工具进程已丢失")

    @pyqtSlot(dict)
    def handle_toutiao_process_updated(self, process_info):
        """处理头条进程状态更新"""
        pid = process_info.get('pid', 'N/A')
        cpu_percent = process_info.get('cpu_percent', 0)
        memory_percent = process_info.get('memory_percent', 0)
        num_threads = process_info.get('num_threads', 0)
        status = process_info.get('status', 'unknown')

        # 更新任务状态显示
        from datetime import datetime
        task_info = f"""
当前任务状态：
• 进程状态: {status}
• CPU使用率: {cpu_percent:.1f}%
• 内存使用率: {memory_percent:.1f}%
• 活动线程: {num_threads}
• 连接数: {process_info.get('connections', 0)}

最后更新: {datetime.now().strftime('%H:%M:%S')}
        """.strip()

        self.task_status_label.setText(task_info)

    # 深度监控处理方法
    @pyqtSlot(str, str, str)
    def handle_deep_exception(self, exc_type, exc_message, exc_traceback):
        """处理深度异常检测"""
        # 添加到异常表格
        row = self.exception_table.rowCount()
        self.exception_table.insertRow(row)

        self.exception_table.setItem(row, 0, QTableWidgetItem(datetime.now().strftime('%H:%M:%S')))
        self.exception_table.setItem(row, 1, QTableWidgetItem("DEEP_EXCEPTION"))
        self.exception_table.setItem(row, 2, QTableWidgetItem(f"{exc_type}: {exc_message}"))
        self.exception_table.setItem(row, 3, QTableWidgetItem(exc_traceback[:100] + "..."))

        # 更新状态栏
        self.status_label.setText(f"🚨 检测到深度异常: {exc_type}")

        # 滚动到最新行
        self.exception_table.scrollToBottom()

    @pyqtSlot(dict)
    def handle_deep_crash(self, crash_info):
        """处理深度闪退检测"""
        # 显示闪退警告
        crash_message = f"""
🚨 检测到头条存稿工具闪退！

闪退信息：
• 异常类型: {crash_info.get('type', 'Unknown')}
• 错误消息: {crash_info.get('message', 'No message')}
• 发生时间: {crash_info.get('timestamp', 'Unknown')}
• 线程: {crash_info.get('thread', 'Unknown')}

⚠️ 建议检查日志获取详细信息
        """.strip()

        self.toutiao_status_label.setText(crash_message)
        self.toutiao_status_label.setStyleSheet("""
            QLabel {
                padding: 10px;
                background-color: #f8d7da;
                border: 1px solid #f5c6cb;
                border-radius: 4px;
                color: #721c24;
            }
        """)

        self.status_label.setText("🚨 头条存稿工具发生闪退")

    @pyqtSlot(int, str)
    def handle_process_terminated(self, pid, reason):
        """处理进程终止"""
        self.status_label.setText(f"⚠️ 进程终止 (PID: {pid}): {reason}")

    @pyqtSlot(str, str, dict)
    def handle_button_clicked(self, button_name, window_name, extra_info):
        """处理按钮点击事件"""
        # 更新按钮点击统计
        current_count = int(self.button_click_count_label.text().split(': ')[1])
        self.button_click_count_label.setText(f"按钮点击: {current_count + 1}")

        # 添加到交互表格
        row = self.interaction_table.rowCount()
        self.interaction_table.insertRow(row)

        self.interaction_table.setItem(row, 0, QTableWidgetItem(datetime.now().strftime('%H:%M:%S')))
        self.interaction_table.setItem(row, 1, QTableWidgetItem("按钮点击"))
        self.interaction_table.setItem(row, 2, QTableWidgetItem(button_name))
        self.interaction_table.setItem(row, 3, QTableWidgetItem(window_name))
        self.interaction_table.setItem(row, 4, QTableWidgetItem(str(extra_info)))

        # 滚动到最新行
        self.interaction_table.scrollToBottom()

    @pyqtSlot(str, dict)
    def handle_operation_started(self, operation_name, parameters):
        """处理操作开始"""
        self.current_operation_label.setText(f"当前操作: {operation_name}")

        # 更新操作统计
        current_count = int(self.operation_count_label.text().split(': ')[1])
        self.operation_count_label.setText(f"操作执行: {current_count + 1}")

    @pyqtSlot(str, bool, str)
    def handle_operation_completed(self, operation_name, success, result):
        """处理操作完成"""
        self.current_operation_label.setText("当前操作: 无")

        # 添加到交互表格
        row = self.interaction_table.rowCount()
        self.interaction_table.insertRow(row)

        status = "成功" if success else "失败"
        self.interaction_table.setItem(row, 0, QTableWidgetItem(datetime.now().strftime('%H:%M:%S')))
        self.interaction_table.setItem(row, 1, QTableWidgetItem("操作完成"))
        self.interaction_table.setItem(row, 2, QTableWidgetItem(f"{operation_name} ({status})"))
        self.interaction_table.setItem(row, 3, QTableWidgetItem(""))
        self.interaction_table.setItem(row, 4, QTableWidgetItem(result))

        # 滚动到最新行
        self.interaction_table.scrollToBottom()

    @pyqtSlot(str, str, object, object)
    def handle_state_changed(self, module, state_name, old_value, new_value):
        """处理状态变化"""
        # 添加到状态表格
        row = self.state_table.rowCount()
        self.state_table.insertRow(row)

        self.state_table.setItem(row, 0, QTableWidgetItem(datetime.now().strftime('%H:%M:%S')))
        self.state_table.setItem(row, 1, QTableWidgetItem(module))
        self.state_table.setItem(row, 2, QTableWidgetItem(state_name))
        self.state_table.setItem(row, 3, QTableWidgetItem(str(old_value)))
        self.state_table.setItem(row, 4, QTableWidgetItem(str(new_value)))

        # 滚动到最新行
        self.state_table.scrollToBottom()

    @pyqtSlot(str, dict)
    def handle_task_status_updated(self, task_id, status_info):
        """处理任务状态更新"""
        # 更新活动任务数量
        active_tasks = len([task for task in [status_info] if status_info.get('status') == 'running'])
        self.active_tasks_label.setText(f"活动任务: {active_tasks}")

    @pyqtSlot(str, dict)
    def handle_browser_event(self, event_type, details):
        """处理浏览器事件"""
        if 'browser' in event_type.lower():
            status = details.get('success', 'unknown')
            self.browser_status_label.setText(f"浏览器状态: {status}")

        # 添加到状态表格
        row = self.state_table.rowCount()
        self.state_table.insertRow(row)

        self.state_table.setItem(row, 0, QTableWidgetItem(datetime.now().strftime('%H:%M:%S')))
        self.state_table.setItem(row, 1, QTableWidgetItem("浏览器"))
        self.state_table.setItem(row, 2, QTableWidgetItem(event_type))
        self.state_table.setItem(row, 3, QTableWidgetItem(""))
        self.state_table.setItem(row, 4, QTableWidgetItem(str(details)))

        # 滚动到最新行
        self.state_table.scrollToBottom()

    # 日志闪退检测处理方法
    @pyqtSlot(str, str)
    def handle_log_crash_detected(self, timestamp, details):
        """处理从日志检测到的闪退"""
        # 显示醒目的闪退警告
        crash_message = f"""
🚨 从日志检测到闪退！

检测时间: {timestamp}
检测详情:
{details}

⚠️ 这表明头条存稿工具可能发生了崩溃或异常退出
建议立即检查应用程序状态和日志文件
        """.strip()

        # 更新头条状态显示
        self.toutiao_status_label.setText(crash_message)
        self.toutiao_status_label.setStyleSheet("""
            QLabel {
                padding: 15px;
                background-color: #f8d7da;
                border: 2px solid #f5c6cb;
                border-radius: 6px;
                color: #721c24;
                font-weight: bold;
            }
        """)

        # 添加到异常表格
        row = self.exception_table.rowCount()
        self.exception_table.insertRow(row)

        self.exception_table.setItem(row, 0, QTableWidgetItem(timestamp))
        self.exception_table.setItem(row, 1, QTableWidgetItem("LOG_CRASH"))
        self.exception_table.setItem(row, 2, QTableWidgetItem("从日志检测到闪退"))
        self.exception_table.setItem(row, 3, QTableWidgetItem(details[:100] + "..."))

        # 滚动到最新行
        self.exception_table.scrollToBottom()

        # 更新状态栏
        self.status_label.setText(f"🚨 日志检测到闪退 - {timestamp}")

        # 记录到日志
        logging.critical(f"从日志检测到闪退: {details}")

    @pyqtSlot(str, str, str)
    def handle_log_exception_detected(self, timestamp, exception_type, message):
        """处理从日志检测到的异常"""
        # 添加到异常表格
        row = self.exception_table.rowCount()
        self.exception_table.insertRow(row)

        self.exception_table.setItem(row, 0, QTableWidgetItem(timestamp))
        self.exception_table.setItem(row, 1, QTableWidgetItem("LOG_EXCEPTION"))
        self.exception_table.setItem(row, 2, QTableWidgetItem(f"{exception_type}"))
        self.exception_table.setItem(row, 3, QTableWidgetItem(message[:100] + "..."))

        # 滚动到最新行
        self.exception_table.scrollToBottom()

        # 更新状态栏
        self.status_label.setText(f"⚠️ 日志检测到异常: {exception_type}")

        # 如果是严重异常，更新头条状态
        critical_types = ['Fatal', 'Critical', 'Crash', 'Segmentation', 'Access']
        if any(critical in exception_type for critical in critical_types):
            warning_message = f"""
⚠️ 检测到严重异常

时间: {timestamp}
类型: {exception_type}
消息: {message[:200]}

🔍 建议立即检查应用程序状态
            """.strip()

            self.toutiao_status_label.setText(warning_message)
            self.toutiao_status_label.setStyleSheet("""
                QLabel {
                    padding: 10px;
                    background-color: #fff3cd;
                    border: 1px solid #ffeaa7;
                    border-radius: 4px;
                    color: #856404;
                }
            """)

    @pyqtSlot(str, int)
    def handle_log_process_exit(self, timestamp, exit_code):
        """处理从日志检测到的进程退出"""
        # 更新状态栏
        if exit_code == 0:
            self.status_label.setText(f"ℹ️ 进程正常退出 - {timestamp}")
        else:
            self.status_label.setText(f"⚠️ 进程异常退出 (代码: {exit_code}) - {timestamp}")

            # 异常退出时显示警告
            exit_message = f"""
⚠️ 检测到进程异常退出

退出时间: {timestamp}
退出代码: {exit_code}

{self._get_exit_code_meaning(exit_code)}

🔍 建议检查日志文件获取更多信息
            """.strip()

            self.toutiao_status_label.setText(exit_message)
            self.toutiao_status_label.setStyleSheet("""
                QLabel {
                    padding: 10px;
                    background-color: #fff3cd;
                    border: 1px solid #ffeaa7;
                    border-radius: 4px;
                    color: #856404;
                }
            """)

    def _get_exit_code_meaning(self, exit_code):
        """获取退出代码的含义"""
        exit_meanings = {
            0: "正常退出",
            1: "一般错误",
            2: "误用shell命令",
            126: "命令无法执行",
            127: "命令未找到",
            128: "无效的退出参数",
            130: "用户中断 (Ctrl+C)",
            137: "进程被强制终止 (SIGKILL)",
            139: "段错误 (SIGSEGV)"
        }

        meaning = exit_meanings.get(exit_code, "未知错误")
        return f"含义: {meaning}"

    def closeEvent(self, event):
        """窗口关闭事件"""
        # 停止监控
        if self.monitor.is_running:
            self.stop_monitoring()
        event.accept()
