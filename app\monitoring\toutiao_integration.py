#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
头条存稿工具监控集成模块 - 将监控功能集成到头条存稿工具中
"""

import logging
import functools
from .monitor_decorators import (
    monitor_button_click, monitor_operation, monitor_state_change,
    monitor_browser_operation, monitor_exception_safe, create_monitored_button,
    performance_monitor, create_state_tracker, monitor_all_buttons,
    inject_monitoring_to_class
)
from .deep_monitor import (
    log_button_click, log_operation_start, log_operation_complete,
    update_state, log_browser_event
)

class ToutiaoMonitoringIntegration:
    """头条存稿工具监控集成类"""
    
    def __init__(self):
        self.is_integrated = False
        self.monitored_components = []
        
    def integrate_main_window(self, main_window):
        """集成主窗口监控"""
        if not main_window:
            return
            
        try:
            # 监控所有按钮
            monitored_buttons = monitor_all_buttons(main_window, "主窗口")
            self.monitored_components.extend(monitored_buttons)
            
            # 添加状态跟踪器
            main_window._monitor_state_tracker = create_state_tracker("主窗口")
            
            # 包装关键方法
            self._wrap_main_window_methods(main_window)
            
            logging.info("主窗口监控集成完成")
            
        except Exception as e:
            logging.error(f"主窗口监控集成失败: {str(e)}")
            
    def integrate_account_tab(self, account_tab):
        """集成账号标签页监控"""
        if not account_tab:
            return
            
        try:
            # 监控账号相关按钮
            self._monitor_account_buttons(account_tab)
            
            # 包装账号操作方法
            self._wrap_account_methods(account_tab)
            
            # 添加状态跟踪器
            account_tab._monitor_state_tracker = create_state_tracker("账号管理")
            
            logging.info("账号标签页监控集成完成")
            
        except Exception as e:
            logging.error(f"账号标签页监控集成失败: {str(e)}")
            
    def integrate_batch_tab(self, batch_tab):
        """集成批量存稿标签页监控"""
        if not batch_tab:
            return
            
        try:
            # 监控批量操作按钮
            self._monitor_batch_buttons(batch_tab)
            
            # 包装批量操作方法
            self._wrap_batch_methods(batch_tab)
            
            # 添加状态跟踪器
            batch_tab._monitor_state_tracker = create_state_tracker("批量存稿")
            
            logging.info("批量存稿标签页监控集成完成")
            
        except Exception as e:
            logging.error(f"批量存稿标签页监控集成失败: {str(e)}")
            
    def integrate_browser_operations(self, browser_manager):
        """集成浏览器操作监控"""
        if not browser_manager:
            return
            
        try:
            # 包装浏览器方法
            self._wrap_browser_methods(browser_manager)
            
            logging.info("浏览器操作监控集成完成")
            
        except Exception as e:
            logging.error(f"浏览器操作监控集成失败: {str(e)}")
            
    def _monitor_account_buttons(self, account_tab):
        """监控账号相关按钮"""
        button_mappings = {
            'load_account_btn': '加载账号',
            'refresh_account_btn': '刷新账号',
            'delete_account_btn': '删除账号',
            'add_account_btn': '添加账号',
            'export_account_btn': '导出账号',
            'import_account_btn': '导入账号'
        }
        
        for attr_name, button_name in button_mappings.items():
            if hasattr(account_tab, attr_name):
                button = getattr(account_tab, attr_name)
                monitored_button = create_monitored_button(button, button_name, "账号管理")
                self.monitored_components.append(monitored_button)
                
    def _monitor_batch_buttons(self, batch_tab):
        """监控批量操作按钮"""
        button_mappings = {
            'start_batch_btn': '开始批量存稿',
            'stop_batch_btn': '停止批量存稿',
            'pause_batch_btn': '暂停批量存稿',
            'resume_batch_btn': '恢复批量存稿',
            'clear_queue_btn': '清空队列',
            'add_material_btn': '添加素材'
        }
        
        for attr_name, button_name in button_mappings.items():
            if hasattr(batch_tab, attr_name):
                button = getattr(batch_tab, attr_name)
                monitored_button = create_monitored_button(button, button_name, "批量存稿")
                self.monitored_components.append(monitored_button)
                
    def _wrap_main_window_methods(self, main_window):
        """包装主窗口方法"""
        # 包装关闭事件
        if hasattr(main_window, 'closeEvent'):
            original_close = main_window.closeEvent
            
            @monitor_exception_safe("主窗口关闭")
            def monitored_close_event(event):
                log_button_click("关闭窗口", "主窗口")
                return original_close(event)
                
            main_window.closeEvent = monitored_close_event
            
        # 包装显示事件
        if hasattr(main_window, 'showEvent'):
            original_show = main_window.showEvent
            
            @monitor_exception_safe("主窗口显示")
            def monitored_show_event(event):
                log_button_click("显示窗口", "主窗口")
                return original_show(event)
                
            main_window.showEvent = monitored_show_event
            
    def _wrap_account_methods(self, account_tab):
        """包装账号操作方法"""
        methods_to_wrap = [
            ('load_accounts', '加载账号列表'),
            ('add_account', '添加账号'),
            ('delete_account', '删除账号'),
            ('refresh_account', '刷新账号'),
            ('switch_account', '切换账号'),
            ('export_accounts', '导出账号'),
            ('import_accounts', '导入账号')
        ]
        
        for method_name, operation_name in methods_to_wrap:
            if hasattr(account_tab, method_name):
                original_method = getattr(account_tab, method_name)
                
                @monitor_operation(operation_name)
                @monitor_exception_safe(operation_name)
                def wrapped_method(*args, **kwargs):
                    return original_method(*args, **kwargs)
                
                setattr(account_tab, method_name, wrapped_method)
                
    def _wrap_batch_methods(self, batch_tab):
        """包装批量操作方法"""
        methods_to_wrap = [
            ('start_batch_process', '开始批量处理'),
            ('stop_batch_process', '停止批量处理'),
            ('pause_batch_process', '暂停批量处理'),
            ('resume_batch_process', '恢复批量处理'),
            ('add_to_queue', '添加到队列'),
            ('clear_queue', '清空队列'),
            ('process_single_item', '处理单个项目')
        ]
        
        for method_name, operation_name in methods_to_wrap:
            if hasattr(batch_tab, method_name):
                original_method = getattr(batch_tab, method_name)
                
                @monitor_operation(operation_name)
                @monitor_exception_safe(operation_name)
                def wrapped_method(*args, **kwargs):
                    return original_method(*args, **kwargs)
                
                setattr(batch_tab, method_name, wrapped_method)
                
    def _wrap_browser_methods(self, browser_manager):
        """包装浏览器操作方法"""
        methods_to_wrap = [
            ('start_browser', '启动浏览器'),
            ('close_browser', '关闭浏览器'),
            ('navigate_to_page', '导航到页面'),
            ('login', '登录操作'),
            ('upload_content', '上传内容'),
            ('submit_form', '提交表单'),
            ('wait_for_element', '等待元素'),
            ('click_element', '点击元素'),
            ('input_text', '输入文本')
        ]
        
        for method_name, operation_name in methods_to_wrap:
            if hasattr(browser_manager, method_name):
                original_method = getattr(browser_manager, method_name)
                
                @monitor_browser_operation(operation_name)
                @monitor_exception_safe(f"浏览器{operation_name}")
                def wrapped_method(*args, **kwargs):
                    return original_method(*args, **kwargs)
                
                setattr(browser_manager, method_name, wrapped_method)

# 全局集成实例
_integration = ToutiaoMonitoringIntegration()

def integrate_monitoring(main_window=None, account_tab=None, batch_tab=None, browser_manager=None):
    """集成监控功能到头条存稿工具"""
    try:
        if main_window:
            _integration.integrate_main_window(main_window)
            
        if account_tab:
            _integration.integrate_account_tab(account_tab)
            
        if batch_tab:
            _integration.integrate_batch_tab(batch_tab)
            
        if browser_manager:
            _integration.integrate_browser_operations(browser_manager)
            
        _integration.is_integrated = True
        logging.info("头条存稿工具监控集成完成")
        
    except Exception as e:
        logging.error(f"监控集成失败: {str(e)}")

def is_monitoring_integrated():
    """检查监控是否已集成"""
    return _integration.is_integrated

# 便捷函数
def monitor_toutiao_operation(operation_name):
    """头条操作监控装饰器"""
    return monitor_operation(operation_name)

def monitor_toutiao_button(button_name, window_name="头条工具"):
    """头条按钮监控装饰器"""
    return monitor_button_click(button_name, window_name)

def track_toutiao_state(module_name, state_name):
    """头条状态跟踪装饰器"""
    return monitor_state_change(module_name, state_name)

def safe_toutiao_operation(operation_name):
    """头条安全操作装饰器"""
    return monitor_exception_safe(operation_name)

# 状态更新便捷函数
def update_account_state(state_name, value):
    """更新账号状态"""
    update_state("账号管理", state_name, value)

def update_batch_state(state_name, value):
    """更新批量处理状态"""
    update_state("批量存稿", state_name, value)

def update_browser_state(state_name, value):
    """更新浏览器状态"""
    update_state("浏览器", state_name, value)

# 操作记录便捷函数
def log_account_operation(operation_name, parameters=None):
    """记录账号操作"""
    return log_operation_start(f"账号-{operation_name}", parameters)

def log_batch_operation(operation_name, parameters=None):
    """记录批量操作"""
    return log_operation_start(f"批量-{operation_name}", parameters)

def log_browser_operation_start(operation_name, parameters=None):
    """记录浏览器操作"""
    return log_operation_start(f"浏览器-{operation_name}", parameters)
