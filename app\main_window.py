#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
主窗口模块，集成系统监控状态栏
示例代码 - 假设这是应用程序的主窗口
"""

import sys
import os
import logging
import json
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QStatusBar, QTabWidget,
    QWidget, QVBoxLayout, QPushButton, QMessageBox, QHBoxLayout, QLabel,
    QDesktopWidget, QStyleFactory, QToolBar, QAction, QCheckBox, QRadioButton,
    QLineEdit, QSizePolicy, QFrame, QDialog, QTextEdit, QProgressBar,
    QScrollArea, QGroupBox, QGridLayout, QSplitter, QButtonGroup
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QFont, QIcon
from app.tabs.account_tab import AccountTab  # 使用原始账号标签页
from app.tabs.setting_tab import SettingTab
# from app.tabs.modern_account_tab import ModernAccountTab  # 暂时禁用现代化账号标签页
from app.components.more_projects_tab import MoreProjectsTab  # 更多网创项目选项卡
from app.utils.thread_worker import ThreadManager
from app.widgets.status_bar_widget import StatusBarWidget
from app.utils.kamidenglu import APP_VERSION, KamiManager  # 导入KamiManager用于检查更新
from app.utils.piliang_cunggao import BatchCunggaoWorker  # 批量存稿工作类
from app.dialogs.toutiao_spider_dialog import open_toutiao_spider_dialog  # 导入头条爬虫对话框
from app.utils.logger import info, error, warning, debug  # 导入日志函数
from app.utils.automation_scheduler import AutomationScheduler  # 导入自动化调度器
from app.widgets.heartbeat_indicator import AutomationStatusWidget  # 导入心跳指示器

from app.dialogs.proxy_settings_dialog import open_proxy_settings_dialog  # 导入代理设置对话框
from app.dialogs.material_selection_dialog import open_material_selection_dialog  # 导入素材选择对话框
from app.dialogs.video_processor_dialog import VideoProcessorDialog  # 导入视频处理对话框

from app.utils.yonghu_zhuangtai import UserStatusChecker  # 导入用户状态检测器
from app.utils.greeting_generator import get_greeting  # 导入问候语生成器
from app.widgets.earnings_stats_widget import EarningsStatsWidget  # 导入收益统计组件
from app.utils.kamidenglu import get_announcement  # 导入公告获取函数
import time
import random
import datetime

class MainWindow(QMainWindow):
    """应用程序主窗口"""

    def __init__(self):
        """初始化主窗口"""
        super().__init__()
        self.setWindowTitle(f"内容社交工具 v{APP_VERSION}")  # 使用APP_VERSION变量
        self.setMinimumSize(1280, 800)  # 增加最小宽度，确保所有元素都能显示

        # 设置窗口图标 - 使用多个备选路径
        try:
            from app.utils.resource_path import resource_path

            # 定义多个备选图标路径
            icon_paths = [
                resource_path("app/resources/icon.ico"),                # 主图标
                resource_path("app/resources/icons/gray_wolf.ico"),     # 灰太狼图标(ICO)
                resource_path("app/resources/icons/gray_wolf.png"),     # 灰太狼图标(PNG)
            ]

            # 尝试设置图标
            icon_set = False
            for icon_path in icon_paths:
                if os.path.exists(icon_path):
                    self.setWindowIcon(QIcon(icon_path))
                    logging.info(f"已设置窗口图标: {icon_path}")
                    icon_set = True
                    break

            # 如果所有图标都不存在，记录警告
            if not icon_set:
                logging.warning("所有图标文件都不存在，将使用系统默认图标")

                # 尝试创建资源目录结构
                try:
                    resources_dir = resource_path("app/resources")
                    icons_dir = resource_path("app/resources/icons")

                    # 确保目录存在
                    os.makedirs(resources_dir, exist_ok=True)
                    os.makedirs(icons_dir, exist_ok=True)

                    logging.info(f"已创建资源目录结构: {resources_dir}, {icons_dir}")
                except Exception as e:
                    logging.error(f"创建资源目录结构失败: {str(e)}")
        except Exception as e:
            logging.error(f"设置窗口图标时出错: {str(e)}")

        # 卡密信息
        self.card_number = ""
        self.expiry_time = ""
        self.kami_manager = KamiManager()  # 创建卡密管理器用于更新检测

        # 批量存稿线程引用
        self._batch_draft_thread = None
        self._batch_draft_worker = None

        # 初始化检测结果
        self.init_check_results = None

        # 初始化自动化调度器
        self.automation_scheduler = AutomationScheduler(self)

        # 初始化心跳指示器
        self.automation_status_widget = None

        # 应用Fusion样式，更现代化的外观
        self.set_application_style()

        # 设置窗口自适应屏幕大小
        self.adjust_to_screen()

        # 创建顶部工具栏
        self.create_top_toolbar()

        # 创建主部件和布局
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)

        # 根据屏幕分辨率动态调整边距
        if hasattr(self, 'screen_width') and self.screen_width < 1366:
            # 小屏幕使用更小的边距
            main_layout.setContentsMargins(10, 10, 10, 10)
            main_layout.setSpacing(8)
            logging.info("检测到小屏幕，使用紧凑布局")
        else:
            # 大屏幕使用正常边距
            main_layout.setContentsMargins(20, 20, 20, 20)
            main_layout.setSpacing(15)

        # 添加问候语标签 - 减小间距
        try:
            greeting_text = get_greeting()
            self.greeting_label = QLabel(greeting_text)
            self.greeting_label.setObjectName("greetingLabel")
            self.greeting_label.setContentsMargins(0, 0, 0, 0)  # 移除内边距
            self.greeting_label.setStyleSheet("margin-bottom: 2px;")  # 减小底部边距
            main_layout.addWidget(self.greeting_label)

            # 减小问候语和收益统计之间的间距
            main_layout.setSpacing(5)  # 设置更小的间距
        except Exception as e:
            logging.error(f"添加问候语标签时出错: {str(e)}")

        # 添加收益统计组件 - 创建水平布局显示两个收益统计
        try:
            # 创建收益统计容器
            earnings_container = QWidget()
            earnings_layout = QHBoxLayout(earnings_container)
            earnings_layout.setContentsMargins(0, 0, 0, 0)
            earnings_layout.setSpacing(10)  # 两个组件之间的间距

            # 第一个收益统计组件
            self.earnings_stats = EarningsStatsWidget()
            earnings_layout.addWidget(self.earnings_stats)

            # 第二个收益统计组件 - 显示昨日收益
            self.earnings_stats_2 = EarningsStatsWidget(title="📈 昨日收益")
            earnings_layout.addWidget(self.earnings_stats_2)

            # 添加收益统计容器到主布局
            main_layout.addWidget(earnings_container)
            logging.info("已添加两个收益统计组件")

            # 不在初始化时更新总收益，而是在账号数据加载完成后更新
        except Exception as e:
            logging.error(f"添加收益统计组件时出错: {str(e)}")

        # 创建标签页
        self.tab_widget = QTabWidget()
        self.tab_widget.setDocumentMode(True)  # 更现代的标签页外观
        self.tab_widget.setContentsMargins(0, 0, 0, 0)  # 移除内边距

        # 使用原始账号标签页
        self.account_tab = AccountTab()
        self.setting_tab = SettingTab()

        # 创建更多网创项目选项卡
        self.more_projects_tab = MoreProjectsTab()

        # 创建系统日志选项卡
        self.system_log_tab = self.create_system_log_tab()

        # 连接信号
        self.account_tab.load_account_clicked.connect(self.on_load_account)

        # 移除自动加载账号功能，用户需要手动点击加载账号按钮
        # self.setting_tab.cookie_path_changed.connect(self.on_cookie_path_changed)

        # 设置保存信号连接
        self.setting_tab.settings_saved.connect(self.on_settings_saved)

        # 连接cookie数量变化信号
        self.account_tab.cookie_count_changed.connect(self.update_account_count)

        # 系统日志功能已移除

        # 连接账号标签页的表格刷新完成信号
        if hasattr(self.account_tab, 'table_refresh_completed'):
            self.account_tab.table_refresh_completed.connect(self.on_table_refresh_completed)
            logging.info("已连接表格刷新完成信号")

        # 添加标签页 - 使用更长的标签文本
        self.tab_widget.addTab(self.account_tab, "账号管理")
        self.tab_widget.addTab(self.system_log_tab, "系统日志")
        self.tab_widget.addTab(self.more_projects_tab, "更多网创项目")
        self.tab_widget.addTab(self.setting_tab, "系统设置")

        # 连接自动化调度器信号
        self.connect_automation_signals()

        # 设置标签页图标 - 暂时不设置图标，避免文件不存在导致错误
        # self.tab_widget.setTabIcon(0, QIcon("app/resources/icons/account.png"))
        # self.tab_widget.setTabIcon(1, QIcon("app/resources/icons/log.png"))
        # self.tab_widget.setTabIcon(2, QIcon("app/resources/icons/setting.png"))

        main_layout.addWidget(self.tab_widget)

        # 创建底部状态栏
        status_frame = QWidget()
        status_frame.setObjectName("statusFrame")  # 设置对象名，便于样式表选择
        status_layout = QHBoxLayout(status_frame)

        # 根据屏幕分辨率动态调整边距 - 优化按钮布局
        if hasattr(self, 'screen_width') and self.screen_width < 1366:
            # 小屏幕使用更小的边距和间距
            status_layout.setContentsMargins(6, 3, 6, 3)
            status_layout.setSpacing(4)
        else:
            # 大屏幕使用紧凑的边距和间距
            status_layout.setContentsMargins(10, 5, 10, 5)
            status_layout.setSpacing(6)

        # 账号数量标签
        self.total_count_label = QLabel("实际数量: 0")
        self.total_count_label.setObjectName("totalCountLabel")
        # 设置最小宽度，确保文本不会被压缩
        self.total_count_label.setMinimumWidth(100)
        status_layout.addWidget(self.total_count_label)

        status_layout.addStretch()

        # 底部状态栏按钮已移动到账号管理页面

        # 剩余的底部状态栏按钮已移动到账号管理页面

        status_layout.addStretch()

        # 卡密信息已移至顶部工具栏
        status_layout.addStretch()

        # 添加系统监控组件
        try:
            from app.widgets.status_bar_widget import SystemInfoWidget
            self.system_info = SystemInfoWidget()
            status_layout.addWidget(self.system_info)

            # 初始化系统监控
            self.init_system_monitor()
        except Exception as e:
            logging.error(f"添加系统监控组件失败: {str(e)}")
            # 如果导入失败，继续使用旧的静态标签
            self.cpu_label = QLabel("CPU: 0%")
            self.cpu_label.setObjectName("cpuLabel")
            status_layout.addWidget(self.cpu_label)

            status_layout.addStretch()

            self.upload_label = QLabel("↑ 0 KB/s")
            self.upload_label.setObjectName("uploadLabel")
            status_layout.addWidget(self.upload_label)

            self.download_label = QLabel("↓ 0 KB/s")
            self.download_label.setObjectName("downloadLabel")
            status_layout.addWidget(self.download_label)

        main_layout.addWidget(status_frame)

        # 创建UI刷新定时器，确保界面响应，使用更保守的刷新频率
        self.ui_refresh_timer = QTimer(self)
        self.ui_refresh_timer.timeout.connect(self.process_pending_events)
        self.ui_refresh_timer.start(3000)  # 每3秒刷新一次界面事件，进一步降低系统负担

        # 延迟初始化非关键功能，提高启动速度 - 使用单次定时器
        self.create_single_shot_timer(1000, self.delayed_initialization)

        # 检查是否是首次运行，如果是则显示教程 - 使用单次定时器
        self.create_single_shot_timer(2000, self.check_first_run)

        # 启动完成后启用UI日志输出 - 使用单次定时器
        self.create_single_shot_timer(3000, self.enable_ui_logging)

        # 延迟触发收益统计更新，确保账号数据已加载 - 使用单次定时器
        self.create_single_shot_timer(8000, self.trigger_initial_earnings_update)

    def create_single_shot_timer(self, delay_ms, callback):
        """创建单次执行定时器的辅助方法"""
        def single_shot_wrapper():
            """包装器函数，确保只执行一次后停止定时器"""
            try:
                callback()
            finally:
                # 执行完成后停止定时器
                if hasattr(single_shot_wrapper, 'timer'):
                    single_shot_wrapper.timer.stop()

        timer = QTimer(self)
        timer.timeout.connect(single_shot_wrapper)
        single_shot_wrapper.timer = timer  # 保存定时器引用
        timer.start(delay_ms)
        return timer

    def connect_automation_signals(self):
        """连接自动化调度器信号"""
        try:
            if self.automation_scheduler and self.automation_status_widget:
                # 连接状态更新信号
                self.automation_scheduler.status_updated.connect(self.on_automation_status_updated)
                self.automation_scheduler.heartbeat_updated.connect(self.on_automation_heartbeat_updated)
                self.automation_scheduler.task_started.connect(self.on_automation_task_started)
                self.automation_scheduler.task_completed.connect(self.on_automation_task_completed)
                self.automation_scheduler.workflow_completed.connect(self.on_automation_workflow_completed)

                # 连接设置变更信号
                if hasattr(self.setting_tab, 'settings_saved'):
                    self.setting_tab.settings_saved.connect(self.on_automation_settings_changed)

                logging.info("自动化调度器信号连接完成")
        except Exception as e:
            logging.error(f"连接自动化信号失败: {str(e)}")

    def on_automation_status_updated(self, status_message):
        """处理自动化状态更新"""
        try:
            logging.info(f"自动化状态更新: {status_message}")

            # 更新心跳指示器状态
            if self.automation_status_widget:
                is_running = self.automation_scheduler.is_running if self.automation_scheduler else False
                current_task = self.automation_scheduler.current_task if self.automation_scheduler else None
                self.automation_status_widget.set_automation_status(is_running, current_task)

        except Exception as e:
            logging.error(f"处理自动化状态更新失败: {str(e)}")

    def on_automation_heartbeat_updated(self, heartbeat_state):
        """处理自动化心跳更新"""
        try:
            if self.automation_status_widget:
                self.automation_status_widget.update_heartbeat(heartbeat_state)
        except Exception as e:
            logging.error(f"处理自动化心跳更新失败: {str(e)}")

    def on_automation_task_started(self, task_name):
        """处理自动化任务开始"""
        try:
            logging.info(f"自动化任务开始: {task_name}")
            if self.automation_status_widget:
                self.automation_status_widget.set_automation_status(True, task_name)
        except Exception as e:
            logging.error(f"处理自动化任务开始失败: {str(e)}")

    def on_automation_task_completed(self, task_name, success):
        """处理自动化任务完成"""
        try:
            status = "成功" if success else "失败"
            logging.info(f"自动化任务完成: {task_name} - {status}")
        except Exception as e:
            logging.error(f"处理自动化任务完成失败: {str(e)}")

    def on_automation_workflow_completed(self, success):
        """处理自动化工作流程完成"""
        try:
            status = "成功" if success else "失败"
            logging.info(f"自动化工作流程完成: {status}")

            # 更新心跳指示器状态
            if self.automation_status_widget:
                is_running = self.automation_scheduler.is_running if self.automation_scheduler else False
                self.automation_status_widget.set_automation_status(is_running)

        except Exception as e:
            logging.error(f"处理自动化工作流程完成失败: {str(e)}")

    def on_automation_settings_changed(self, settings):
        """处理自动化设置变更"""
        try:
            if self.automation_scheduler and 'enable_automation' in settings:
                # 提取自动化相关设置
                automation_config = {
                    'enabled': settings.get('enable_automation', False),
                    'schedule_mode': self.map_schedule_mode(settings.get('schedule_mode', '间隔执行')),
                    'interval_hours': settings.get('interval_hours', 2),
                    'task_interval_minutes': settings.get('task_interval', 5),
                    'auto_retry': settings.get('auto_retry', True),
                    'max_retries': settings.get('max_retries', 3),
                    'loop_mode': 'continuous' if settings.get('loop_mode', '连续执行') == '连续执行' else 'count',
                    'loop_count': settings.get('loop_count', 1)
                }

                # 更新调度器配置
                self.automation_scheduler.update_config(automation_config)
                logging.info("自动化设置已更新")

        except Exception as e:
            logging.error(f"处理自动化设置变更失败: {str(e)}")

    def map_schedule_mode(self, mode_text):
        """映射调度模式文本到内部值"""
        mode_map = {
            '间隔执行': 'interval',
            '每日定时': 'daily',
            '自定义时间': 'custom'
        }
        return mode_map.get(mode_text, 'interval')

    def trigger_initial_earnings_update(self):
        """初始触发收益统计更新"""
        try:
            logging.info("触发初始收益统计更新...")
            if hasattr(self, 'account_tab') and self.account_tab:
                # 使用账号标签页的更新方法
                self.account_tab.update_total_earnings()
                logging.info("已触发账号标签页的收益统计更新")

            # 也使用主窗口的更新方法
            self.update_total_earnings()
            logging.info("已触发主窗口的收益统计更新")
        except Exception as e:
            logging.error(f"触发初始收益统计更新时出错: {str(e)}")

    def create_system_log_tab(self):
        """创建系统日志选项卡"""
        # 创建主容器
        log_widget = QWidget()
        main_layout = QVBoxLayout(log_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # 创建分割器，分为左右两部分
        splitter = QSplitter(1)  # 1 = Qt.Horizontal

        # 左半部分：进度监控区域
        progress_group = QGroupBox("📊 批量存稿进度监控")
        progress_layout = QVBoxLayout(progress_group)
        progress_group.setMinimumWidth(350)  # 设置进度区域最小宽度

        # 总体进度
        overall_layout = QHBoxLayout()
        overall_layout.addWidget(QLabel("总体进度:"))

        self.overall_progress_bar = QProgressBar()
        self.overall_progress_bar.setMinimum(0)
        self.overall_progress_bar.setMaximum(100)
        self.overall_progress_bar.setValue(0)
        self.overall_progress_bar.setFormat("0/0 (0%)")
        overall_layout.addWidget(self.overall_progress_bar)

        progress_layout.addLayout(overall_layout)

        # 运行时间显示
        runtime_layout = QHBoxLayout()
        runtime_layout.addWidget(QLabel("运行时间:"))

        self.runtime_label = QLabel("00:00:00")
        self.runtime_label.setStyleSheet("""
            QLabel {
                font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
                font-size: 14px;
                font-weight: bold;
                color: #1976d2;
                background-color: #f0f8ff;
                padding: 4px 8px;
                border-radius: 4px;
                border: 1px solid #e3f2fd;
            }
        """)
        runtime_layout.addWidget(self.runtime_label)
        runtime_layout.addStretch()  # 添加弹性空间

        progress_layout.addLayout(runtime_layout)

        # 账号进度区域（可滚动）
        self.account_progress_scroll = QScrollArea()
        self.account_progress_widget = QWidget()
        self.account_progress_layout = QVBoxLayout(self.account_progress_widget)
        self.account_progress_layout.setContentsMargins(5, 5, 5, 5)
        self.account_progress_layout.setSpacing(5)

        self.account_progress_scroll.setWidget(self.account_progress_widget)
        self.account_progress_scroll.setWidgetResizable(True)
        self.account_progress_scroll.setMinimumHeight(200)
        self.account_progress_scroll.setStyleSheet("""
            QScrollArea {
                border: 1px solid #cccccc;
                border-radius: 5px;
                background-color: #f9f9f9;
            }
        """)

        progress_layout.addWidget(self.account_progress_scroll)

        splitter.addWidget(progress_group)

        # 右半部分：日志显示区域
        log_group = QGroupBox("📋 系统日志")
        log_layout = QVBoxLayout(log_group)

        # 日志过滤控件
        filter_layout = QHBoxLayout()
        filter_layout.setContentsMargins(5, 5, 5, 5)

        # 过滤标签
        filter_label = QLabel("日志过滤:")
        # 根据夜间模式设置样式
        is_night_mode = self.property("nightMode")
        if is_night_mode:
            filter_label.setStyleSheet("font-weight: bold; color: #e0e0e0;")
        else:
            filter_label.setStyleSheet("font-weight: bold; color: #333333;")
        filter_layout.addWidget(filter_label)

        # 过滤单选按钮组
        self.log_filter_group = QButtonGroup()

        self.filter_all_radio = QRadioButton("全部")
        self.filter_all_radio.toggled.connect(self.apply_log_filter)
        self.log_filter_group.addButton(self.filter_all_radio, 0)
        filter_layout.addWidget(self.filter_all_radio)

        self.filter_success_radio = QRadioButton("仅成功")
        self.filter_success_radio.setChecked(True)  # 默认选择仅成功
        self.filter_success_radio.toggled.connect(self.apply_log_filter)
        self.log_filter_group.addButton(self.filter_success_radio, 1)
        filter_layout.addWidget(self.filter_success_radio)

        self.filter_failure_radio = QRadioButton("失败")
        self.filter_failure_radio.toggled.connect(self.apply_log_filter)
        self.log_filter_group.addButton(self.filter_failure_radio, 2)
        filter_layout.addWidget(self.filter_failure_radio)

        self.filter_statistics_radio = QRadioButton("统计")
        self.filter_statistics_radio.toggled.connect(self.apply_log_filter)
        self.log_filter_group.addButton(self.filter_statistics_radio, 3)
        filter_layout.addWidget(self.filter_statistics_radio)

        filter_layout.addStretch()
        log_layout.addLayout(filter_layout)

        # 创建垂直分割器用于主日志和失败日志
        log_splitter = QSplitter(2)  # 2 = Qt.Vertical

        # 主日志显示文本框
        self.log_text_edit = QTextEdit()
        self.log_text_edit.setReadOnly(True)
        self.log_text_edit.setMinimumWidth(400)  # 设置日志区域最小宽度
        self.log_text_edit.setMinimumHeight(200)  # 设置最小高度
        self.log_text_edit.setStyleSheet("""
            QTextEdit {
                background-color: #1e1e1e;
                color: #ffffff;
                border: 1px solid #3c3c3c;
                border-radius: 5px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
                padding: 5px;
            }
        """)
        log_splitter.addWidget(self.log_text_edit)

        # 失败日志专用区域
        failure_log_group = QGroupBox("❌ 失败日志")
        failure_log_layout = QVBoxLayout(failure_log_group)

        # 失败日志控制按钮
        failure_control_layout = QHBoxLayout()

        self.toggle_failure_log_btn = QPushButton("🔽 展开失败日志")
        self.toggle_failure_log_btn.clicked.connect(self.toggle_failure_log_area)
        self.toggle_failure_log_btn.setMaximumWidth(150)
        failure_control_layout.addWidget(self.toggle_failure_log_btn)

        self.clear_failure_log_btn = QPushButton("🗑️ 清空失败日志")
        self.clear_failure_log_btn.clicked.connect(self.clear_failure_log)
        self.clear_failure_log_btn.setMaximumWidth(120)
        failure_control_layout.addWidget(self.clear_failure_log_btn)

        failure_control_layout.addStretch()
        failure_log_layout.addLayout(failure_control_layout)

        # 失败日志显示文本框
        self.failure_log_text_edit = QTextEdit()
        self.failure_log_text_edit.setReadOnly(True)
        self.failure_log_text_edit.setMinimumHeight(150)
        self.failure_log_text_edit.setMaximumHeight(350)
        self.failure_log_text_edit.setStyleSheet("""
            QTextEdit {
                background-color: #2d1b1b;
                color: #ffcccc;
                border: 1px solid #8b0000;
                border-radius: 5px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
                padding: 5px;
            }
        """)
        self.failure_log_text_edit.setVisible(False)  # 默认隐藏
        failure_log_layout.addWidget(self.failure_log_text_edit)

        log_splitter.addWidget(failure_log_group)

        # 设置分割器初始比例 (主日志:失败日志 = 2:1)
        log_splitter.setSizes([300, 150])
        log_layout.addWidget(log_splitter)

        # 日志控制按钮
        log_button_layout = QHBoxLayout()

        self.clear_log_btn = QPushButton("🗑️ 清空日志")
        self.clear_log_btn.clicked.connect(self.clear_system_log)
        self.clear_log_btn.setMaximumWidth(120)
        log_button_layout.addWidget(self.clear_log_btn)

        self.auto_scroll_checkbox = QCheckBox("自动滚动")
        self.auto_scroll_checkbox.setChecked(True)
        log_button_layout.addWidget(self.auto_scroll_checkbox)

        log_button_layout.addStretch()
        log_layout.addLayout(log_button_layout)

        splitter.addWidget(log_group)

        # 初始化日志存储
        self.all_logs = []  # 存储所有日志条目
        self.failure_logs = []  # 存储失败日志条目
        self.current_filter = "success"  # 当前过滤器状态，默认显示仅成功

        # 设置分割器的初始比例 (左:右 = 2:3)
        splitter.setSizes([350, 500])

        main_layout.addWidget(splitter)

        # 初始化账号进度字典
        self.account_progress_bars = {}

        # 连接批量存稿信号
        self.connect_batch_signals()

        return log_widget

    def connect_batch_signals(self):
        """连接批量存稿相关信号"""
        try:
            # 连接账号标签页的批量存稿信号
            if hasattr(self.account_tab, 'batch_progress_updated'):
                self.account_tab.batch_progress_updated.connect(self.update_batch_progress)

            if hasattr(self.account_tab, 'batch_log_updated'):
                self.account_tab.batch_log_updated.connect(self.add_system_log)

            if hasattr(self.account_tab, 'account_progress_updated'):
                self.account_tab.account_progress_updated.connect(self.update_account_progress)

            # 连接全局日志系统
            self.connect_global_logger()

        except Exception as e:
            logging.error(f"连接批量存稿信号失败: {str(e)}")

    def connect_global_logger(self):
        """连接全局日志系统到系统日志选项卡"""
        try:
            from app.utils.logger import logger
            # 连接全局日志信号到系统日志显示
            logger.log_added.connect(self.add_global_log)
        except Exception as e:
            logging.error(f"连接全局日志系统失败: {str(e)}")

    def add_global_log(self, message, level):
        """添加全局日志到系统日志选项卡

        Args:
            message: 日志消息
            level: 日志级别 (DEBUG, INFO, SUCCESS, WARNING, ERROR, CRITICAL, ACCOUNT_FAIL)
        """
        try:
            # 将全局日志级别映射到系统日志格式
            level_mapping = {
                'DEBUG': 'INFO',
                'INFO': 'INFO',
                'SUCCESS': 'SUCCESS',
                'WARNING': 'WARNING',
                'ERROR': 'ERROR',
                'CRITICAL': 'ERROR',
                'ACCOUNT_FAIL': 'ERROR'
            }

            # 确定日志来源
            source = "系统"
            if "账号" in message:
                source = "账号管理"
            elif "批量" in message:
                source = "批量存稿"
            elif "设置" in message:
                source = "设置"
            elif "文件" in message or "目录" in message:
                source = "文件操作"
            elif "登录" in message or "卡密" in message:
                source = "登录验证"

            mapped_level = level_mapping.get(level, 'INFO')
            self.add_system_log(source, mapped_level, message)

        except Exception as e:
            logging.error(f"添加全局日志失败: {str(e)}")

    def enable_ui_logging(self):
        """启用UI日志输出（结束启动静默模式）"""
        try:
            from app.utils.logger import logger
            logger.enable_ui_logging()
        except Exception as e:
            logging.error(f"启用UI日志失败: {str(e)}")

    # 移除启动测试日志方法，保持启动时界面简洁

    def clear_system_log(self):
        """清空系统日志"""
        self.log_text_edit.clear()
        self.all_logs.clear()
        self.add_system_log("系统", "INFO", "日志已清空")

    def clear_failure_log(self):
        """清空失败日志"""
        self.failure_log_text_edit.clear()
        self.failure_logs.clear()
        self.add_failure_log("系统", "INFO", "失败日志已清空")

    def toggle_failure_log_area(self):
        """切换失败日志区域的显示/隐藏"""
        is_visible = self.failure_log_text_edit.isVisible()
        self.failure_log_text_edit.setVisible(not is_visible)

        if is_visible:
            self.toggle_failure_log_btn.setText("🔽 展开失败日志")
        else:
            self.toggle_failure_log_btn.setText("🔼 收起失败日志")

    def apply_log_filter(self):
        """应用日志过滤器"""
        try:
            # 确定当前选择的过滤器
            if self.filter_all_radio.isChecked():
                self.current_filter = "all"
            elif self.filter_success_radio.isChecked():
                self.current_filter = "success"
            elif self.filter_failure_radio.isChecked():
                self.current_filter = "failure"
            elif self.filter_statistics_radio.isChecked():
                self.current_filter = "statistics"

            # 重新显示日志
            self.refresh_log_display()

        except Exception as e:
            logging.error(f"应用日志过滤器失败: {str(e)}")

    def refresh_log_display(self):
        """根据当前过滤器刷新日志显示"""
        try:
            self.log_text_edit.clear()

            for log_entry in self.all_logs:
                source, level, message, formatted_entry = log_entry

                # 根据过滤器决定是否显示
                should_show = False

                if self.current_filter == "all":
                    should_show = True
                elif self.current_filter == "success":
                    # 只显示关键的成功操作日志
                    should_show = (level in ["SUCCESS", "INFO"] and
                                 any(keyword in message.lower() for keyword in [
                                     "存稿成功", "第", "次存稿成功", "视频上传成功", "封面上传成功",
                                     "登录成功", "✅ 视频上传", "✅ 封面上传", "✅ 登录成功",
                                     "存稿完成", "上传完成", "任务完成"
                                 ]))
                elif self.current_filter == "failure":
                    # 显示失败相关日志
                    should_show = (level in ["ERROR", "WARNING"] or
                                 any(keyword in message.lower() for keyword in
                                     ["失败", "错误", "异常", "error", "fail", "exception"]))
                elif self.current_filter == "statistics":
                    # 只显示统计相关日志
                    should_show = (level in ["SUCCESS", "INFO"] and
                                 any(keyword in message.lower() for keyword in [
                                     "📊 任务统计", "📊 存稿成功", "📊 发布成功", "📊 登录失败",
                                     "📊 存稿失败", "📊 发布失败", "📊 总账号", "任务统计",
                                     "成功:", "失败:", "总账号"
                                 ]))

                if should_show:
                    self.log_text_edit.append(formatted_entry)

            # 自动滚动到底部
            if self.auto_scroll_checkbox.isChecked():
                scrollbar = self.log_text_edit.verticalScrollBar()
                if scrollbar:
                    scrollbar.setValue(scrollbar.maximum())

        except Exception as e:
            logging.error(f"刷新日志显示失败: {str(e)}")

    def add_failure_log(self, source, level, message):
        """添加失败日志条目"""
        try:
            # 首先检查是否应该过滤掉这条日志
            if self._should_filter_failure_log(message):
                return

            # 简化失败日志格式
            simplified_message = self._simplify_failure_message(message)

            # 如果简化后的消息为空，说明这是不需要显示的冗余信息
            if not simplified_message:
                return

            # 再次检查简化后的消息是否应该过滤
            if self._should_filter_failure_log(simplified_message):
                return

            # 检查是否已经存在相同的失败日志，避免重复显示
            if self._is_duplicate_failure_log(simplified_message):
                return

            # 获取当前时间
            current_time = datetime.datetime.now().strftime("%H:%M:%S")

            # 格式化失败日志条目（红色高亮，简洁格式）
            log_entry = f'<span style="color: #ff6666;">[{current_time}]</span> ' \
                       f'<span style="color: #ffcccc;">{simplified_message}</span>'

            # 添加到失败日志显示区域
            self.failure_log_text_edit.append(log_entry)

            # 存储失败日志
            self.failure_logs.append((source, level, simplified_message, log_entry))

            # 如果有新的失败日志，自动展开失败日志区域
            if not self.failure_log_text_edit.isVisible():
                self.toggle_failure_log_area()

            # 自动滚动到底部
            scrollbar = self.failure_log_text_edit.verticalScrollBar()
            if scrollbar:
                scrollbar.setValue(scrollbar.maximum())

        except Exception as e:
            logging.error(f"添加失败日志失败: {str(e)}")

    def _simplify_failure_message(self, message):
        """简化失败日志消息格式 - 只显示真正的失败信息"""
        try:
            import re

            # 如果消息为空或过短，直接返回None
            if not message or len(message.strip()) < 3:
                return None

            # 检查是否已经是格式化的失败日志格式
            if "-" in message and "账号" in message:
                parts = message.split("-")
                if len(parts) >= 4:
                    # 检查是否是有效的账号ID（不是错误信息片段）
                    account_part = parts[-1]
                    if "账号" in account_part:
                        account_id = account_part.replace("账号", "").strip()
                        # 过滤掉无效的账号ID
                        invalid_ids = ["未知", "文件夹", "文件时出错", ""]
                        if account_id not in invalid_ids and not any(keyword in account_id for keyword in ["出错", "失败", "异常", "错误"]):
                            return message  # 返回有效的格式化日志
                    return None  # 过滤掉无效的格式化日志

            # 过滤掉不需要显示的冗余信息
            redundant_patterns = [
                r"工作窃取线程.*",
                r"线程.*已启动",
                r"线程.*已完成",
                r"线程.*浏览器.*",
                r"JavaScript点击结果.*",
                r"元素被遮挡，使用JavaScript点击",
                r"方法\d+.*查找.*",
                r"备用方案.*",
                r"等待.*元素.*",
                r"检测.*元素.*",
                r"滚动到.*",
                r"刷新页面.*",
                r"导航到.*",
                r"准备.*",
                r"开始.*",
                r"正在.*",
                r"查找.*元素.*",
                r"尝试.*方法.*",
                r"使用.*方式.*",
                r"通过.*检测.*",
                r".*处理中.*",
                r".*文件路径.*",
                r".*绝对路径.*",
                r"管理.*文件.*出错",
                r"文件备份失败",
                r"创建.*文件夹",
                r"删除.*文件夹"
            ]

            # 检查是否是冗余信息
            for pattern in redundant_patterns:
                if re.search(pattern, message, re.IGNORECASE):
                    return None  # 返回None表示不显示此日志

            # 检查是否是新的格式化失败日志格式：[失败类型]-[具体原因]-[处理结果]-[账号ID]
            if "-" in message and "账号" in message:
                parts = message.split("-")
                if len(parts) >= 4:
                    # 检查最后一部分是否包含有效的账号ID
                    account_part = parts[-1].strip()
                    if account_part.startswith("账号") and len(account_part) > 2:
                        account_id = account_part.replace("账号", "").strip()
                        # 如果账号ID有效（不是"未知"或包含错误信息），直接返回
                        invalid_ids = ["未知", "文件夹", "文件时出错", ""]
                        if account_id not in invalid_ids and not any(keyword in account_id for keyword in ["出错", "失败", "异常", "错误"]):
                            return message

            # 如果不是标准格式，转换为标准格式
            # 提取账号ID - 改进的正则表达式，更准确地匹配账号ID
            account_id_patterns = [
                r'账号\s*([a-zA-Z0-9_\-\.]+)',  # 匹配字母数字下划线横线点号
                r'账号\s*([^\s:：，,。！!？?]+)',  # 匹配非空白和标点符号
                r'\[([a-zA-Z0-9_\-\.]+)\]',     # 匹配方括号中的账号ID
            ]

            account_id = "未知"
            for pattern in account_id_patterns:
                match = re.search(pattern, message)
                if match:
                    potential_id = match.group(1).strip()
                    # 验证账号ID的有效性
                    if potential_id and len(potential_id) > 0 and not any(keyword in potential_id.lower() for keyword in ["出错", "失败", "异常", "错误", "未知"]):
                        account_id = potential_id
                        break

            # 根据消息内容分类失败类型和原因
            failure_type = "存稿失败"
            failure_reason = "未知错误"
            action = "终止存稿"

            # 登录相关失败
            if any(keyword in message.lower() for keyword in ["登录失败", "cookie", "登录验证失败", "登录异常"]):
                failure_type = "登录失败"
                if "cookie" in message.lower() and any(word in message.lower() for word in ["过期", "为空", "不存在", "失效"]):
                    failure_reason = "Cookie失效"
                elif "被封" in message.lower() or "封禁" in message.lower():
                    failure_reason = "账号被封"
                elif "未实名" in message.lower():
                    failure_type = "验证失败"
                    failure_reason = "未实名"
                else:
                    failure_reason = "认证失败"

            # 存稿相关失败
            elif any(keyword in message.lower() for keyword in ["存稿失败", "存稿按钮", "保存失败", "上传失败", "视频", "video"]):
                failure_type = "存稿失败"
                if "视频" in message.lower() or "上传" in message.lower():
                    failure_reason = "视频上传失败"
                else:
                    failure_reason = "发布失败"

            # 网络或页面相关失败
            elif any(keyword in message.lower() for keyword in ["网络", "超时", "连接", "页面", "元素", "导航"]):
                failure_type = "网络失败"
                failure_reason = "网络错误"

            # 验证相关失败
            elif any(keyword in message.lower() for keyword in ["验证", "实名", "认证"]):
                failure_type = "验证失败"
                # 根据具体内容确定失败原因
                if "未实名" in message.lower():
                    failure_reason = "未实名"
                elif "禁言" in message.lower():
                    failure_reason = "账号禁言"
                elif "验证码" in message.lower():
                    failure_reason = "验证码错误"
                else:
                    failure_reason = "认证失败"

            # 其他具体错误
            elif "异常" in message.lower() or "exception" in message.lower():
                failure_type = "存稿失败"
                failure_reason = "系统异常"

            # 返回标准格式：[失败类型]-[具体原因]-[处理结果]-[账号ID]
            return f"{failure_type}-{failure_reason}-{action}-账号{account_id}"

        except Exception as e:
            # 如果简化失败，返回标准格式的错误信息
            return "存稿失败-处理失败-终止存稿-账号未知"

    def _is_duplicate_failure_log(self, message):
        """检查是否是重复的失败日志"""
        try:
            # 检查最近的10条失败日志
            recent_logs = self.failure_logs[-10:] if len(self.failure_logs) >= 10 else self.failure_logs

            for _, _, existing_message, _ in recent_logs:
                if existing_message == message:
                    return True

            # 检查是否是错误的简化结果
            if self._is_incorrect_simplification(message, recent_logs):
                return True

            return False
        except Exception:
            return False

    def _is_incorrect_simplification(self, message, recent_logs):
        """检查是否是错误的简化结果"""
        try:
            # 如果当前消息是"验证失败-验证码错误"，但最近有"验证失败-未实名"，则过滤掉
            if "验证失败-验证码错误" in message:
                for _, _, existing_message, _ in recent_logs:
                    if "验证失败-未实名" in existing_message:
                        return True

            # 如果当前消息是"验证失败-验证码错误"，但最近有"验证失败-账号禁言"，则过滤掉
            if "验证失败-验证码错误" in message:
                for _, _, existing_message, _ in recent_logs:
                    if "验证失败-账号禁言" in existing_message:
                        return True

            return False
        except Exception:
            return False

    def _should_filter_failure_log(self, message):
        """检查是否应该过滤掉这条失败日志"""
        try:
            # 过滤掉明显无关的日志
            filter_keywords = [
                "管理失败账号文件时出错",
                "文件备份失败，但不影响主流程继续",
                "创建失败账号文件夹",
                "删除已存在的失败账号文件夹",
                "工作窃取线程",
                "线程 #",
                "JavaScript点击结果",
                "等待元素出现",
                "检测元素状态",
                "准备上传文件",
                "开始处理账号",
                "浏览器已关闭",
                "信号连接",
                "定时器",
                "QTimer",
                "QObject"
            ]

            # 检查是否包含过滤关键词
            for keyword in filter_keywords:
                if keyword in message:
                    return True

            # 过滤掉账号ID无效的日志
            if "账号未知" in message or "账号文件夹" in message or "账号文件时出错" in message:
                return True

            # 过滤掉已经格式化的失败日志的原始消息
            if self._is_raw_message_after_formatted_log(message):
                return True

            # 过滤掉禁言检测后产生的冗余日志
            if self._is_redundant_after_ban_detection(message):
                return True

            return False
        except Exception:
            return False

    def _is_raw_message_after_formatted_log(self, message):
        """检查是否是格式化失败日志后的原始消息"""
        try:
            # 检查最近是否有格式化的失败日志
            recent_logs = self.failure_logs[-3:] if len(self.failure_logs) >= 3 else self.failure_logs

            # 如果最近有格式化的失败日志，过滤掉可能的原始异常消息
            for _, _, existing_message, _ in recent_logs:
                if any(pattern in existing_message for pattern in ["验证失败-", "登录失败-", "存稿失败-"]):
                    # 如果当前消息是原始的异常消息，则过滤掉
                    if any(raw_pattern in message for raw_pattern in [
                        "未实名认证",
                        "账号禁言",
                        "Cookie失效",
                        "处理异常",
                        "系统异常"
                    ]) and not any(formatted_pattern in message for formatted_pattern in ["验证失败-", "登录失败-", "存稿失败-"]):
                        return True

            return False
        except Exception:
            return False

    def _is_redundant_after_ban_detection(self, message):
        """检查是否是禁言检测后的冗余日志"""
        try:
            # 检查最近是否有禁言日志
            recent_logs = self.failure_logs[-5:] if len(self.failure_logs) >= 5 else self.failure_logs
            has_recent_ban = any("禁言" in log[2] for log in recent_logs)

            if has_recent_ban:
                # 如果最近有禁言日志，过滤掉以下类型的冗余日志
                redundant_patterns = [
                    "存稿失败-未知错误",
                    "存稿失败-发布失败",
                    "存稿失败-操作异常",
                    "存稿失败-系统异常",
                    "登录失败-认证失败"
                ]

                for pattern in redundant_patterns:
                    if pattern in message:
                        return True

            # 检查最近是否有未实名日志
            has_recent_unverified = any("未实名" in log[2] for log in recent_logs)

            if has_recent_unverified:
                # 如果最近有未实名日志，过滤掉以下类型的冗余日志
                unverified_redundant_patterns = [
                    "登录失败-Cookie失效",
                    "登录失败-认证失败",
                    "存稿失败-未知错误",
                    "存稿失败-发布失败",
                    "存稿失败-操作异常",
                    "存稿失败-系统异常"
                ]

                for pattern in unverified_redundant_patterns:
                    if pattern in message:
                        return True

            return False
        except Exception:
            return False

    def add_system_log(self, source, level, message):
        """添加系统日志条目"""
        try:
            # 获取当前时间
            current_time = datetime.datetime.now().strftime("%H:%M:%S")

            # 根据日志级别设置颜色
            color_map = {
                "INFO": "#00ff00",      # 绿色
                "WARNING": "#ffff00",   # 黄色
                "ERROR": "#ff0000",     # 红色
                "SUCCESS": "#00ffff",   # 青色
                "DEBUG": "#cccccc"      # 灰色
            }

            color = color_map.get(level, "#ffffff")

            # 格式化日志条目
            log_entry = f'<span style="color: #888888;">[{current_time}]</span> ' \
                       f'<span style="color: {color};">[{level}]</span> ' \
                       f'<span style="color: #ffffff;">[{source}]</span> ' \
                       f'<span style="color: #ffffff;">{message}</span>'

            # 存储到所有日志列表
            self.all_logs.append((source, level, message, log_entry))

            # 检查是否是失败日志，如果是则同时添加到失败日志
            is_failure_log = (level in ["ERROR", "WARNING"] or
                             any(keyword in message.lower() for keyword in
                                 ["失败", "错误", "异常", "error", "fail", "exception"]))

            if is_failure_log:
                self.add_failure_log(source, level, message)

            # 根据当前过滤器决定是否显示在主日志区域
            should_show = False

            if self.current_filter == "all":
                should_show = True
            elif self.current_filter == "success":
                # 只显示关键的成功操作日志
                should_show = (level in ["SUCCESS", "INFO"] and
                             any(keyword in message.lower() for keyword in [
                                 "存稿成功", "第", "次存稿成功", "视频上传成功", "封面上传成功",
                                 "登录成功", "✅ 视频上传", "✅ 封面上传", "✅ 登录成功",
                                 "存稿完成", "上传完成", "任务完成"
                             ]))
            elif self.current_filter == "failure":
                # 显示失败相关日志
                should_show = is_failure_log
            elif self.current_filter == "statistics":
                # 只显示统计相关日志
                should_show = (level in ["SUCCESS", "INFO"] and
                             any(keyword in message.lower() for keyword in [
                                 "📊 任务统计", "📊 存稿成功", "📊 发布成功", "📊 登录失败",
                                 "📊 存稿失败", "📊 发布失败", "📊 总账号", "任务统计",
                                 "成功:", "失败:", "总账号"
                             ]))

            if should_show:
                # 添加到日志显示区域
                self.log_text_edit.append(log_entry)

                # 自动滚动到底部
                if self.auto_scroll_checkbox.isChecked():
                    scrollbar = self.log_text_edit.verticalScrollBar()
                    if scrollbar:
                        scrollbar.setValue(scrollbar.maximum())

        except Exception as e:
            logging.error(f"添加系统日志失败: {str(e)}")

    def update_batch_progress(self, current, total):
        """更新总体批量存稿进度"""
        try:
            if total > 0:
                percentage = int((current / total) * 100)
                self.overall_progress_bar.setValue(percentage)
                self.overall_progress_bar.setFormat(f"{current}/{total} ({percentage}%)")
            else:
                self.overall_progress_bar.setValue(0)
                self.overall_progress_bar.setFormat("0/0 (0%)")

            # 添加进度日志
            self.add_system_log("批量存稿", "INFO", f"总体进度更新: {current}/{total}")

        except Exception as e:
            logging.error(f"更新批量存稿进度失败: {str(e)}")

    def update_account_progress(self, account_id, status, progress=0):
        """更新单个账号的进度"""
        try:
            # 如果账号进度条不存在，创建一个
            if account_id not in self.account_progress_bars:
                self.create_account_progress_bar(account_id)

            # 更新进度条
            progress_info = self.account_progress_bars[account_id]
            progress_bar = progress_info['progress_bar']
            status_label = progress_info['status_label']
            progress_text = progress_info['progress_text']

            # 更新状态标签
            status_label.setText(status)

            # 更新进度条值
            progress_bar.setValue(progress)

            # 更新进度文本
            progress_text.setText(f"{progress}%")

            # 根据状态设置蓝白色配色
            if status in ["已完成", "成功", "存稿完成"]:
                # 成功状态 - 深蓝色
                status_label.setStyleSheet("""
                    QLabel {
                        font-family: 'Microsoft YaHei';
                        font-size: 9px;
                        color: #ffffff;
                        background-color: #1976d2;
                        padding: 1px 3px;
                        border-radius: 6px;
                    }
                """)
                progress_bar.setStyleSheet("""
                    QProgressBar {
                        border: none;
                        border-radius: 4px;
                        background-color: #f5f5f5;
                    }
                    QProgressBar::chunk {
                        background-color: #1976d2;
                        border-radius: 4px;
                    }
                """)
            elif status in ["失败", "错误", "存稿失败", "登录失败"]:
                # 失败状态 - 浅红色
                status_label.setStyleSheet("""
                    QLabel {
                        font-family: 'Microsoft YaHei';
                        font-size: 9px;
                        color: #d32f2f;
                        background-color: #ffebee;
                        padding: 1px 3px;
                        border-radius: 6px;
                    }
                """)
                progress_bar.setStyleSheet("""
                    QProgressBar {
                        border: none;
                        border-radius: 4px;
                        background-color: #f5f5f5;
                    }
                    QProgressBar::chunk {
                        background-color: #f44336;
                        border-radius: 4px;
                    }
                """)
            elif status in ["登录中", "存稿中", "处理中", "正在存稿"]:
                # 进行中状态 - 蓝色
                status_label.setStyleSheet("""
                    QLabel {
                        font-family: 'Microsoft YaHei';
                        font-size: 9px;
                        color: #1976d2;
                        background-color: #e3f2fd;
                        padding: 1px 3px;
                        border-radius: 6px;
                    }
                """)
                progress_bar.setStyleSheet("""
                    QProgressBar {
                        border: none;
                        border-radius: 4px;
                        background-color: #f5f5f5;
                    }
                    QProgressBar::chunk {
                        background-color: #2196f3;
                        border-radius: 4px;
                    }
                """)
            else:
                # 等待状态 - 浅蓝色
                status_label.setStyleSheet("""
                    QLabel {
                        font-family: 'Microsoft YaHei';
                        font-size: 9px;
                        color: #666666;
                        background-color: #f0f8ff;
                        padding: 1px 3px;
                        border-radius: 6px;
                    }
                """)
                progress_bar.setStyleSheet("""
                    QProgressBar {
                        border: none;
                        border-radius: 4px;
                        background-color: #f5f5f5;
                    }
                    QProgressBar::chunk {
                        background-color: #90caf9;
                        border-radius: 4px;
                    }
                """)

            # 添加账号进度日志
            self.add_system_log("账号进度", "INFO", f"账号 {account_id}: {status} ({progress}%)")

        except Exception as e:
            logging.error(f"更新账号进度失败: {str(e)}")

    def create_account_progress_bar(self, account_id):
        """为账号创建紧凑的蓝白色进度条"""
        try:
            # 创建账号进度容器
            account_widget = QWidget()
            account_widget.setFixedHeight(32)  # 增加高度，确保内容不被挤压
            account_widget.setStyleSheet("""
                QWidget {
                    background-color: #ffffff;
                    border: 1px solid #e3f2fd;
                    border-radius: 4px;
                    margin: 1px;
                }
                QWidget:hover {
                    border: 1px solid #2196f3;
                }
            """)

            account_layout = QHBoxLayout(account_widget)
            account_layout.setContentsMargins(8, 3, 15, 3)  # 增加右边距到15px，确保右侧内容不被挤压
            account_layout.setSpacing(10)  # 增加间距到10px

            # 账号标签 - 增加宽度，确保账号信息完整显示
            account_label = QLabel(f"账号{account_id}")
            account_label.setMinimumWidth(80)  # 增加最小宽度
            account_label.setMaximumWidth(120)  # 设置最大宽度，避免过长
            account_label.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Fixed)
            account_label.setStyleSheet("""
                QLabel {
                    font-family: 'Microsoft YaHei';
                    font-size: 11px;
                    color: #1976d2;
                    font-weight: bold;
                    padding: 2px 4px;
                }
            """)
            account_layout.addWidget(account_label)

            # 进度条 - 设置合理的拉伸策略
            progress_bar = QProgressBar()
            progress_bar.setMinimum(0)
            progress_bar.setMaximum(100)
            progress_bar.setValue(0)
            progress_bar.setFixedHeight(10)  # 稍微增大进度条高度
            progress_bar.setMinimumWidth(80)  # 减少最小宽度，为右侧组件腾出空间
            progress_bar.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)  # 允许水平拉伸
            progress_bar.setTextVisible(False)
            progress_bar.setStyleSheet("""
                QProgressBar {
                    border: none;
                    border-radius: 5px;
                    background-color: #f5f5f5;
                }
                QProgressBar::chunk {
                    background-color: #2196f3;
                    border-radius: 5px;
                }
            """)
            account_layout.addWidget(progress_bar, 1)  # 设置拉伸因子为1

            # 状态标签 - 增加宽度，确保状态信息完整显示
            from PyQt5.QtCore import Qt
            status_label = QLabel("等待中")
            status_label.setMinimumWidth(65)  # 增加最小宽度
            status_label.setMaximumWidth(85)  # 增加最大宽度
            status_label.setAlignment(Qt.AlignCenter)  # 使用正确的Qt常量
            status_label.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Fixed)
            status_label.setStyleSheet("""
                QLabel {
                    font-family: 'Microsoft YaHei';
                    font-size: 10px;
                    color: #666666;
                    background-color: #f0f8ff;
                    padding: 2px 4px;
                    border-radius: 8px;
                }
            """)
            account_layout.addWidget(status_label)

            # 进度文本 - 增加宽度
            progress_text = QLabel("0%")
            progress_text.setMinimumWidth(40)  # 增加最小宽度
            progress_text.setMaximumWidth(50)  # 增加最大宽度
            progress_text.setAlignment(Qt.AlignCenter)  # 使用正确的Qt常量
            progress_text.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Fixed)
            progress_text.setStyleSheet("""
                QLabel {
                    font-family: 'Microsoft YaHei';
                    font-size: 10px;
                    color: #1976d2;
                    font-weight: bold;
                    padding: 2px;
                }
            """)
            account_layout.addWidget(progress_text)

            # 添加到滚动区域
            self.account_progress_layout.addWidget(account_widget)

            # 保存引用
            self.account_progress_bars[account_id] = {
                'widget': account_widget,
                'account_label': account_label,
                'status_label': status_label,
                'progress_bar': progress_bar,
                'progress_text': progress_text
            }

        except Exception as e:
            logging.error(f"创建账号进度条失败: {str(e)}")

    def clear_account_progress(self):
        """清空所有账号进度条"""
        try:
            # 清除所有子控件
            for i in reversed(range(self.account_progress_layout.count())):
                item = self.account_progress_layout.itemAt(i)
                if item:
                    child = item.widget()
                    if child:
                        child.setParent(None)

            # 清空字典
            self.account_progress_bars.clear()

        except Exception as e:
            logging.error(f"清空账号进度条失败: {str(e)}")

    def open_toutiao_spider(self):
        """打开头条视频爬虫对话框"""
        try:
            # 打开头条视频爬虫对话框
            dialog = open_toutiao_spider_dialog(self)

            # 保存引用，避免被垃圾回收
            self.spider_dialog = dialog

            logging.info("已打开头条视频爬虫对话框")
        except Exception as e:
            logging.error(f"打开头条视频爬虫对话框失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"打开头条视频爬虫对话框失败: {str(e)}")



    def open_proxy_settings(self):
        """打开代理设置对话框"""
        try:
            # 打开代理设置对话框
            result = open_proxy_settings_dialog(self)

            if result:
                logging.info("代理设置已更新")
                # 显示提示
                QMessageBox.information(self, "提示", "代理设置已更新，将在下次操作时生效。")
            else:
                logging.info("用户取消了代理设置")
        except Exception as e:
            logging.error(f"打开代理设置对话框失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"打开代理设置对话框失败: {str(e)}")

    def open_video_downloader(self):
        """打开视频下载工具对话框"""
        try:
            # 显示状态栏消息，提示用户窗口正在加载
            status_bar = self.statusBar()
            if status_bar:
                status_bar.showMessage("正在打开视频下载工具窗口...", 3000)

            # 禁用按钮，避免重复点击
            if hasattr(self, 'video_downloader_btn'):
                self.video_downloader_btn.setEnabled(False)
                self.video_downloader_btn.setText("正在加载...")

            # 打开头条视频爬虫对话框（视频下载工具）
            dialog = open_toutiao_spider_dialog(self)

            # 保存引用，避免被垃圾回收
            self.toutiao_spider_dialog = dialog

            # 恢复按钮状态
            def restore_button():
                if hasattr(self, 'video_downloader_btn'):
                    self.video_downloader_btn.setEnabled(True)
                    self.video_downloader_btn.setText("视频下载工具")

                # 清除状态栏消息
                if status_bar:
                    status_bar.clearMessage()

            # 使用QTimer延迟恢复按钮状态
            self.create_single_shot_timer(1000, restore_button)

            logging.info("已打开视频下载工具对话框")
        except Exception as e:
            logging.error(f"打开视频下载工具对话框失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"打开视频下载工具对话框失败: {str(e)}")

            # 确保按钮恢复可用状态
            if hasattr(self, 'video_downloader_btn'):
                self.video_downloader_btn.setEnabled(True)
                self.video_downloader_btn.setText("视频下载工具")

    def open_material_selection(self):
        """打开素材选择对话框"""
        try:
            # 显示状态栏消息，提示用户窗口正在加载
            status_bar = self.statusBar()
            if status_bar:
                status_bar.showMessage("正在打开素材选择窗口...", 3000)

            # 禁用按钮，避免重复点击
            if hasattr(self, 'material_select_btn'):
                self.material_select_btn.setEnabled(False)
                self.material_select_btn.setText("正在加载...")

            # 打开素材选择对话框
            dialog = open_material_selection_dialog(self)

            # 保存引用，避免被垃圾回收
            self.material_selection_dialog = dialog

            # 恢复按钮状态
            def restore_button():
                if hasattr(self, 'material_select_btn'):
                    self.material_select_btn.setEnabled(True)
                    self.material_select_btn.setText("素材选择")

                # 清除状态栏消息
                if status_bar:
                    status_bar.clearMessage()

            # 使用QTimer延迟恢复按钮状态
            self.create_single_shot_timer(1000, restore_button)

            logging.info("已打开素材选择对话框")
        except Exception as e:
            logging.error(f"打开素材选择对话框失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"打开素材选择对话框失败: {str(e)}")

            # 确保按钮恢复可用状态
            if hasattr(self, 'material_select_btn'):
                self.material_select_btn.setEnabled(True)
                self.material_select_btn.setText("素材选择")

    def open_software_tutorial(self):
        """打开软件使用教程"""
        try:
            # 显示状态栏消息，提示用户正在打开教程
            status_bar = self.statusBar()
            if status_bar:
                status_bar.showMessage("正在打开软件使用教程...", 3000)

            # 禁用按钮，避免重复点击
            if hasattr(self, 'tutorial_btn'):
                self.tutorial_btn.setEnabled(False)
                self.tutorial_btn.setText("正在打开...")

            # 使用内置浏览器打开教程链接
            tutorial_url = "https://x0a8ja29q2i.feishu.cn/docx/W0h3dqtHro1pNexQvPjcl6wznxd"

            # 导入内置浏览器对话框
            from app.dialogs.web_browser_dialog import WebBrowserDialog

            # 创建并显示内置浏览器
            self.tutorial_browser = WebBrowserDialog(
                parent=self,
                url=tutorial_url,
                title="软件使用教程 - 内置浏览器",
                content_type="software_tutorial"
            )
            self.tutorial_browser.show()

            # 恢复按钮状态
            def restore_button():
                if hasattr(self, 'tutorial_btn'):
                    self.tutorial_btn.setEnabled(True)
                    self.tutorial_btn.setText("📖 软件使用教程")

                # 清除状态栏消息
                if status_bar:
                    status_bar.showMessage("软件使用教程已在内置浏览器中打开", 3000)

            # 使用QTimer延迟恢复按钮状态
            self.create_single_shot_timer(500, restore_button)

            logging.info("已在内置浏览器中打开软件使用教程")
        except Exception as e:
            logging.error(f"打开软件使用教程失败: {str(e)}")

            # 如果内置浏览器失败，回退到外部浏览器
            try:
                import webbrowser
                tutorial_url = "https://x0a8ja29q2i.feishu.cn/docx/W0h3dqtHro1pNexQvPjcl6wznxd"
                webbrowser.open(tutorial_url)

                if status_bar:
                    status_bar.showMessage("已使用外部浏览器打开教程", 3000)
                logging.info("已回退到外部浏览器打开教程")
            except Exception as fallback_error:
                QMessageBox.warning(self, "打开失败",
                    f"内置浏览器打开失败: {str(e)}\n外部浏览器也打开失败: {str(fallback_error)}")

            # 确保按钮恢复可用状态
            if hasattr(self, 'tutorial_btn'):
                self.tutorial_btn.setEnabled(True)
                self.tutorial_btn.setText("📖 软件使用教程")

            # 清除状态栏消息
            if status_bar:
                status_bar.clearMessage()

    def open_video_processor(self):
        """打开视频处理对话框"""
        try:
            # 显示状态栏消息，提示用户窗口正在加载
            status_bar = self.statusBar()
            if status_bar:
                status_bar.showMessage("正在打开视频处理窗口...", 3000)

            # 禁用按钮，避免重复点击
            if hasattr(self, 'video_processor_btn'):
                self.video_processor_btn.setEnabled(False)
                self.video_processor_btn.setText("正在加载...")

            # 打开视频处理对话框
            dialog = VideoProcessorDialog(self)
            dialog.show()

            # 保存引用，避免被垃圾回收
            self.video_processor_dialog = dialog

            # 恢复按钮状态
            def restore_button():
                if hasattr(self, 'video_processor_btn'):
                    self.video_processor_btn.setEnabled(True)
                    self.video_processor_btn.setText("视频处理")

                # 清除状态栏消息
                if status_bar:
                    status_bar.clearMessage()

            # 使用QTimer延迟恢复按钮状态
            self.create_single_shot_timer(1000, restore_button)

            logging.info("已打开视频处理对话框")
        except Exception as e:
            logging.error(f"打开视频处理对话框失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"打开视频处理对话框失败: {str(e)}")

            # 确保按钮恢复可用状态
            if hasattr(self, 'video_processor_btn'):
                self.video_processor_btn.setEnabled(True)
                self.video_processor_btn.setText("视频处理")

            # 清除状态栏消息
            if status_bar:
                status_bar.clearMessage()

    def open_account_nurture(self):
        """打开账号养号功能"""
        try:
            # 导入养号对话框
            from app.dialogs.nurture_dialog import NurtureSettingsDialog

            # 创建养号设置对话框
            nurture_dialog = NurtureSettingsDialog(self)

            # 默认选择所有账号选项
            nurture_dialog.select_all_accounts.setChecked(True)

            # 显示对话框并获取结果
            if nurture_dialog.exec_():
                # 用户点击了确定，获取养号设置
                settings = nurture_dialog.get_settings()

                # 确定要养号的账号列表
                selected_accounts = []

                # 获取所有账号
                try:
                    selected_accounts = self.account_tab.get_all_accounts()
                    logging.info(f"将使用所有账号进行养号，共 {len(selected_accounts)} 个账号")
                except Exception as e:
                    logging.error(f"获取所有账号失败: {str(e)}")
                    QMessageBox.warning(self, "警告", f"获取所有账号失败: {str(e)}")
                    return

                # 如果没有账号，提示用户
                if not selected_accounts:
                    QMessageBox.warning(self, "提示", "没有可用的账号进行养号！请先添加账号。")
                    return

                # 导入养号线程
                from app.modules.account_nurture import NurtureThread

                # 创建养号线程
                nurture_thread = NurtureThread(selected_accounts, settings)

                # 连接信号 - 系统日志功能已移除
                nurture_thread.error_message.connect(self.on_nurture_error)

                # 保存引用，避免被垃圾回收
                self.nurture_thread = nurture_thread

                # 启动养号线程
                nurture_thread.start()

                logging.info("已启动账号养号任务")
                QMessageBox.information(self, "提示", "账号养号任务已启动，请查看日志了解进度。")

                # 切换到日志标签页，方便用户查看进度
                if hasattr(self, 'tab_widget'):
                    for i in range(self.tab_widget.count()):
                        if self.tab_widget.tabText(i) == "日志":
                            self.tab_widget.setCurrentIndex(i)
                            break

        except Exception as e:
            logging.error(f"打开账号养号功能失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"打开账号养号功能失败: {str(e)}")

    def on_nurture_error(self, title, message):
        """处理养号任务错误

        Args:
            title: 错误标题
            message: 错误信息
        """
        QMessageBox.critical(self, title, message)
        logging.error(f"养号任务错误: {title} - {message}")

    def kill_browser_processes(self):
        """强制关闭所有Chrome浏览器进程"""
        try:
            # 显示确认对话框
            reply = QMessageBox.question(
                self,
                "关闭浏览器进程",
                "确定要强制关闭所有Chrome浏览器进程吗？\n这可能会导致正在运行的浏览器任务中断。",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # 显示进度提示
                status_bar = self.statusBar()
                if status_bar:
                    status_bar.showMessage("正在关闭浏览器进程，请稍候...", 0)

                # 禁用按钮，避免重复点击
                self.kill_browser_btn.setEnabled(False)
                self.kill_browser_btn.setText("正在关闭...")

                # 使用QTimer延迟执行操作，不阻塞主线程
                def do_kill_processes():
                    """执行进程关闭操作"""
                    try:
                        # 刷新UI
                        QApplication.processEvents()

                        # 导入psutil
                        import psutil

                        # 关闭Chrome进程
                        killed_count = 0
                        for proc in psutil.process_iter(['pid', 'name']):
                            try:
                                try:
                                    proc_info = proc.as_dict(['pid', 'name'])
                                except Exception:
                                    continue
                                if 'chrome' in proc_info['name'].lower():
                                    try:
                                        proc.kill()
                                        killed_count += 1
                                        # 每关闭5个进程刷新一次UI
                                        if killed_count % 5 == 0:
                                            QApplication.processEvents()
                                    except Exception:
                                        pass
                            except Exception:
                                pass

                        # 刷新UI
                        QApplication.processEvents()

                        # 在主线程中显示结果
                        def show_result():
                            if killed_count > 0:
                                QMessageBox.information(
                                    self,
                                    "操作完成",
                                    f"成功关闭了 {killed_count} 个Chrome浏览器进程。"
                                )
                            else:
                                QMessageBox.information(
                                    self,
                                    "操作完成",
                                    "没有找到需要关闭的Chrome浏览器进程。"
                                )

                            # 恢复状态栏和按钮状态
                            if status_bar:
                                status_bar.clearMessage()
                            self.kill_browser_btn.setEnabled(True)
                            self.kill_browser_btn.setText("关闭浏览器进程")

                        # 使用QTimer确保在主线程中执行UI更新
                        self.create_single_shot_timer(0, show_result)

                    except Exception as e:
                        logging.error(f"关闭浏览器进程出错: {str(e)}")

                        # 在主线程中显示错误
                        def show_error():
                            QMessageBox.warning(self, "错误", f"关闭浏览器进程时出错: {str(e)}")
                            # 恢复状态栏和按钮状态
                            if status_bar:
                                status_bar.clearMessage()
                            self.kill_browser_btn.setEnabled(True)
                            self.kill_browser_btn.setText("关闭浏览器进程")

                        # 使用QTimer确保在主线程中执行UI更新
                        self.create_single_shot_timer(0, show_error)

                # 延迟100ms执行操作，确保UI更新
                self.create_single_shot_timer(100, do_kill_processes)

        except Exception as e:
            logging.error(f"启动关闭浏览器进程任务时出错: {str(e)}")
            QMessageBox.warning(self, "错误", f"关闭浏览器进程时出错: {str(e)}")
            status_bar = self.statusBar()
            if status_bar:
                status_bar.clearMessage()
            # 确保按钮恢复可用状态
            self.kill_browser_btn.setEnabled(True)
            self.kill_browser_btn.setText("关闭浏览器进程")

    def clean_browser_cache(self):
        """清理浏览器缓存，释放C盘空间"""
        try:
            # 显示确认对话框
            reply = QMessageBox.question(
                self,
                "清理缓存",
                "确定要清理C盘浏览器缓存吗？\n这将删除临时文件夹中的Chrome和Selenium相关文件。",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # 显示进度提示
                status_bar = self.statusBar()
                if status_bar:
                    status_bar.showMessage("正在清理缓存，请稍候...", 0)

                # 禁用清理按钮，避免重复点击
                self.clean_cache_btn.setEnabled(False)
                self.clean_cache_btn.setText("正在清理...")

                # 使用QTimer延迟执行清理操作，不阻塞主线程
                from app.utils.browser_utils import clean_browser_cache

                def do_cleanup():
                    """执行清理操作"""
                    try:
                        # 刷新UI
                        QApplication.processEvents()
                        # 执行清理
                        cleaned_count, cleaned_size = clean_browser_cache()
                        # 刷新UI
                        QApplication.processEvents()

                        # 在主线程中显示结果
                        def show_result():
                            if cleaned_count > 0:
                                QMessageBox.information(
                                    self,
                                    "清理完成",
                                    f"成功清理了 {cleaned_count} 个缓存项，共释放 {cleaned_size:.2f}MB 空间。"
                                )
                            else:
                                QMessageBox.information(
                                    self,
                                    "清理完成",
                                    "没有找到需要清理的缓存文件，或清理过程中出现错误。"
                                )

                            # 恢复状态栏和按钮状态
                            if status_bar:
                                status_bar.clearMessage()
                            self.clean_cache_btn.setEnabled(True)
                            self.clean_cache_btn.setText("清理C盘缓存")

                        # 使用QTimer确保在主线程中执行UI更新
                        self.create_single_shot_timer(0, show_result)

                    except Exception as e:
                        logging.error(f"清理缓存出错: {str(e)}")

                        # 在主线程中显示错误
                        def show_error():
                            QMessageBox.warning(self, "错误", f"清理缓存时出错: {str(e)}")
                            # 恢复状态栏和按钮状态
                            if status_bar:
                                status_bar.clearMessage()
                            self.clean_cache_btn.setEnabled(True)
                            self.clean_cache_btn.setText("清理C盘缓存")

                        # 使用QTimer确保在主线程中执行UI更新
                        self.create_single_shot_timer(0, show_error)

                # 延迟100ms执行清理，确保UI更新
                self.create_single_shot_timer(100, do_cleanup)

        except Exception as e:
            logging.error(f"启动清理缓存任务时出错: {str(e)}")
            QMessageBox.warning(self, "错误", f"清理缓存时出错: {str(e)}")
            status_bar = self.statusBar()
            if status_bar:
                status_bar.clearMessage()
            # 确保按钮恢复可用状态
            self.clean_cache_btn.setEnabled(True)
            self.clean_cache_btn.setText("清理C盘缓存")

    def check_for_updates(self):
        """检查软件更新（用户手动触发）"""
        # 如果已经在初始化进度对话框中检测过更新，则不再重复检测
        if hasattr(self, 'update_checked') and self.update_checked:
            logging.info("已在初始化进度对话框中检测过更新，不再重复检测")
            return

        try:
            logging.info("开始检查软件更新")
            # 使用KamiManager的check_for_updates方法
            self.kami_manager.check_for_updates()

            # 标记已检测过更新
            self.update_checked = True
        except Exception as e:
            logging.error(f"检查更新时出错: {str(e)}")

    def check_for_updates_background(self):
        """在后台检查软件更新（系统自动触发）"""
        try:
            # 如果已经检查过更新，则不再重复检测
            # 注释：此检查可以被main.py中的代码覆盖，确保更新检测一定会执行
            if hasattr(self, 'update_checked') and self.update_checked:
                logging.info("已检测过更新，不再在后台重复检测")
                return

            logging.info("开始在后台检查软件更新")

            # 导入必要的模块
            from app.utils.kamidenglu import APP_VERSION, get_server_update_info
            from PyQt5.QtCore import QThread, pyqtSignal

            # 创建更新检查线程
            class UpdateCheckThread(QThread):
                update_found = pyqtSignal(dict)
                check_completed = pyqtSignal(bool, str)

                def run(self):
                    try:
                        # 获取服务器版本信息
                        logging.info("正在获取服务器版本信息...")
                        result = get_server_update_info()
                        if result is None:
                            success, server_version = False, "获取更新信息失败"
                        else:
                            success, server_version = result

                        if not success:
                            logging.warning(f"检查更新失败: {server_version}")
                            self.check_completed.emit(False, f"检查更新失败: {server_version}")
                            return

                        logging.info(f"获取到服务器版本: {server_version}, 当前版本: {APP_VERSION}")

                        # 比较版本号
                        if server_version != APP_VERSION:
                            logging.info(f"发现新版本: {server_version}")
                            update_info = {
                                "status": True,
                                "message": f"发现新版本: {server_version}",
                                "current_version": APP_VERSION,
                                "server_version": server_version,
                                "has_update": True
                            }
                            self.update_found.emit(update_info)
                            self.check_completed.emit(True, f"发现新版本: {server_version}")
                        else:
                            logging.info("当前已是最新版本")
                            self.check_completed.emit(True, "已是最新版本")
                    except Exception as e:
                        logging.error(f"检查更新时出错: {str(e)}")
                        self.check_completed.emit(False, f"检查更新时出错: {str(e)}")

            # 创建并启动线程
            self.update_thread = UpdateCheckThread()

            # 连接信号
            self.update_thread.update_found.connect(self.show_update_notification)
            self.update_thread.check_completed.connect(self.on_update_check_completed)

            # 启动线程
            self.update_thread.start()
            logging.info("更新检查线程已启动")

        except Exception as e:
            logging.error(f"启动后台更新检查时出错: {str(e)}")

    def on_update_check_completed(self, success, message):
        """更新检查完成回调

        Args:
            success: 是否成功
            message: 结果消息
        """
        # 标记已检测过更新
        self.update_checked = True

        # 记录日志
        if success:
            logging.info(f"后台更新检查完成: {message}")
        else:
            logging.warning(f"后台更新检查失败: {message}")

    def update_license_info(self, card_number, expiry_time):
        """更新卡密信息显示

        Args:
            card_number: 卡密号
            expiry_time: 到期时间
        """
        self.card_number = card_number
        self.expiry_time = expiry_time

        # 获取账号（机器码）
        try:
            from app.utils.kamidenglu import get_machine_code
            self.machine_code = get_machine_code()
        except Exception as e:
            logging.error(f"获取账号失败: {str(e)}")
            self.machine_code = ""

        # 更新状态栏显示
        if card_number and expiry_time:
            # 隐藏完整卡密号，只显示前4位和后4位
            if len(card_number) > 8:
                masked_card = card_number[:4] + '*' * (len(card_number) - 8) + card_number[-4:]
            else:
                masked_card = card_number

            self.license_label.setText(f"卡密: {masked_card}  到期: {expiry_time}")

            # 如果已经初始化了用户状态检测器，更新其配置
            if hasattr(self, 'status_checker') and self.status_checker:
                try:
                    # 更新配置文件
                    from app.utils.kamidenglu import load_config, save_config
                    config = load_config() or {}

                    # 更新卡密信息
                    if card_number and card_number != config.get('card'):
                        config['card'] = card_number
                        if self.machine_code:
                            config['machine_code'] = self.machine_code  # 保存账号信息

                        # 保存配置
                        save_config(config)
                        logging.info(f"已更新卡密配置: {card_number[:4]}***")

                        # 重新加载配置并启动状态检测
                        self.status_checker.load_config()
                        result = self.status_checker.start_checking()
                        if result:
                            logging.info("使用更新后的卡密信息启动用户状态监测成功")
                        else:
                            logging.warning("使用更新后的卡密信息启动用户状态监测失败")
                except Exception as e:
                    logging.error(f"更新用户状态检测器配置时出错: {str(e)}")
        else:
            self.license_label.setText("卡密: 未授权")
            self.license_label.setProperty("unauthorized", True)
            try:
                style = self.style()
                if style:
                    style.unpolish(self.license_label)
                    style.polish(self.license_label)
            except Exception:
                pass

    def adjust_to_screen(self):
        """使窗口自适应屏幕大小"""
        # 获取屏幕大小
        desktop = QDesktopWidget()
        screen_rect = desktop.availableGeometry(desktop.primaryScreen())
        screen_width, screen_height = screen_rect.width(), screen_rect.height()

        # 设置窗口大小为屏幕大小的85%，但不小于最小尺寸
        window_width = max(int(screen_width * 0.85), self.minimumWidth())
        window_height = max(int(screen_height * 0.85), self.minimumHeight())

        # 如果屏幕分辨率较低，确保窗口不会超出屏幕
        if window_width > screen_width * 0.95:
            window_width = int(screen_width * 0.95)
        if window_height > screen_height * 0.95:
            window_height = int(screen_height * 0.95)

        self.resize(window_width, window_height)

        # 记录当前屏幕分辨率，用于后续布局调整
        self.screen_width = screen_width
        self.screen_height = screen_height
        logging.info(f"屏幕分辨率: {screen_width}x{screen_height}, 窗口大小: {window_width}x{window_height}")

        # 将窗口居中显示
        self.center_window()

    def center_window(self):
        """将窗口居中显示在屏幕上"""
        frame_geometry = self.frameGeometry()
        screen = QDesktopWidget().availableGeometry().center()
        frame_geometry.moveCenter(screen)
        self.move(frame_geometry.topLeft())

    def create_top_toolbar(self):
        """创建顶部工具栏 - 现代化设计"""
        # 创建顶部工具栏
        self.top_toolbar = QToolBar("顶部工具栏")
        self.top_toolbar.setMovable(False)  # 禁止移动
        self.top_toolbar.setFloatable(False)  # 禁止浮动
        # self.top_toolbar.setIconSize(QSize(16, 16))  # 设置图标大小 - 移除以避免兼容性问题
        self.top_toolbar.setFixedHeight(42)  # 减小固定高度
        self.top_toolbar.setObjectName("mainToolbar")  # 设置对象名，用于样式表

        # 添加工具栏到窗口顶部，并固定位置
        self.addToolBar(self.top_toolbar)
        # 移除不兼容的工具栏换行设置

        # 创建一个水平布局容器，用于更好地控制工具栏内的元素布局
        toolbar_container = QWidget()
        toolbar_container.setObjectName("toolbarContainer")
        toolbar_layout = QHBoxLayout(toolbar_container)
        toolbar_layout.setContentsMargins(10, 3, 10, 3)  # 减小边距
        toolbar_layout.setSpacing(8)  # 减小间距

        # 添加应用标题和版本
        app_title = QLabel(f"头条内容社交工具 <span style='color:#4a86e8;'>v{APP_VERSION}</span>")
        app_title.setObjectName("appTitle")
        app_title.setStyleSheet("""
            #appTitle {
                font-size: 13px;
                font-weight: bold;
                color: #333333;
            }
        """)
        toolbar_layout.addWidget(app_title)

        # 添加卡密信息显示到工具栏左侧
        self.license_label = QLabel("卡密: --  到期: --")
        self.license_label.setObjectName("licenseLabel")
        self.license_label.setStyleSheet("""
            #licenseLabel {
                color: #666666;
                font-size: 10px;
                padding: 0 6px;
                border-left: 1px solid #e0e0e0;
            }
        """)
        # 设置最小宽度，确保文本不会被压缩
        self.license_label.setMinimumWidth(150)
        toolbar_layout.addWidget(self.license_label)

        # 添加公告显示到卡密信息后面
        self.announcement_label = QLabel("📢 公告: 正在加载...")
        self.announcement_label.setObjectName("announcementLabel")
        self.announcement_label.setStyleSheet("""
            #announcementLabel {
                color: #e74c3c;
                font-size: 10px;
                font-weight: bold;
                padding: 2px 8px;
                background-color: rgba(231, 76, 60, 0.1);
                border: 1px solid rgba(231, 76, 60, 0.3);
                border-radius: 10px;
                margin: 0 6px;
            }
        """)
        # 设置自适应宽度，确保公告文字完整显示
        self.announcement_label.setMinimumWidth(120)
        # 移除最大宽度限制，让公告自适应显示
        # self.announcement_label.setMaximumWidth(400)  # 注释掉最大宽度限制
        # 允许文本自适应，不强制省略
        self.announcement_label.setWordWrap(False)
        self.announcement_label.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Fixed)
        toolbar_layout.addWidget(self.announcement_label)

        # 添加弹性空间，将后续控件推到右侧
        toolbar_layout.addStretch(1)

        # 添加自动化状态指示器
        self.automation_status_widget = AutomationStatusWidget()
        toolbar_layout.addWidget(self.automation_status_widget)

        # 添加保存设置复选框
        self.save_settings_checkbox = QCheckBox("自动保存设置")
        self.save_settings_checkbox.setObjectName("saveSettingsCheckbox")
        self.save_settings_checkbox.setChecked(True)  # 默认选中
        self.save_settings_checkbox.setStyleSheet("""
            QCheckBox {
                color: #666666;
                font-size: 10px;
            }
            QCheckBox::indicator {
                width: 12px;
                height: 12px;
            }
        """)
        toolbar_layout.addWidget(self.save_settings_checkbox)

        # 添加清理缓存按钮
        self.clean_cache_btn = QPushButton("清理C盘缓存")
        self.clean_cache_btn.setObjectName("cleanCacheBtn")
        self.clean_cache_btn.clicked.connect(self.clean_browser_cache)
        self.clean_cache_btn.setStyleSheet("""
            #cleanCacheBtn {
                background-color: #3498db;
                color: white;
                font-weight: bold;
                border: none;
                border-radius: 3px;
                padding: 4px 8px;
                font-size: 10px;
                min-height: 20px;
                max-height: 24px;
            }
            #cleanCacheBtn:hover {
                background-color: #2980b9;
            }
        """)
        toolbar_layout.addWidget(self.clean_cache_btn)

        # 添加关闭浏览器进程按钮
        self.kill_browser_btn = QPushButton("关闭浏览器进程")
        self.kill_browser_btn.setObjectName("killBrowserBtn")
        self.kill_browser_btn.clicked.connect(self.kill_browser_processes)
        self.kill_browser_btn.setStyleSheet("""
            #killBrowserBtn {
                background-color: #f39c12;
                color: white;
                font-weight: bold;
                border: none;
                border-radius: 3px;
                padding: 4px 8px;
                font-size: 10px;
                min-height: 20px;
                max-height: 24px;
            }
            #killBrowserBtn:hover {
                background-color: #e67e22;
            }
        """)
        toolbar_layout.addWidget(self.kill_browser_btn)

        # 添加停止按钮到工具栏右侧
        self.stop_btn = QPushButton("停止任务")
        self.stop_btn.setObjectName("stopBtn")
        # self.stop_btn.setCursor(Qt.PointingHandCursor)  # 移除光标设置以避免兼容性问题
        self.stop_btn.clicked.connect(self.stop_all_tasks)
        self.stop_btn.setStyleSheet("""
            #stopBtn {
                background-color: #e74c3c;
                color: white;
                font-weight: bold;
                border: none;
                border-radius: 3px;
                padding: 4px 8px;
                font-size: 10px;
                min-height: 20px;
                max-height: 24px;
            }
            #stopBtn:hover {
                background-color: #c0392b;
            }
        """)
        toolbar_layout.addWidget(self.stop_btn)

        # 将容器添加到工具栏
        self.top_toolbar.addWidget(toolbar_container)

        # 初始化公告更新定时器
        self.init_announcement_timer()

    def init_announcement_timer(self):
        """初始化公告更新定时器"""
        try:
            # 创建公告更新定时器
            self.announcement_timer = QTimer(self)
            self.announcement_timer.timeout.connect(self.update_announcement)
            self.announcement_timer.start(3600000)  # 每小时更新一次公告

            # 初始加载公告 - 使用单次定时器
            self.create_single_shot_timer(2000, self.update_announcement)
            logging.info("公告更新定时器已初始化")
        except Exception as e:
            logging.error(f"初始化公告定时器时出错: {str(e)}")

    def update_announcement(self):
        """更新公告内容"""
        try:
            success, announcement = get_announcement()
            if success and announcement:
                # 显示完整公告内容，不再限制长度
                display_text = f"📢 公告: {announcement}"
                self.announcement_label.setText(display_text)
                self.announcement_label.setToolTip(f"完整公告: {announcement}")
                logging.info(f"已更新公告: {announcement[:30]}...")
            else:
                self.announcement_label.setText("📢 公告: 暂无公告")
                self.announcement_label.setToolTip("暂无公告信息")
                logging.info(f"获取公告失败: {announcement}")
        except Exception as e:
            logging.error(f"更新公告时出错: {str(e)}")
            self.announcement_label.setText("📢 公告: 获取失败")
            self.announcement_label.setToolTip(f"获取公告失败: {str(e)}")

    def set_application_style(self):
        """设置应用程序样式 - 现代化设计"""
        # 使用Fusion样式，更加现代
        self.setStyle(QStyleFactory.create("Fusion"))

        # 设置全局字体，优化字体渲染
        font = QFont("Microsoft YaHei", 9)
        font.setHintingPreference(QFont.PreferDefaultHinting)
        font.setStyleStrategy(QFont.PreferAntialias)
        self.setFont(font)

        # 应用全局样式表
        self.setStyleSheet("""
            /* 全局样式 */
            QMainWindow, QDialog {
                background-color: #f5f7fa;
                color: #333333;
                font-family: 'Microsoft YaHei', '微软雅黑', sans-serif;
            }

            /* 工具栏样式 */
            #mainToolbar {
                background-color: #ffffff;
                border-bottom: 1px solid #e0e0e0;
                spacing: 10px;
            }

            #toolbarContainer {
                background-color: transparent;
            }

            /* 标签页样式 - 更长的标签页 */
            QTabWidget::pane {
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                padding: 15px;
                background-color: #ffffff;
                margin-top: -1px;
            }

            QTabBar::tab {
                padding: 10px 30px;  /* 增加宽度，确保内容完整显示 */
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                border: 1px solid #e0e0e0;
                border-bottom: none;
                background-color: #f0f2f5;
                color: #666666;
                font-size: 13px;
                font-weight: bold;
                min-width: 120px;  /* 设置最小宽度 */
                max-width: 200px;  /* 设置最大宽度 */
            }

            QTabBar::tab:selected {
                background-color: #ffffff;
                color: #4a86e8;
                border-bottom: 3px solid #4a86e8;
            }

            QTabBar::tab:hover:!selected {
                background-color: #e8f0fe;
                color: #4a86e8;
            }

            /* 按钮样式 - 优化尺寸 */
            QPushButton {
                padding: 5px 10px;
                border-radius: 4px;
                border: none;
                background-color: #4a86e8;
                color: white;
                font-weight: bold;
                font-family: 'Microsoft YaHei', '微软雅黑', sans-serif;
                font-size: 11px;
                min-height: 24px;
                max-height: 28px;
            }

            QPushButton:hover {
                background-color: #3a76d8;
            }

            QPushButton:pressed {
                background-color: #2a66c8;
            }

            QPushButton:disabled {
                background-color: #a0a0a0;
                color: #e0e0e0;
            }

            /* 输入框样式 */
            QLineEdit, QSpinBox, QDoubleSpinBox, QComboBox {
                padding: 8px 12px;
                border: 1px solid #d0d0d0;
                border-radius: 6px;
                background-color: white;
                selection-background-color: #4a86e8;
                font-family: 'Microsoft YaHei', '微软雅黑', sans-serif;
                font-size: 13px;
                min-height: 25px;
            }

            QLineEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus, QComboBox:focus {
                border: 1px solid #4a86e8;
                background-color: #f8f9ff;
            }

            /* 下拉框样式 */
            QComboBox {
                padding-right: 20px;
            }

            QComboBox::drop-down {
                subcontrol-origin: padding;
                subcontrol-position: right center;
                width: 20px;
                border-left: none;
                border-top-right-radius: 6px;
                border-bottom-right-radius: 6px;
            }

            QComboBox::down-arrow {
                width: 12px;
                height: 12px;
            }

            QComboBox QAbstractItemView {
                border: 1px solid #d0d0d0;
                border-radius: 6px;
                background-color: white;
                selection-background-color: #4a86e8;
                selection-color: white;
            }

            /* 复选框样式 */
            QCheckBox {
                spacing: 10px;
                font-family: 'Microsoft YaHei', '微软雅黑', sans-serif;
                font-size: 13px;
            }

            QCheckBox::indicator {
                width: 20px;
                height: 20px;
                border: 1px solid #d0d0d0;
                border-radius: 4px;
                background-color: white;
            }

            QCheckBox::indicator:checked {
                background-color: #4a86e8;
                border-color: #4a86e8;
                image: url(app/resources/icons/check.png);
            }

            QCheckBox::indicator:unchecked:hover {
                border: 1px solid #4a86e8;
                background-color: #f8f9ff;
            }

            /* 标签样式 */
            QLabel {
                font-family: 'Microsoft YaHei', '微软雅黑', sans-serif;
                color: #333333;
                font-size: 13px;
            }

            /* 进度条样式 */
            QProgressBar {
                border: none;
                border-radius: 8px;
                text-align: center;
                height: 24px;
                background-color: #f0f0f0;
                font-family: 'Microsoft YaHei', '微软雅黑', sans-serif;
                font-weight: bold;
                font-size: 13px;
                color: #333333;
            }

            QProgressBar::chunk {
                background-color: #4caf50;
                border-radius: 8px;
            }

            /* 文本编辑框样式 */
            QTextEdit, QPlainTextEdit {
                background-color: #ffffff;
                border: none;
                border-radius: 8px;
                font-family: 'Consolas', 'Courier New', monospace;
                font-size: 13px;
                padding: 10px;
                color: #333333;
                selection-background-color: #4a86e8;
                selection-color: white;
            }

            /* 分组框样式 */
            QGroupBox {
                font-family: 'Microsoft YaHei', '微软雅黑', sans-serif;
                font-weight: bold;
                border: 1px solid #dddddd;
                border-radius: 10px;
                margin-top: 15px;
                padding-top: 12px;
                background-color: #ffffff;
            }

            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 8px;
                color: #333333;
                font-size: 14px;
            }

            /* 滚动条样式 */
            QScrollBar:vertical {
                border: none;
                background: #f0f0f0;
                width: 10px;
                margin: 0px;
                border-radius: 5px;
            }

            QScrollBar::handle:vertical {
                background: #c0c0c0;
                min-height: 30px;
                border-radius: 5px;
            }

            QScrollBar::handle:vertical:hover {
                background: #a0a0a0;
            }

            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0px;
            }

            QScrollBar:horizontal {
                border: none;
                background: #f0f0f0;
                height: 10px;
                margin: 0px;
                border-radius: 5px;
            }

            QScrollBar::handle:horizontal {
                background: #c0c0c0;
                min-width: 30px;
                border-radius: 5px;
            }

            QScrollBar::handle:horizontal:hover {
                background: #a0a0a0;
            }

            QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
                width: 0px;
            }

            /* 工具栏样式 */
            QToolBar {
                background-color: #ffffff;
                border: none;
                border-bottom: 1px solid #e0e0e0;
                padding: 5px;
                spacing: 5px;
            }

            /* 状态栏样式 */
            QStatusBar {
                background-color: #ffffff;
                border-top: 1px solid #e0e0e0;
                color: #333333;
            }

            /* 表格样式 */
            QTableView, QTableWidget {
                background-color: #ffffff;
                border: none;
                border-radius: 8px;
                gridline-color: #e0e0e0;
                selection-background-color: #e3f2fd;
                selection-color: #333333;
            }

            QTableView::item, QTableWidget::item {
                padding: 5px;
                border-bottom: 1px solid #f0f0f0;
            }

            QTableView::item:selected, QTableWidget::item:selected {
                background-color: #e3f2fd;
                color: #333333;
            }

            QHeaderView::section {
                background-color: #f5f5f5;
                padding: 8px;
                border: none;
                border-bottom: 1px solid #e0e0e0;
                font-weight: bold;
                color: #333333;
            }

            /* 菜单样式 */
            QMenu {
                background-color: #ffffff;
                border: 1px solid #e0e0e0;
                border-radius: 6px;
                padding: 5px;
            }

            QMenu::item {
                padding: 8px 30px 8px 20px;
                border-radius: 4px;
            }

            QMenu::item:selected {
                background-color: #e3f2fd;
                color: #333333;
            }

            QMenu::separator {
                height: 1px;
                background-color: #e0e0e0;
                margin: 5px 15px;
            }

            /* 消息框样式 */
            QMessageBox {
                background-color: #ffffff;
            }

            QMessageBox QLabel {
                color: #333333;
            }

            QMessageBox QPushButton {
                min-width: 80px;
                min-height: 30px;
            }

            /* 特定控件样式 */
            #statusFrame {
                background-color: #ffffff;
                border-radius: 6px;
                border: 1px solid #dddddd;
                margin-top: 5px;
                min-height: 32px;
                max-height: 36px;
            }

            #totalCountLabel {
                color: #333333;
                font-weight: bold;
                font-size: 11px;
            }

            #licenseLabel {
                color: #4a86e8;
                font-weight: bold;
                font-size: 11px;
                margin-left: 10px;
            }

            #stopBtn {
                background-color: #f44336;
                color: white;
                border-radius: 6px;
                padding: 5px 12px;
                font-weight: bold;
                font-size: 12px;
                max-height: 28px;
                margin-right: 15px;
            }

            #stopBtn:hover {
                background-color: #e53935;
            }

            #stopBtn:pressed {
                background-color: #d32f2f;
            }

            /* 底部状态栏按钮已移动到账号管理页面 */

            #cpuLabel, #uploadLabel, #downloadLabel {
                color: #333333;
                font-weight: bold;
                font-size: 13px;
            }

            #uploadLabel {
                color: #4caf50;
            }

            #downloadLabel {
                color: #2196f3;
            }

            #licenseLabel[unauthorized="true"] {
                color: #f44336;
            }

            #greetingLabel {
                color: #4a86e8;
                font-size: 16px;
                font-weight: bold;
                padding: 10px 15px;
                background-color: #ffffff;
                border-radius: 10px;
                margin-bottom: 15px;
                border: 1px solid #dddddd;
            }















            QLabel[status_normal="true"] {
                color: #4caf50;
                font-weight: bold;
            }

            QLabel[status_normal="false"] {
                color: #f44336;
                font-weight: bold;
            }
        """)

    def set_init_check_results(self, results):
        """设置初始化检测结果，避免重复检测

        Args:
            results: 初始化检测结果字典
        """
        # 确保ffmpeg检测结果始终为兼容
        if 'ffmpeg' in results:
            results['ffmpeg'] = {
                "status": True,
                "version": "已兼容",
                "message": "已兼容"
            }

        self.init_check_results = results
        logging.info("已设置初始化检测结果，主窗口将不再重复检测环境和更新")

        # 保存更新检测结果，避免再次检测
        if 'update' in results:
            self.update_checked = True
            update_info = results.get('update', {})
            logging.info(f"更新检测结果: {update_info.get('message', '未知')}")

            # 如果有更新，显示更新提示
            if update_info.get('status', False) and update_info.get('has_update', False):
                self.show_update_notification(update_info)

    def show_update_notification(self, update_info):
        """显示更新提示

        Args:
            update_info: 更新信息字典
        """
        try:
            # 记录更新信息
            current_version = update_info.get('current_version', '')
            server_version = update_info.get('server_version', '')
            logging.info(f"收到更新通知: 当前版本 {current_version}，服务器版本 {server_version}")

            # 显示系统托盘通知
            try:
                from app.utils.notification_helper import show_tray_notification
                from PyQt5.QtWidgets import QSystemTrayIcon

                # 显示通知
                show_tray_notification(
                    "发现新版本",
                    f"发现新版本: v{server_version}，当前版本: v{current_version}。",
                    1,  # 1 = QSystemTrayIcon.Information
                    5000
                )
            except Exception as e:
                logging.warning(f"显示系统托盘通知失败: {str(e)}")

            # 延迟5秒后显示更新提示，避免与初始化对话框冲突
            self.create_single_shot_timer(5000, lambda: self._display_update_notification(update_info))
        except Exception as e:
            logging.error(f"设置更新提示时出错: {str(e)}")

    def _display_update_notification(self, update_info):
        """显示更新提示对话框

        Args:
            update_info: 更新信息字典
        """
        try:
            from app.utils.kamidenglu import KamiManager, APP_VERSION

            # 获取版本信息
            current_version = update_info.get('current_version', APP_VERSION)
            server_version = update_info.get('server_version', '')

            logging.info(f"显示更新提示: 当前版本 {current_version}，服务器版本 {server_version}")

            # 创建更新提示对话框
            from PyQt5.QtWidgets import QMessageBox
            reply = QMessageBox.question(
                self,
                "发现新版本",
                f"发现新版本: v{server_version}，当前版本: v{current_version}。\n是否立即更新?",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )

            # 如果用户确认更新
            if reply == QMessageBox.Yes:
                logging.info("用户确认更新，开始下载更新")
                # 创建卡密管理器
                kami_manager = KamiManager()

                # 直接获取下载地址并开始下载，跳过第二个确认窗口
                try:
                    # 获取下载地址
                    download_url = kami_manager.get_download_url(server_version)
                    if not download_url:
                        logging.error("无法获取更新下载地址")
                        QMessageBox.warning(self, "更新失败",
                                          "无法获取更新下载地址。错误原因可能是:\n"
                                          "1. 网络连接问题\n"
                                          "2. 服务器暂时不可用\n"
                                          "3. 软件标识有误\n\n"
                                          "请稍后再试或联系客服获取最新版本。")
                        return

                    # 创建选择下载方式的对话框
                    from PyQt5.QtWidgets import QDialog, QVBoxLayout, QLabel, QPushButton, QHBoxLayout

                    download_method_dialog = QDialog(self)
                    download_method_dialog.setWindowTitle("选择下载方式")
                    download_method_dialog.setMinimumWidth(400)

                    layout = QVBoxLayout()

                    # 提示信息
                    info_label = QLabel("请选择下载更新的方式：")
                    info_label.setStyleSheet("font-size: 14px; margin-bottom: 10px;")
                    layout.addWidget(info_label)

                    # 按钮布局
                    button_layout = QHBoxLayout()

                    # 自动下载按钮
                    auto_download_btn = QPushButton("软件自动下载")
                    auto_download_btn.setMinimumHeight(40)

                    # 浏览器下载按钮
                    browser_download_btn = QPushButton("使用浏览器下载")
                    browser_download_btn.setMinimumHeight(40)

                    # 取消按钮
                    cancel_btn = QPushButton("取消")
                    cancel_btn.setMinimumHeight(40)

                    button_layout.addWidget(auto_download_btn)
                    button_layout.addWidget(browser_download_btn)
                    button_layout.addWidget(cancel_btn)

                    layout.addLayout(button_layout)
                    download_method_dialog.setLayout(layout)

                    # 下载方法选择结果
                    download_method = 0  # 0=取消, 1=自动, 2=浏览器

                    # 按钮事件处理
                    def on_auto_download():
                        nonlocal download_method
                        download_method = 1
                        download_method_dialog.accept()

                    def on_browser_download():
                        nonlocal download_method
                        download_method = 2
                        download_method_dialog.accept()

                    auto_download_btn.clicked.connect(on_auto_download)
                    browser_download_btn.clicked.connect(on_browser_download)
                    cancel_btn.clicked.connect(download_method_dialog.reject)

                    # 显示对话框
                    download_method_dialog.exec_()

                    # 根据用户选择执行不同的下载方式
                    if download_method == 1:
                        # 自动下载 - 创建下载进度对话框
                        from PyQt5.QtWidgets import QProgressDialog
                        from PyQt5.QtCore import Qt
                        progress_dialog = QProgressDialog("正在下载新版本到当前目录...", "取消", 0, 100, self)
                        progress_dialog.setWindowTitle("下载进度")
                        progress_dialog.setMinimumDuration(0)
                        progress_dialog.setWindowModality(1)  # 1 = Qt.WindowModal
                        progress_dialog.setValue(0)
                        progress_dialog.setFixedSize(400, 100)
                        progress_dialog.show()
                    elif download_method == 2:
                        # 浏览器下载 - 打开浏览器并跳转到下载链接
                        try:
                            import webbrowser
                            import os

                            # 打开浏览器下载
                            webbrowser.open(download_url)

                            # 提示用户
                            QMessageBox.information(
                                self,
                                "浏览器下载",
                                "已在浏览器中打开下载链接，请在浏览器中完成下载。\n\n"
                                "下载完成后，请将文件移动到软件目录并重命名为：\n"
                                f"头条自媒体自动存稿工具_v{server_version}.exe\n\n"
                                "然后重启软件即可完成更新。"
                            )

                            # 直接返回，不继续执行后续的自动下载代码
                            return
                        except Exception as e:
                            logging.error(f"打开浏览器下载失败: {str(e)}")
                            QMessageBox.warning(
                                self,
                                "浏览器下载失败",
                                f"打开浏览器下载失败: {str(e)}\n请尝试自动下载或手动访问官网下载。"
                            )
                            # 如果浏览器下载失败，回退到自动下载
                            from PyQt5.QtWidgets import QProgressDialog
                            from PyQt5.QtCore import Qt
                            progress_dialog = QProgressDialog("正在下载新版本到当前目录...", "取消", 0, 100, self)
                            progress_dialog.setWindowTitle("下载进度")
                            progress_dialog.setMinimumDuration(0)
                            progress_dialog.setWindowModality(1)  # 1 = Qt.WindowModal
                            progress_dialog.setValue(0)
                            progress_dialog.setFixedSize(400, 100)
                            progress_dialog.show()
                    else:
                        # 用户取消下载
                        return

                    # 确保应用处理事件
                    from PyQt5.QtWidgets import QApplication
                    app = QApplication.instance()
                    if app:
                        app.processEvents()

                    # 显示系统托盘通知
                    try:
                        from app.utils.notification_helper import show_tray_notification
                        from PyQt5.QtWidgets import QSystemTrayIcon

                        # 显示通知
                        show_tray_notification(
                            "开始下载更新",
                            f"正在下载新版本 v{server_version}，请稍候...",
                            1,  # 1 = QSystemTrayIcon.Information
                            3000
                        )
                    except Exception as e:
                        logging.warning(f"显示下载开始通知失败: {str(e)}")

                    # 更新状态栏
                    status_bar = self.statusBar()
                    if status_bar:
                        status_bar.showMessage("正在下载软件更新，请稍候...", 0)

                    # 使用KamiManager的下载和安装功能
                    import threading

                    # 下载状态共享变量
                    download_status = {
                        "path": None,
                        "error": None,
                        "completed": False,
                        "total_size": 0,
                        "current_size": 0,
                        "retry_count": 0,
                        "status_message": "正在准备下载..."
                    }

                    # 复制KamiManager中的下载线程函数
                    def download_thread_func():
                        try:
                            import requests
                            import os
                            import time

                            # 获取文件总大小
                            session = requests.Session()

                            # 检查URL是否包含非ASCII字符，如果有则进行编码
                            head_url = download_url
                            try:
                                if any(ord(c) > 127 for c in download_url):
                                    # 导入URL处理模块
                                    from urllib.parse import urlparse, quote, unquote, urlunparse

                                    # 解析URL
                                    parsed_url = urlparse(download_url)

                                    # 重新编码路径部分
                                    path = parsed_url.path
                                    # 先解码以避免重复编码
                                    try:
                                        path = unquote(path)
                                    except Exception:
                                        pass
                                    # 编码路径中的非ASCII字符
                                    path = quote(path, safe='/:')

                                    # 重新编码查询参数
                                    query = parsed_url.query
                                    if query:
                                        try:
                                            query = unquote(query)
                                        except Exception:
                                            pass
                                        query = quote(query, safe='=&')

                                    # 重新组合URL
                                    head_url = urlunparse((
                                        parsed_url.scheme,
                                        parsed_url.netloc,
                                        path,
                                        parsed_url.params,
                                        query,
                                        parsed_url.fragment
                                    ))

                                    print(f"HEAD请求编码URL: {head_url}")
                            except Exception as e:
                                print(f"URL编码处理失败: {str(e)}，将使用原始URL")

                            # 使用可能编码后的URL进行HEAD请求
                            response = session.head(head_url, timeout=15)
                            total_size = int(response.headers.get('content-length', 0))
                            download_status["total_size"] = total_size

                            # 使用当前工作目录作为下载位置
                            current_dir = os.getcwd()

                            # 创建标准命名的文件名
                            file_name = f"头条自媒体自动存稿工具_v{server_version}.exe"
                            download_path = os.path.join(current_dir, file_name)
                            temp_path = f"{download_path}.tmp"

                            # 检查是否已有部分下载的文件
                            downloaded_size = 0
                            if os.path.exists(temp_path):
                                downloaded_size = os.path.getsize(temp_path)
                                download_status["current_size"] = downloaded_size
                                download_status["status_message"] = f"发现已下载部分: {downloaded_size/1024/1024:.2f} MB"
                                if downloaded_size >= total_size and total_size > 0:
                                    download_status["status_message"] = "文件已完全下载，正在校验..."
                                    os.rename(temp_path, download_path)
                                    download_status["path"] = download_path
                                    download_status["completed"] = True
                                    return

                            # 重试次数和间隔设置
                            max_retries = 5
                            retry_delay = 3  # 秒
                            retry_count = 0

                            # 断点续传 - 设置请求头
                            headers = {}
                            if downloaded_size > 0:
                                headers['Range'] = f'bytes={downloaded_size}-'
                                download_status["status_message"] = f"设置断点续传，从 {downloaded_size/1024/1024:.2f} MB 继续"

                            # 使用with上下文自动关闭文件
                            with open(temp_path, 'ab' if downloaded_size > 0 else 'wb') as f:
                                while retry_count < max_retries:
                                    try:
                                        # 设置较短的超时时间以便及时检测连接问题

                                        # 检查URL是否包含非ASCII字符，如果有则进行编码
                                        current_url = download_url
                                        try:
                                            if any(ord(c) > 127 for c in download_url):
                                                # 导入URL处理模块
                                                from urllib.parse import urlparse, quote, unquote, urlunparse

                                                # 解析URL
                                                parsed_url = urlparse(download_url)

                                                # 重新编码路径部分
                                                path = parsed_url.path
                                                # 先解码以避免重复编码
                                                try:
                                                    path = unquote(path)
                                                except Exception:
                                                    pass
                                                # 编码路径中的非ASCII字符
                                                path = quote(path, safe='/:')

                                                # 重新编码查询参数
                                                query = parsed_url.query
                                                if query:
                                                    try:
                                                        query = unquote(query)
                                                    except Exception:
                                                        pass
                                                    query = quote(query, safe='=&')

                                                # 重新组合URL
                                                current_url = urlunparse((
                                                    parsed_url.scheme,
                                                    parsed_url.netloc,
                                                    path,
                                                    parsed_url.params,
                                                    query,
                                                    parsed_url.fragment
                                                ))

                                                print(f"下载时编码URL: {current_url}")
                                        except Exception as e:
                                            print(f"URL编码处理失败: {str(e)}，将使用原始URL")

                                        # 使用可能编码后的URL进行请求
                                        response = session.get(current_url, headers=headers,
                                                           stream=True, timeout=(5, 30))
                                        response.raise_for_status()

                                        # 计算当前块起始位置
                                        current_size = downloaded_size

                                        # 使用更小的块大小，提高稳定性
                                        chunk_size = 1024 * 1024  # 1MB 块
                                        for chunk in response.iter_content(chunk_size=chunk_size):
                                            if chunk:
                                                f.write(chunk)
                                                current_size += len(chunk)
                                                download_status["current_size"] = current_size
                                                # 刷新文件缓冲，确保写入磁盘
                                                f.flush()
                                                os.fsync(f.fileno())

                                        # 下载完成，退出循环
                                        break
                                    except (requests.RequestException, IOError) as e:
                                        retry_count += 1
                                        download_status["retry_count"] = retry_count
                                        download_status["status_message"] = f"下载中断! 正在进行第 {retry_count}/{max_retries} 次重试..."

                                        if retry_count >= max_retries:
                                            download_status["status_message"] = "达到最大重试次数，下载失败"
                                            raise Exception(f"下载失败，已重试 {max_retries} 次: {str(e)}")

                                        # 更新已下载大小
                                        if os.path.exists(temp_path):
                                            downloaded_size = os.path.getsize(temp_path)
                                            download_status["current_size"] = downloaded_size
                                            headers['Range'] = f'bytes={downloaded_size}-'

                                        # 等待一段时间后重试
                                        time.sleep(retry_delay)

                            # 检查文件完整性
                            if total_size > 0:
                                final_size = os.path.getsize(temp_path)
                                if final_size < total_size:
                                    raise Exception(f"下载文件不完整: {final_size}/{total_size} 字节")
                                download_status["status_message"] = "文件下载完成，正在校验..."

                            # 重命名临时文件为正式文件名
                            if os.path.exists(download_path):
                                os.remove(download_path)
                            os.rename(temp_path, download_path)

                            download_status["path"] = download_path
                            download_status["status_message"] = "下载完成!"
                        except Exception as e:
                            download_status["error"] = str(e)
                            download_status["status_message"] = f"下载错误: {str(e)}"
                        finally:
                            download_status["completed"] = True

                    # 启动下载线程
                    download_thread = threading.Thread(target=download_thread_func)
                    download_thread.daemon = True
                    download_thread.start()

                    # 进度更新循环
                    import time
                    while not download_status["completed"] and not progress_dialog.wasCanceled():
                        # 计算实际进度
                        current_size = download_status["current_size"]
                        total_size = download_status["total_size"]

                        if total_size > 0:
                            # 计算真实进度
                            progress = min(int((current_size / total_size) * 100), 99)
                            progress_dialog.setValue(progress)

                            # 显示下载速度和状态
                            status_message = download_status["status_message"]
                            size_info = f"[{current_size/1024/1024:.2f}/{total_size/1024/1024:.2f} MB] {progress}%"
                            retry_info = ""
                            if download_status["retry_count"] > 0:
                                retry_info = f" (已重试: {download_status['retry_count']}次)"

                            progress_dialog.setLabelText(f"{status_message}{retry_info}\n{size_info}")

                            # 更新状态栏和任务栏进度
                            try:
                                # 更新状态栏
                                status_bar = self.statusBar()
                                if status_bar:
                                    status_bar.showMessage(f"正在下载软件更新: {status_message} {size_info}", 0)

                                # 更新任务栏进度
                                from app.utils.notification_helper import set_taskbar_progress
                                set_taskbar_progress(self, progress)
                            except Exception as e:
                                logging.warning(f"更新状态栏或任务栏进度失败: {str(e)}")
                        else:
                            # 如果无法获取总大小，使用模拟进度
                            progress = min(download_status["retry_count"] * 10 + 10, 90)
                            progress_dialog.setValue(progress)
                            progress_dialog.setLabelText(f"{download_status['status_message']}\n已下载: {current_size/1024/1024:.2f} MB")

                            # 更新状态栏和任务栏进度
                            try:
                                # 更新状态栏
                                status_bar = self.statusBar()
                                if status_bar:
                                    status_bar.showMessage(f"正在下载软件更新: {download_status['status_message']} (已下载: {current_size/1024/1024:.2f} MB)", 0)

                                # 更新任务栏进度
                                from app.utils.notification_helper import set_taskbar_progress
                                set_taskbar_progress(self, progress)
                            except Exception as e:
                                logging.warning(f"更新状态栏或任务栏进度失败: {str(e)}")

                        # 处理事件
                        if app:
                            app.processEvents()

                        # 等待100毫秒
                        time.sleep(0.1)

                    # 如果用户取消了下载
                    if progress_dialog.wasCanceled():
                        logging.info("用户取消下载")
                        return

                    # 检查下载结果
                    if download_status["error"]:
                        logging.error(f"下载更新失败: {download_status['error']}")
                        QMessageBox.warning(self, "更新失败", f"下载更新失败: {download_status['error']}\n请稍后重试或访问官网手动下载更新。")
                        return

                    download_path = download_status["path"]
                    if download_path:
                        # 设置进度为100%
                        progress_dialog.setValue(100)
                        progress_dialog.setLabelText("下载完成，准备打开下载目录...")

                        # 确保app不为None
                        if app:
                            app.processEvents()

                        # 更新状态栏和任务栏进度
                        try:
                            # 更新状态栏
                            status_bar = self.statusBar()
                            if status_bar:
                                status_bar.showMessage("软件更新下载完成，准备安装...", 5000)

                            # 更新任务栏进度为完成状态
                            from app.utils.notification_helper import set_taskbar_progress
                            set_taskbar_progress(self, 100)

                            # 显示下载完成通知
                            from app.utils.notification_helper import show_tray_notification
                            from PyQt5.QtWidgets import QSystemTrayIcon

                            # 显示通知
                            show_tray_notification(
                                "更新下载完成",
                                f"新版本 v{server_version} 已下载完成，准备安装...",
                                1,  # 1 = QSystemTrayIcon.Information
                                5000
                            )
                        except Exception as e:
                            logging.warning(f"更新下载完成通知失败: {str(e)}")

                        # 关闭进度对话框
                        progress_dialog.close()

                        # 安装更新
                        kami_manager.install_update(download_path)
                    else:
                        logging.error("下载更新失败")
                        QMessageBox.warning(self, "更新失败", "下载更新失败，请访问官网手动下载更新。")
                except Exception as e:
                    logging.error(f"更新过程中出错: {str(e)}")
                    QMessageBox.warning(self, "更新失败", f"更新过程中出错: {str(e)}\n请访问官网手动下载更新。")
            else:
                logging.info("用户取消更新")
        except Exception as e:
            logging.error(f"显示更新提示时出错: {str(e)}")

    def init_system_monitor(self):
        """初始化系统监控"""
        try:
            from app.utils.system_monitor import get_system_monitor

            # 获取系统监控实例
            self.monitor = get_system_monitor()

            # 连接信号到界面 - 连接CPU和网络监控（已移除公网IP监控）
            self.monitor.cpu_updated.connect(self.system_info.update_cpu)
            self.monitor.network_updated.connect(self.system_info.update_network)

            # 如果已有初始化检测结果，则直接使用，不再重复检测
            if self.init_check_results:
                # 直接更新浏览器环境和FFmpeg状态
                browser_result = self.init_check_results.get('browser', {})
                ffmpeg_result = self.init_check_results.get('ffmpeg', {})

                # 手动更新界面
                self.system_info.update_browser_env(browser_result)
                self.system_info.update_ffmpeg(
                    ffmpeg_result.get('status', False),
                    ffmpeg_result.get('version', '未知')
                )

                # 设置监控器的缓存，避免重复检测
                self.monitor.set_cached_results(browser_result, ffmpeg_result)
            else:
                # 如果没有初始化检测结果，则延迟连接浏览器环境信号
                # 延迟连接，避免启动时进行耗时的浏览器环境检测
                self.create_single_shot_timer(5000, lambda: self.monitor.browser_env_updated.connect(self.system_info.update_browser_env))
                self.create_single_shot_timer(5000, lambda: self.monitor.ffmpeg_updated.connect(self.system_info.update_ffmpeg))

            # 启动监控，但使用较低的更新频率
            self.monitor.set_update_interval(5000)  # 设置为5秒更新一次
            self.monitor.start()
            logging.info("系统监控已启动，使用低频率更新")

            # 延迟3秒后恢复正常更新频率
            self.create_single_shot_timer(3000, lambda: self.monitor.set_update_interval(2000))
        except Exception as e:
            logging.error(f"初始化系统监控时出错: {str(e)}")

    def on_cookie_path_changed(self, path):
        """Cookie路径变化时处理

        Args:
            path: 新的cookie路径
        """
        # 更新账号标签页中的cookie_path属性
        self.account_tab.cookie_path = path

        # 仅更新路径，不自动加载账号
        logging.info(f"Cookie路径已更新为: {path}")

        # 移除自动加载账号功能，用户需要手动点击加载账号按钮
        # self.account_tab.load_cookie_files(path, show_message=False, delay_data_loading=True)

    def on_load_account(self):
        """手动加载账号处理 - 使用完整加载模式获取详细数据"""
        try:
            # 从设置中获取cookie路径并加载
            cookie_path = self.setting_tab.cookie_path.text().strip()

            # 验证路径
            if not cookie_path:
                QMessageBox.warning(self, "路径错误", "请先在设置中配置Cookie文件路径")
                return

            if not os.path.exists(cookie_path):
                QMessageBox.warning(self, "路径错误", f"Cookie路径不存在: {cookie_path}")
                return

            # 更新账号标签页中的cookie_path属性
            self.account_tab.cookie_path = cookie_path

            # 显示状态栏消息
            status_bar = self.statusBar()
            if status_bar:
                status_bar.showMessage("正在加载账号数据...", 3000)

            # 使用完整加载模式，获取详细数据
            self.account_tab.load_cookie_files(cookie_path, show_message=True, delay_data_loading=False)

        except Exception as e:
            logging.error(f"手动加载账号时出错: {str(e)}")
            import traceback
            logging.error(traceback.format_exc())
            QMessageBox.critical(self, "加载错误", f"加载账号时发生错误: {str(e)}")

            # 紧急重置账号标签页状态
            self._emergency_reset_account_tab()

    def _emergency_reset_account_tab(self):
        """紧急重置账号标签页状态 - 防止按钮卡死"""
        try:
            logging.info("执行紧急重置账号标签页状态...")

            if hasattr(self, 'account_tab') and self.account_tab:
                # 重置加载状态
                self.account_tab.is_loading = False
                self.account_tab.background_loading_active = False

                # 清除按钮点击标志
                if hasattr(self.account_tab, '_button_click_in_progress'):
                    self.account_tab._button_click_in_progress = False

                # 重新启用加载按钮
                if hasattr(self.account_tab, '_re_enable_load_button'):
                    self.account_tab._re_enable_load_button()

                # 关闭进度对话框
                if hasattr(self.account_tab, 'progress_dialog') and self.account_tab.progress_dialog:
                    try:
                        self.account_tab.progress_dialog.accept()
                        self.account_tab.progress_dialog = None
                    except:
                        pass

                # 确保表格更新启用
                try:
                    self.account_tab.table.setUpdatesEnabled(True)
                except:
                    pass

                logging.info("紧急重置完成")

        except Exception as e:
            logging.error(f"紧急重置账号标签页状态时出错: {str(e)}")

    def on_settings_saved(self, settings):
        """设置保存后处理

        Args:
            settings: 保存的设置字典
        """
        # 更新账号标签页中的cookie_path属性
        if "cookie_path" in settings:
            self.account_tab.cookie_path = settings["cookie_path"]
            logging.info(f"设置已保存，Cookie路径已更新为: {settings['cookie_path']}，准备加载账号基本信息")

            # 显示状态栏消息
            status_bar = self.statusBar()
            if status_bar:
                status_bar.showMessage("设置已保存，正在加载账号基本信息...", 3000)

            # 恢复自动加载账号功能，设置保存后自动加载账号
            self.account_tab.load_cookie_files(settings["cookie_path"], show_message=False, delay_data_loading=True)

        # 确保设置账号已加载标志
        if not hasattr(self.account_tab, 'accounts_loaded'):
            self.account_tab.accounts_loaded = True

    def delayed_initialization(self):
        """延迟初始化非关键功能，提高启动速度"""
        try:
            # 检查是否已经初始化过，避免重复执行
            if hasattr(self, '_delayed_init_completed') and self._delayed_init_completed:
                logging.info("延迟初始化已完成，跳过重复执行")
                return

            # 标记开始初始化
            self._delayed_init_completed = True
            # 导入垃圾回收模块
            import gc

            # 执行一次垃圾回收，确保开始时内存干净
            gc.collect()

            # 显示状态栏消息，提示用户正在初始化
            status_bar = self.statusBar()
            if status_bar:
                status_bar.showMessage("正在初始化系统组件，请稍候...", 3000)

            # 处理Qt事件，确保UI响应
            QApplication.processEvents()

            # 初始化用户状态监测
            self.init_user_status_checker()

            # 处理Qt事件，确保UI响应
            QApplication.processEvents()

            # 再次执行垃圾回收
            gc.collect()

            # 显示状态栏消息，提示用户需要手动加载账号
            if status_bar:
                status_bar.showMessage("请点击'加载账号'按钮加载账号数据", 5000)

            # 最终执行垃圾回收
            gc.collect()

            logging.info("延迟初始化完成，账号数据需要手动加载")
        except Exception as e:
            logging.error(f"延迟初始化时出错: {str(e)}")
            import traceback
            logging.error(traceback.format_exc())  # 添加详细的错误堆栈

    def init_auto_load_accounts(self):
        """启动时检查是否需要自动加载账号"""
        try:
            # 导入垃圾回收模块
            import gc

            # 执行垃圾回收
            gc.collect()

            # 检查设置中是否启用了自动加载
            try:
                settings_file = os.path.join(os.getcwd(), "data", "settings.json")
                if os.path.exists(settings_file):
                    with open(settings_file, "r", encoding="utf-8") as f:
                        settings = json.load(f)

                    auto_load = settings.get("auto_load_accounts", True)
                    cookie_path = settings.get("cookie_path", "")
                    fast_startup = settings.get("fast_startup_enabled", True)  # 默认启用快速启动

                    if auto_load and cookie_path and os.path.exists(cookie_path):
                        logging.info(f"启动时自动加载账号，路径: {cookie_path}，快速启动: {fast_startup}")

                        if fast_startup:
                            # 快速启动模式：立即显示界面，后台加载数据
                            status_bar = self.statusBar()
                            if status_bar:
                                status_bar.showMessage("快速启动模式 - 正在后台加载账号数据...", 3000)
                            # 账号标签页会自动启动后台加载
                        else:
                            # 传统模式：延迟加载
                            self.create_single_shot_timer(3000, lambda: self._delayed_load_accounts(cookie_path))
                            status_bar = self.statusBar()
                            if status_bar:
                                status_bar.showMessage("正在自动加载账号数据...", 5000)
                    else:
                        # 显示状态栏消息，提示用户需要手动加载账号
                        status_bar = self.statusBar()
                        if status_bar:
                            if auto_load:
                                status_bar.showMessage("未设置Cookie路径，请在设置中配置后重启", 8000)
                            else:
                                status_bar.showMessage("请点击'加载账号'按钮加载账号数据", 5000)
                else:
                    logging.warning("设置文件不存在，无法自动加载账号")
            except Exception as e:
                logging.error(f"读取自动加载设置时出错: {str(e)}")

        except Exception as e:
            logging.error(f"初始化自动加载账号时出错: {str(e)}")
            import traceback
            logging.error(traceback.format_exc())  # 添加详细的错误堆栈

    def _delayed_load_accounts(self, cookie_path=None):
        """延迟加载账号

        Args:
            cookie_path: Cookie文件路径（可选）
        """
        try:
            # 导入垃圾回收模块
            import gc

            # 执行垃圾回收
            gc.collect()

            # 确保主界面已经完全显示
            QApplication.processEvents()

            if cookie_path and os.path.exists(cookie_path):
                logging.info(f"开始自动加载账号，路径: {cookie_path}")

                # 显示状态栏消息
                status_bar = self.statusBar()
                if status_bar:
                    status_bar.showMessage("正在自动加载账号数据...", 3000)

                # 更新账号标签页中的cookie_path属性
                if hasattr(self, 'account_tab'):
                    self.account_tab.cookie_path = cookie_path
                    # 使用延迟加载模式，提高启动速度
                    self.account_tab.load_cookie_files(cookie_path, show_message=False, delay_data_loading=True)
                    logging.info("账号自动加载完成")
                else:
                    logging.warning("账号标签页未初始化，无法自动加载账号")
            else:
                logging.info("Cookie路径无效或不存在，跳过自动加载")
                # 显示状态栏消息，提示用户需要手动加载账号
                status_bar = self.statusBar()
                if status_bar:
                    status_bar.showMessage("请点击'加载账号'按钮加载账号数据", 5000)

            # 处理Qt事件，确保UI响应
            QApplication.processEvents()

        except Exception as e:
            logging.error(f"延迟加载账号时出错: {str(e)}")
            import traceback
            logging.error(traceback.format_exc())  # 添加详细的错误堆栈

    def init_user_status_checker(self):
        """初始化用户状态监测"""
        try:
            # 检查是否已经初始化过，避免重复创建
            if hasattr(self, 'status_checker') and self.status_checker is not None:
                logging.info("用户状态监测已经初始化，跳过重复初始化")
                return

            # 创建用户状态检测器实例，设置检查间隔为300秒（5分钟）
            self.status_checker = UserStatusChecker(self, check_interval=300)

            # 系统日志功能已移除

            # 创建状态指示器但不添加到状态栏（避免与公网IP显示冲突）
            # 仍然创建指示器以便在内部使用，但不显示在状态栏中
            self.status_checker.setup_status_indicator(self)

            # 将状态指示器添加到状态栏
            status_bar = self.statusBar()
            if not status_bar:
                status_bar = QStatusBar()
                self.setStatusBar(status_bar)

            # 添加状态文本标签（不再添加状态指示器，避免与公网IP显示冲突）
            self.status_text_label = QLabel("用户状态: 正常")
            self.status_text_label.setProperty("status_normal", True)
            status_bar.addPermanentWidget(self.status_text_label)

            # 连接状态变化信号
            self.status_checker.status_changed_signal.connect(self.update_status_text)

            # 强制重新加载配置，确保使用正确的配置文件路径
            self.status_checker.load_config()

            # 延迟启动状态检测，避免启动时卡顿
            def delayed_start_checking():
                try:
                    # 检查是否已经在应用启动时验证过卡密
                    from app.utils.auto_login_helper import _login_success_flag

                    if _login_success_flag:
                        logging.info("检测到已经在应用启动时验证过卡密，使用现有配置启动状态监测")
                        # 直接启动状态检测，不再重复验证卡密
                        result = self.status_checker.start_checking()
                        if result:
                            logging.info("用户状态监测已启动，检查间隔: 300秒")
                        else:
                            logging.warning("用户状态监测启动失败，可能是登录信息不完整")
                    else:
                        logging.info("未检测到应用启动时的卡密验证标志，执行完整验证流程")
                        result = self.status_checker.start_checking()
                        if result:
                            logging.info("用户状态监测已启动，检查间隔: 300秒")
                        else:
                            logging.warning("用户状态监测启动失败，可能是登录信息不完整")

                            # 尝试从主窗口获取卡密信息
                            try:
                                from app.utils.kamidenglu import load_config, save_config
                                config = load_config() or {}

                                # 如果主窗口有卡密信息但配置中没有，则更新配置
                                if hasattr(self, 'card_number') and self.card_number and not config.get('card'):
                                    logging.info(f"从主窗口获取到卡密信息，更新配置")
                                    config['card'] = self.card_number
                                    save_config(config)

                                    # 重新加载配置并启动检测
                                    self.status_checker.load_config()
                                    result = self.status_checker.start_checking()
                                    if result:
                                        logging.info("使用主窗口卡密信息启动用户状态监测成功")
                                    else:
                                        logging.warning("即使使用主窗口卡密信息，状态监测仍然启动失败")
                            except Exception as e:
                                logging.error(f"尝试使用主窗口卡密信息时出错: {str(e)}")
                except Exception as e:
                    logging.error(f"延迟启动用户状态检测时出错: {str(e)}")

            # 延迟3秒后启动状态检测
            self.create_single_shot_timer(3000, delayed_start_checking)
            logging.info("用户状态监测初始化完成，将在3秒后启动")
        except Exception as e:
            logging.error(f"初始化用户状态监测时出错: {str(e)}")

    def update_status_text(self, is_normal, message):
        """更新状态文本

        Args:
            is_normal: 状态是否正常
            message: 状态消息
        """
        if hasattr(self, 'status_text_label'):
            # 使用传入的消息或默认文本
            status_text = message if message else ("正常" if is_normal else "异常")
            self.status_text_label.setText(f"用户状态: {status_text}")

            # 根据状态设置不同的属性
            self.status_text_label.setProperty("status_normal", is_normal)
            try:
                style = self.style()
                if style:
                    style.unpolish(self.status_text_label)
                    style.polish(self.status_text_label)
            except Exception:
                pass



    def stop_all_tasks(self):
        """立即停止所有正在运行的任务 - 静态停止模式，无确认对话框，后台执行"""
        try:
            # 防止重复点击
            if hasattr(self, '_is_stopping') and self._is_stopping:
                logging.info("停止操作正在进行中，忽略重复点击")
                return

            self._is_stopping = True
            logging.info("用户请求立即停止所有任务（静态模式）")

            # 立即更新按钮状态，提供视觉反馈
            self.stop_btn.setEnabled(False)
            self.stop_btn.setText("停止中...")
            self.stop_btn.setStyleSheet("""
                #stopBtn {
                    background-color: #f39c12;
                    color: white;
                    font-weight: bold;
                    border: none;
                    border-radius: 3px;
                    padding: 4px 8px;
                    font-size: 10px;
                    min-height: 20px;
                    max-height: 24px;
                }
            """)

            # 在状态栏显示停止状态
            status_bar = self.statusBar()
            if status_bar:
                status_bar.showMessage("正在停止所有任务...", 0)  # 0表示不自动消失

            # 立即刷新界面
            QApplication.processEvents()

            # 启动后台停止线程
            self._start_background_stop_thread()

        except Exception as e:
            logging.error(f"启动停止任务时出错: {str(e)}")
            self._reset_stop_button_state()

    def _start_background_stop_thread(self):
        """启动后台停止线程"""
        try:
            from PyQt5.QtCore import QThread, QObject, pyqtSignal

            class BackgroundStopWorker(QObject):
                """后台停止工作线程"""
                finished = pyqtSignal(bool, str)  # 完成信号(成功, 消息)

                def __init__(self, main_window):
                    super().__init__()
                    self.main_window = main_window
                    self.timeout_seconds = 10  # 10秒超时

                def stop_tasks(self):
                    """执行停止任务操作"""
                    import time
                    start_time = time.time()

                    try:
                        logging.info("开始后台停止任务操作")

                        # 1. 停止账号标签页的任务（静默模式）
                        if hasattr(self.main_window, 'account_tab'):
                            self._stop_account_tab_tasks()

                        # 2. 停止其他可能的任务
                        self._stop_other_tasks()

                        # 3. 清理资源
                        self._cleanup_resources()

                        elapsed = time.time() - start_time
                        logging.info(f"后台停止任务完成，耗时: {elapsed:.2f}秒")
                        self.finished.emit(True, "所有任务已停止")

                    except Exception as e:
                        elapsed = time.time() - start_time
                        logging.error(f"后台停止任务出错，耗时: {elapsed:.2f}秒，错误: {str(e)}")
                        self.finished.emit(False, f"停止任务时出错: {str(e)}")

                def _stop_account_tab_tasks(self):
                    """停止账号标签页任务（静默模式）"""
                    try:
                        account_tab = self.main_window.account_tab

                        # 停止批量存稿任务
                        if hasattr(account_tab, 'batch_worker') and account_tab.batch_worker:
                            if hasattr(account_tab.batch_worker, 'stop'):
                                account_tab.batch_worker.stop()
                                logging.info("批量存稿任务已停止")

                        # 停止数据采集任务
                        if hasattr(account_tab, 'data_collector') and account_tab.data_collector:
                            if hasattr(account_tab.data_collector, 'stop_all_threads'):
                                account_tab.data_collector.stop_all_threads()
                                logging.info("数据采集任务已停止")

                        # 停止活动线程
                        if hasattr(account_tab, 'active_threads'):
                            self._stop_active_threads(account_tab.active_threads)

                    except Exception as e:
                        logging.error(f"停止账号标签页任务时出错: {str(e)}")

                def _stop_active_threads(self, active_threads):
                    """停止活动线程"""
                    try:
                        if not active_threads:
                            return

                        stopped_count = 0
                        for thread, worker in active_threads.copy():
                            try:
                                # 尝试优雅停止
                                if worker and hasattr(worker, 'stop'):
                                    worker.stop()
                                elif worker and hasattr(worker, 'abort'):
                                    worker.abort()

                                # 等待线程结束（最多2秒）
                                if thread and thread.isRunning():
                                    if thread.wait(2000):  # 等待2秒
                                        stopped_count += 1
                                    else:
                                        # 强制终止
                                        thread.terminate()
                                        thread.wait(1000)  # 再等1秒
                                        stopped_count += 1

                            except Exception as e:
                                logging.error(f"停止线程时出错: {str(e)}")

                        logging.info(f"已停止 {stopped_count} 个活动线程")

                    except Exception as e:
                        logging.error(f"停止活动线程时出错: {str(e)}")

                def _stop_other_tasks(self):
                    """停止其他任务"""
                    try:
                        # 这里可以添加其他需要停止的任务
                        pass
                    except Exception as e:
                        logging.error(f"停止其他任务时出错: {str(e)}")

                def _cleanup_resources(self):
                    """清理资源"""
                    try:
                        # 执行垃圾回收
                        import gc
                        gc.collect()
                        logging.info("资源清理完成")
                    except Exception as e:
                        logging.error(f"清理资源时出错: {str(e)}")

            # 创建线程和工作对象
            self._stop_thread = QThread()
            self._stop_worker = BackgroundStopWorker(self)
            self._stop_worker.moveToThread(self._stop_thread)

            # 连接信号
            self._stop_thread.started.connect(self._stop_worker.stop_tasks)
            self._stop_worker.finished.connect(self._on_background_stop_finished)
            self._stop_worker.finished.connect(self._stop_thread.quit)

            # 启动线程
            self._stop_thread.start()

            # 设置超时保护
            self.create_single_shot_timer(15000, self._on_stop_timeout)  # 15秒超时

        except Exception as e:
            logging.error(f"启动后台停止线程时出错: {str(e)}")
            self._reset_stop_button_state()

    def _on_background_stop_finished(self, success, message):
        """后台停止完成处理"""
        try:
            logging.info(f"后台停止完成: 成功={success}, 消息={message}")

            # 重置停止状态
            self._is_stopping = False

            # 恢复按钮状态
            self._reset_stop_button_state()

            # 在状态栏显示结果
            status_bar = self.statusBar()
            if status_bar:
                if success:
                    status_bar.showMessage(message, 3000)
                else:
                    status_bar.showMessage(f"停止失败: {message}", 5000)

        except Exception as e:
            logging.error(f"处理后台停止完成时出错: {str(e)}")
            self._reset_stop_button_state()

    def _on_stop_timeout(self):
        """停止操作超时处理"""
        try:
            if hasattr(self, '_is_stopping') and self._is_stopping:
                logging.warning("停止操作超时，强制重置状态")

                # 强制终止停止线程
                if hasattr(self, '_stop_thread') and self._stop_thread.isRunning():
                    self._stop_thread.terminate()
                    self._stop_thread.wait(1000)

                # 重置状态
                self._is_stopping = False
                self._reset_stop_button_state()

                # 显示超时消息
                status_bar = self.statusBar()
                if status_bar:
                    status_bar.showMessage("停止操作超时，已强制重置", 5000)

        except Exception as e:
            logging.error(f"处理停止超时时出错: {str(e)}")

    def _reset_stop_button_state(self):
        """重置停止按钮状态"""
        try:
            self._is_stopping = False
            self.stop_btn.setEnabled(True)
            self.stop_btn.setText("停止任务")
            self.stop_btn.setStyleSheet("""
                #stopBtn {
                    background-color: #e74c3c;
                    color: white;
                    font-weight: bold;
                    border: none;
                    border-radius: 3px;
                    padding: 4px 8px;
                    font-size: 10px;
                    min-height: 20px;
                    max-height: 24px;
                }
                #stopBtn:hover {
                    background-color: #c0392b;
                }
            """)
        except Exception as e:
            logging.error(f"重置停止按钮状态时出错: {str(e)}")

    def update_total_earnings(self):
        """计算并更新所有账号的7天总收益和昨日收益"""
        try:
            # 确保账号标签页已初始化
            if not hasattr(self, 'account_tab') or not self.account_tab:
                logging.warning("账号标签页未初始化，无法计算总收益")
                return

            # 确保表格已初始化
            if not hasattr(self.account_tab, 'table') or not self.account_tab.table:
                logging.warning("账号表格未初始化，无法计算总收益")
                return

            total_7day_income = 0.0
            total_yesterday_income = 0.0
            total_rows = self.account_tab.table.rowCount()
            column_count = self.account_tab.table.columnCount()

            logging.info(f"开始计算收益统计，表格行数: {total_rows}, 列数: {column_count}")

            # 打印表头信息，帮助调试
            headers = []
            for col in range(column_count):
                header_item = self.account_tab.table.horizontalHeaderItem(col)
                if header_item:
                    headers.append(f"{col}:{header_item.text()}")
                else:
                    headers.append(f"{col}:None")
            logging.info(f"表格列标题: {headers}")

            # 遍历所有行，计算7天总收益和昨日收益
            for row in range(total_rows):
                # 只在前5行打印详细调试信息
                if row < 5:
                    # 打印当前行的所有数据
                    row_data = []
                    for col in range(column_count):
                        item = self.account_tab.table.item(row, col)
                        if item:
                            row_data.append(f"{col}:{item.text()}")
                        else:
                            row_data.append(f"{col}:None")
                    logging.info(f"行 {row} 数据: {row_data}")
                # 获取七天收益列（索引11）的数据
                seven_day_income_item = self.account_tab.table.item(row, 11)
                if seven_day_income_item and seven_day_income_item.text():
                    try:
                        # 处理可能的格式，如 "¥5.23" 或 "5.23"
                        income_str = seven_day_income_item.text().replace("¥", "").replace(",", "").strip()
                        if income_str and income_str != "--":
                            income_value = float(income_str)
                            total_7day_income += income_value
                            if row < 5 or income_value > 0:  # 只在前5行或有收益时输出日志
                                logging.info(f"行 {row} 七天收益: {income_value}")
                    except ValueError:
                        logging.warning(f"无法解析七天收益值: {seven_day_income_item.text()}")
                        continue

                # 获取昨日收益列（索引10）的数据
                yesterday_income_item = self.account_tab.table.item(row, 10)
                if yesterday_income_item and yesterday_income_item.text():
                    try:
                        # 处理可能的格式，如 "¥5.23" 或 "5.23" 或 "+5.23" 或 "-5.23"
                        income_str = yesterday_income_item.text().replace("¥", "").replace(",", "").strip()
                        if income_str and income_str != "--":
                            # 移除可能的正负号前缀，但保留数值的正负性
                            if income_str.startswith('+'):
                                income_str = income_str[1:]
                            income_value = float(income_str)
                            total_yesterday_income += income_value
                            if row < 5 or income_value > 0:  # 只在前5行或有收益时输出日志
                                logging.info(f"行 {row} 昨日收益: {income_value}")
                    except ValueError:
                        logging.warning(f"无法解析昨日收益值: {yesterday_income_item.text()}")
                        continue

            # 输出最终计算结果
            logging.info(f"收益统计计算完成 - 7天总收益: ¥{total_7day_income:.2f}, 昨日总收益: ¥{total_yesterday_income:.2f}")

            # 更新收益统计组件
            if hasattr(self, 'earnings_stats'):
                logging.info(f"更新7天总收益统计: ¥{total_7day_income:.2f}")
                self.earnings_stats.update_earnings(total_7day_income)

                # 更新第二个收益统计组件显示昨日收益
                if hasattr(self, 'earnings_stats_2'):
                    logging.info(f"更新昨日总收益统计: ¥{total_yesterday_income:.2f}")
                    self.earnings_stats_2.update_earnings(total_yesterday_income)
            else:
                logging.warning("收益统计组件未初始化")
        except Exception as e:
            logging.error(f"计算收益统计时出错: {str(e)}")
            import traceback
            logging.error(traceback.format_exc())

    def check_first_run(self):
        """检查是否是首次运行，如果是则显示教程"""
        try:
            # 获取应用程序路径
            if getattr(sys, 'frozen', False):
                # 如果是打包后的环境
                application_path = os.path.dirname(sys.executable)
            else:
                # 如果是开发环境
                application_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

            # 读取设置文件
            settings_file = os.path.join(application_path, "settings.json")
            if os.path.exists(settings_file):
                with open(settings_file, "r", encoding="utf-8") as f:
                    settings = json.load(f)

                # 检查是否是首次运行
                if settings.get("first_run", True):
                    logging.info("检测到首次运行，显示软件教程")

                    # 切换到设置标签页
                    for i in range(self.tab_widget.count()):
                        if self.tab_widget.tabText(i) == "设置":
                            self.tab_widget.setCurrentIndex(i)
                            break

                    # 切换到教程标签页
                    if hasattr(self, 'setting_tab') and hasattr(self.setting_tab, 'setting_tabs'):
                        for i in range(self.setting_tab.setting_tabs.count()):
                            if self.setting_tab.setting_tabs.tabText(i) == "软件教程":
                                self.setting_tab.setting_tabs.setCurrentIndex(i)
                                break

                    # 更新设置，标记为非首次运行
                    settings["first_run"] = False
                    with open(settings_file, "w", encoding="utf-8") as f:
                        json.dump(settings, f, ensure_ascii=False, indent=2)

                    logging.info("已更新首次运行标记")
        except Exception as e:
            logging.error(f"检查首次运行状态时出错: {str(e)}")

    def update_account_count(self, count):
        """更新状态栏中的账号数量

        Args:
            count: 当前账号数量
        """
        self.total_count_label.setText(f"实际数量: {count}")

    # 表格刷新完成信号处理方法已移除，避免段错误

    def process_pending_events(self):
        """处理界面挂起的事件，确保界面响应

        使用极度保守的事件处理方式，大幅降低系统负担
        """
        try:
            # 获取当前系统状态
            current_time = time.time()

            # 记录上次处理时间，用于计算负载
            if not hasattr(self, '_last_event_process_time'):
                self._last_event_process_time = current_time
                self._event_process_count = 0
                self._ui_load_level = 0  # 0-10的负载级别
                self._last_load_check_time = current_time
                self._last_repaint_time = current_time
                self._last_full_process_time = current_time

            # 增加处理计数
            self._event_process_count += 1

            # 检查是否存在高负载操作
            high_load_operation = False

            # 检查批量存稿线程
            if hasattr(self, '_batch_draft_thread') and self._batch_draft_thread and self._batch_draft_thread.isRunning():
                high_load_operation = True
                # 批量存稿运行时，大幅降低UI刷新频率
                if current_time - self._last_full_process_time < 5.0:  # 每5秒才进行一次完整处理
                    # 只处理最小限度的事件，不进行重绘
                    QApplication.processEvents()
                    return
                self._last_full_process_time = current_time

            # 每5秒评估一次UI负载（从3秒增加到5秒，大幅减少评估频率）
            if current_time - self._last_load_check_time > 5.0:
                # 计算每秒处理的事件数量
                events_per_second = self._event_process_count / (current_time - self._last_load_check_time)

                # 根据事件处理频率动态调整负载级别，大幅降低阈值
                if events_per_second > 20:  # 从30降低到20
                    self._ui_load_level = min(10, self._ui_load_level + 1)
                elif events_per_second < 3:  # 从5降低到3
                    self._ui_load_level = max(0, self._ui_load_level - 1)

                # 重置计数器
                self._event_process_count = 0
                self._last_load_check_time = current_time

                # 记录负载级别
                logging.debug(f"UI负载级别: {self._ui_load_level}, 事件处理频率: {events_per_second:.1f}/秒")

                # 根据负载级别动态调整UI刷新定时器间隔
                if self._ui_load_level >= 8:  # 高负载
                    self.ui_refresh_timer.start(5000)  # 5秒
                elif self._ui_load_level >= 5:  # 中等负载
                    self.ui_refresh_timer.start(3000)  # 3秒
                else:  # 低负载
                    self.ui_refresh_timer.start(2000)  # 2秒

            # 使用极度保守的方式处理事件，减少系统负担
            QApplication.processEvents()

            # 检查当前活动的标签页
            current_tab_index = self.tab_widget.currentIndex()
            current_tab_name = self.tab_widget.tabText(current_tab_index)

            # 账号管理标签可能需要更高的刷新率，但仍然保持合理范围
            if current_tab_name == "账号管理" and hasattr(self.account_tab, 'is_loading') and self.account_tab.is_loading:
                high_load_operation = True

            # 智能重绘 - 使用极度保守的重绘策略，减少不必要的重绘
            should_repaint = False

            # 低负载且超过5秒未重绘（从3秒增加到5秒）
            if self._ui_load_level <= 3 and current_time - self._last_repaint_time > 5.0:
                should_repaint = True
            # 中等负载且超过10秒未重绘（从5秒增加到10秒）
            elif self._ui_load_level <= 7 and current_time - self._last_repaint_time > 10.0:
                should_repaint = True
            # 高负载且超过20秒未重绘（从10秒增加到20秒）
            elif current_time - self._last_repaint_time > 20.0:
                should_repaint = True

            if should_repaint and not high_load_operation:
                self.repaint()
                self._last_repaint_time = current_time

        except Exception as e:
            # 如果事件处理过程中出错，不应影响程序运行
            logging.error(f"处理界面事件时出错: {str(e)}")
            # 降低刷新频率，避免问题持续触发
            self.ui_refresh_timer.start(5000)  # 设置为5秒，大幅降低频率

    def closeEvent(self, a0):
        """窗口关闭事件处理

        确保在应用程序关闭前清理所有线程
        """
        # 停止UI刷新定时器
        if hasattr(self, 'ui_refresh_timer'):
            try:
                self.ui_refresh_timer.stop()
            except Exception:
                pass

        # 停止系统监控
        try:
            if hasattr(self, 'monitor'):
                self.monitor.stop()
                logging.info("系统监控已停止")
        except Exception as e:
            logging.error(f"停止系统监控时出错: {str(e)}")

        # 停止用户状态监测
        try:
            if hasattr(self, 'status_checker'):
                self.status_checker.stop_checking()
                logging.info("用户状态监测已停止")
        except Exception as e:
            logging.error(f"停止用户状态监测时出错: {str(e)}")

        # 清理批量存稿线程 - 使用更安全的方式
        try:
            if hasattr(self, '_batch_draft_thread') and self._batch_draft_thread:
                if self._batch_draft_thread.isRunning():
                    info("正在停止批量存稿线程...")
                    self._batch_draft_thread.quit()
                    # 增加等待时间到10秒，确保线程有足够时间停止
                    if not self._batch_draft_thread.wait(10000):
                        warning("批量存稿线程停止超时，强制终止")
                        self._batch_draft_thread.terminate()
                        # 等待强制终止完成
                        if not self._batch_draft_thread.wait(5000):
                            error("批量存稿线程强制终止失败")
                        else:
                            info("批量存稿线程已强制终止")
                    else:
                        info("批量存稿线程已正常停止")

                # 确保线程完全停止后再清理引用
                if not self._batch_draft_thread.isRunning():
                    self._batch_draft_thread = None
                    info("已清理批量存稿线程引用")
                else:
                    warning("线程仍在运行，但强制清理引用")
                    self._batch_draft_thread = None

            if hasattr(self, '_batch_draft_worker'):
                self._batch_draft_worker = None
                info("已清理批量存稿工作器引用")
        except Exception as e:
            error(f"清理批量存稿线程时出错: {str(e)}")
            # 即使出错也要清理引用
            if hasattr(self, '_batch_draft_thread'):
                self._batch_draft_thread = None
            if hasattr(self, '_batch_draft_worker'):
                self._batch_draft_worker = None

        # 先让各个标签页有机会清理其线程
        if hasattr(self.account_tab, 'stop_threads'):
            self.account_tab.stop_threads()

        # 确保等待一小段时间，让线程有机会完成清理
        time.sleep(0.5)

        # 暂时禁用ThreadManager.cleanup()以避免Qt Fatal错误
        # 让Qt自己处理线程清理
        try:
            info("跳过ThreadManager.cleanup()，让Qt自己处理线程清理")
            # ThreadManager.cleanup()  # 暂时禁用
            info("线程清理跳过")
        except Exception as e:
            error(f"清理线程时出错: {str(e)}")
            # 即使清理失败，也要继续关闭程序

        # 再次等待一小段时间确保线程清理完成
        time.sleep(0.5)

        # 调用父类方法，允许关闭窗口
        super().closeEvent(a0)