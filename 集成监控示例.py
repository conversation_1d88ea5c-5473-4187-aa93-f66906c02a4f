#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
头条存稿工具监控集成示例 - 展示如何将监控功能集成到现有代码中
"""

import sys
import os
import logging
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# 导入监控模块
from app.monitoring.toutiao_integration import (
    integrate_monitoring, monitor_toutiao_operation, monitor_toutiao_button,
    track_toutiao_state, safe_toutiao_operation, update_account_state,
    update_batch_state, log_account_operation, log_batch_operation
)
from app.monitoring.deep_monitor import (
    log_button_click, log_operation_start, log_operation_complete,
    update_state, log_browser_event
)

class MonitoredMainWindow:
    """示例：集成监控的主窗口类"""
    
    def __init__(self):
        self.account_count = 0
        self.batch_status = "停止"
        self.browser_status = "未启动"
        
        # 集成监控（在实际代码中，这应该在主窗口初始化后调用）
        # integrate_monitoring(main_window=self)
        
    @monitor_toutiao_button("开始批量存稿", "主窗口")
    @monitor_toutiao_operation("开始批量存稿")
    @safe_toutiao_operation("开始批量存稿")
    def start_batch_process(self):
        """开始批量存稿处理"""
        print("🚀 开始批量存稿处理...")
        
        # 更新状态
        self.batch_status = "运行中"
        update_batch_state("处理状态", self.batch_status)
        update_batch_state("开始时间", datetime.now().isoformat())
        
        # 模拟处理过程
        import time
        time.sleep(2)
        
        print("✅ 批量存稿处理已启动")
        return True
        
    @monitor_toutiao_button("停止批量存稿", "主窗口")
    @monitor_toutiao_operation("停止批量存稿")
    @safe_toutiao_operation("停止批量存稿")
    def stop_batch_process(self):
        """停止批量存稿处理"""
        print("⏹️ 停止批量存稿处理...")
        
        # 更新状态
        self.batch_status = "已停止"
        update_batch_state("处理状态", self.batch_status)
        update_batch_state("停止时间", datetime.now().isoformat())
        
        print("✅ 批量存稿处理已停止")
        return True
        
    @monitor_toutiao_button("加载账号", "主窗口")
    @monitor_toutiao_operation("加载账号")
    @safe_toutiao_operation("加载账号")
    def load_accounts(self):
        """加载账号列表"""
        print("📋 加载账号列表...")
        
        # 记录操作开始
        operation_id = log_account_operation("加载账号列表", {"source": "本地文件"})
        
        try:
            # 模拟加载过程
            import time
            time.sleep(1)
            
            # 更新账号数量
            self.account_count = 5
            update_account_state("账号数量", self.account_count)
            update_account_state("最后加载时间", datetime.now().isoformat())
            
            # 记录操作完成
            if operation_id:
                log_operation_complete(operation_id, True, f"成功加载 {self.account_count} 个账号")
            
            print(f"✅ 成功加载 {self.account_count} 个账号")
            return True
            
        except Exception as e:
            # 记录操作失败
            if operation_id:
                log_operation_complete(operation_id, False, f"加载失败: {str(e)}")
            raise
            
    @monitor_toutiao_operation("启动浏览器")
    @safe_toutiao_operation("启动浏览器")
    def start_browser(self):
        """启动浏览器"""
        print("🌐 启动浏览器...")
        
        # 记录浏览器事件
        log_browser_event("browser_start", {
            "browser_type": "Chrome",
            "headless": False,
            "user_agent": "Custom Agent"
        })
        
        # 更新浏览器状态
        self.browser_status = "已启动"
        update_state("浏览器", "状态", self.browser_status)
        update_state("浏览器", "启动时间", datetime.now().isoformat())
        
        print("✅ 浏览器启动成功")
        return True
        
    @monitor_toutiao_operation("模拟异常")
    @safe_toutiao_operation("模拟异常")
    def simulate_error(self):
        """模拟异常情况"""
        print("⚠️ 模拟异常情况...")
        
        # 故意抛出异常来测试异常监控
        raise ValueError("这是一个模拟的异常，用于测试监控系统")

def demonstrate_monitoring():
    """演示监控功能"""
    print("🎯 头条存稿工具监控集成演示")
    print("=" * 50)
    
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    # 创建示例窗口
    window = MonitoredMainWindow()
    
    print("\n📋 演示步骤:")
    print("1. 加载账号")
    print("2. 启动浏览器")
    print("3. 开始批量处理")
    print("4. 停止批量处理")
    print("5. 模拟异常")
    print("")
    
    try:
        # 步骤1：加载账号
        print("🔄 步骤1: 加载账号")
        window.load_accounts()
        print("")
        
        # 步骤2：启动浏览器
        print("🔄 步骤2: 启动浏览器")
        window.start_browser()
        print("")
        
        # 步骤3：开始批量处理
        print("🔄 步骤3: 开始批量处理")
        window.start_batch_process()
        print("")
        
        # 步骤4：停止批量处理
        print("🔄 步骤4: 停止批量处理")
        window.stop_batch_process()
        print("")
        
        # 步骤5：模拟异常
        print("🔄 步骤5: 模拟异常")
        try:
            window.simulate_error()
        except ValueError as e:
            print(f"✅ 异常已被监控系统捕获: {str(e)}")
        print("")
        
        print("🎉 演示完成！")
        print("")
        print("📊 监控数据说明:")
        print("   • 所有按钮点击都被记录")
        print("   • 所有操作的开始和结束都被监控")
        print("   • 状态变化被实时跟踪")
        print("   • 异常被安全捕获和记录")
        print("   • 浏览器事件被详细记录")
        print("")
        print("🔍 查看监控数据:")
        print("   1. 启动监控系统: python monitor_launcher.py")
        print("   2. 点击'开始监控'按钮")
        print("   3. 在各个标签页查看监控数据:")
        print("      - 用户交互: 查看按钮点击和操作记录")
        print("      - 状态监控: 查看状态变化历史")
        print("      - 异常监控: 查看异常和错误信息")
        print("")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

def show_integration_guide():
    """显示集成指南"""
    print("📖 头条存稿工具监控集成指南")
    print("=" * 50)
    print("")
    
    print("🔧 1. 基本集成步骤:")
    print("   a) 在主程序中导入监控模块:")
    print("      from app.monitoring.toutiao_integration import integrate_monitoring")
    print("")
    print("   b) 在主窗口初始化后调用集成函数:")
    print("      integrate_monitoring(")
    print("          main_window=self,")
    print("          account_tab=self.account_tab,")
    print("          batch_tab=self.batch_tab,")
    print("          browser_manager=self.browser_manager")
    print("      )")
    print("")
    
    print("🎯 2. 使用装饰器监控方法:")
    print("   @monitor_toutiao_operation('操作名称')")
    print("   @safe_toutiao_operation('操作名称')")
    print("   def your_method(self):")
    print("       # 你的代码")
    print("       pass")
    print("")
    
    print("📊 3. 手动记录状态变化:")
    print("   from app.monitoring.toutiao_integration import update_batch_state")
    print("   update_batch_state('任务状态', '运行中')")
    print("")
    
    print("🔍 4. 记录用户操作:")
    print("   from app.monitoring.deep_monitor import log_button_click")
    print("   log_button_click('按钮名称', '窗口名称')")
    print("")
    
    print("⚠️ 5. 异常安全处理:")
    print("   所有被装饰的方法都会自动捕获异常并发送到监控系统")
    print("   异常会被记录但不会被抑制，原有的异常处理逻辑保持不变")
    print("")
    
    print("📈 6. 性能监控:")
    print("   使用 @monitor_toutiao_operation 装饰器会自动记录:")
    print("   • 操作开始时间")
    print("   • 操作结束时间")
    print("   • 操作持续时间")
    print("   • 操作成功/失败状态")
    print("")

if __name__ == "__main__":
    print("选择演示模式:")
    print("1. 运行监控功能演示")
    print("2. 显示集成指南")
    
    choice = input("请输入选择 (1 或 2): ").strip()
    
    if choice == "1":
        demonstrate_monitoring()
    elif choice == "2":
        show_integration_guide()
    else:
        print("无效选择，显示集成指南...")
        show_integration_guide()
    
    input("\n按回车键退出...")
