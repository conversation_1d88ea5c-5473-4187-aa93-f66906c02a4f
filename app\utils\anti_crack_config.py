#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
防破解配置管理模块
提供防破解检测的配置管理功能
"""

import os
import json
import hashlib
import time
import base64
from typing import Dict, Any, List, Optional, Union
from cryptography.fernet import Fernet

class AntiCrackConfig:
    """防破解配置管理器 - 改进版本"""

    def __init__(self, config_file: str = "anti_crack_config.json"):
        self.config_file = config_file
        self.encrypted_config_file = config_file.replace('.json', '.enc')
        self.config_hash_file = config_file.replace('.json', '.hash')
        self._encryption_key = self._get_or_create_key()
        self.default_config = {
            # 基础检测配置
            "check_ce": True,                    # 检测Cheat Engine
            "check_ollydbg": True,              # 检测OllyDbg
            "check_x64dbg": True,               # 检测x64dbg/x32dbg
            "check_ida": True,                  # 检测IDA Pro
            "check_windbg": True,               # 检测WinDbg
            "check_process_hacker": True,       # 检测Process Hacker
            "check_process_monitor": True,      # 检测Process Monitor
            "check_api_monitor": True,          # 检测API Monitor
            "check_wireshark": True,            # 检测Wireshark
            "check_fiddler": True,              # 检测Fiddler
            "check_huorong_sword": True,        # 检测火绒剑
            
            # 环境检测配置
            "check_vm": True,                   # 检测虚拟机
            "check_sandbox": True,              # 检测沙箱
            "check_debugger": True,             # 检测调试器附加
            "check_system_drive": False,        # 检测系统盘运行 (已禁用，允许任意盘符)
            "check_integrity": True,            # 检测文件完整性
            
            # 行为配置
            "auto_blacklist": False,            # 禁用自动加入黑名单（减少误判）
            "exit_on_detect": False,            # 禁用检测到后立即退出（减少误判）
            "background_monitor": True,         # 后台监控
            "monitor_interval": 60,             # 监控间隔(秒) - 增加到60秒
            "sensitivity_level": "low",         # 敏感度级别：low/medium/high
            
            # VMP保护配置
            "vmp_protection": {
                "level": "强化型",              # 保护级别
                "anti_debug": True,             # 反调试
                "anti_vm": True,                # 反虚拟机
                "anti_dump": True,              # 反转储
                "code_virtualization": True,    # 代码虚拟化
                "import_protection": True,      # 导入表保护
                "resource_protection": True,    # 资源保护
                "string_encryption": True,      # 字符串加密
                "control_flow_obfuscation": True, # 控制流混淆
                "api_redirection": True,        # API重定向
                "memory_protection": True,      # 内存保护
                "integrity_check": True,        # 完整性检查
                "license_check": True,          # 许可证检查
                "system_drive_only": True,      # 仅系统盘运行
                "blacklist_check": True         # 黑名单检查
            },
            
            # 检测进程列表
            "dangerous_processes": [
                # 调试器
                "ollydbg.exe", "x64dbg.exe", "x32dbg.exe", "windbg.exe", 
                "ida.exe", "ida64.exe", "idaq.exe", "idaq64.exe", 
                "idaw.exe", "idaw64.exe", "devenv.exe", "msdev.exe",
                
                # CE和内存编辑器
                "cheatengine-x86_64.exe", "cheatengine-i386.exe", 
                "cheatengine.exe", "ce.exe", "artmoney.exe", 
                "gamecih.exe", "tsearch.exe", "memoryeditor.exe",
                
                # 进程监控工具
                "procmon.exe", "procexp.exe", "processhacker.exe", 
                "procexp64.exe", "processmonitor.exe", "systemexplorer.exe", 
                "anvir.exe",
                
                # 网络监控工具
                "wireshark.exe", "fiddler.exe", "charles.exe", 
                "burpsuite.exe", "httpanalyzer.exe", "networkminer.exe", 
                "tcpview.exe",
                
                # API监控工具
                "apimonitor.exe", "detours.exe", "apihook.exe", 
                "winapi.exe", "spy++.exe", "spyxx.exe", "rohitab.exe",
                
                # 反汇编工具
                "reflector.exe", "ildasm.exe", "dotpeek.exe", 
                "jetbrains.exe", "dnspy.exe", "ilspy.exe", "reflexil.exe",
                
                # 火绒剑
                "huorong.exe", "hrsword.exe", "hrjian.exe", 
                "huorongjian.exe", "hrsword64.exe", "hrjian64.exe"
            ],
            
            # 危险窗口标题
            "dangerous_window_titles": [
                "cheat engine", "ollydbg", "x64dbg", "x32dbg", "windbg", 
                "ida pro", "process hacker", "process monitor", "fiddler", 
                "wireshark", "api monitor", "spy++", "reflector", "dnspy", 
                "ilspy", "火绒剑", "huorong sword", "process explorer"
            ],
            
            # 虚拟机检测配置
            "vm_detection": {
                "processes": [
                    "vmware.exe", "vmtoolsd.exe", "vboxservice.exe", 
                    "vboxtray.exe", "qemu-ga.exe", "xenservice.exe", 
                    "vmsrvc.exe", "vmusrvc.exe"
                ],
                "registry_keys": [
                    r"SYSTEM\CurrentControlSet\Services\VBoxGuest",
                    r"SYSTEM\CurrentControlSet\Services\VBoxMouse",
                    r"SYSTEM\CurrentControlSet\Services\VBoxSF",
                    r"SYSTEM\CurrentControlSet\Services\vmci",
                    r"SYSTEM\CurrentControlSet\Services\vmhgfs"
                ],
                "files": [
                    r"C:\windows\system32\drivers\vboxguest.sys",
                    r"C:\windows\system32\drivers\vmhgfs.sys",
                    r"C:\windows\system32\drivers\vmmouse.sys"
                ]
            }
        }
        
        self.config = self.load_config()

    def _get_or_create_key(self) -> bytes:
        """获取或创建加密密钥"""
        key_file = "anti_crack.key"
        try:
            if os.path.exists(key_file):
                with open(key_file, 'rb') as f:
                    return f.read()
            else:
                key = Fernet.generate_key()
                with open(key_file, 'wb') as f:
                    f.write(key)
                # 隐藏密钥文件
                if os.name == 'nt':  # Windows
                    import subprocess
                    subprocess.run(['attrib', '+H', key_file], check=False)
                return key
        except Exception:
            # 如果无法创建文件，使用固定密钥（不安全但可用）
            return base64.urlsafe_b64encode(b'anti_crack_key_2024_secure_12345678')[:32]

    def _encrypt_data(self, data: str) -> bytes:
        """加密数据"""
        try:
            fernet = Fernet(self._encryption_key)
            return fernet.encrypt(data.encode())
        except Exception:
            return data.encode()

    def _decrypt_data(self, encrypted_data: bytes) -> str:
        """解密数据"""
        try:
            fernet = Fernet(self._encryption_key)
            return fernet.decrypt(encrypted_data).decode()
        except Exception:
            return encrypted_data.decode()

    def _verify_config_integrity(self) -> bool:
        """验证配置文件完整性"""
        try:
            if not os.path.exists(self.config_hash_file):
                return False

            with open(self.config_hash_file, 'r') as f:
                stored_hash = f.read().strip()

            current_hash = self.get_config_hash()
            return stored_hash == current_hash
        except Exception:
            return False

    def _save_config_hash(self):
        """保存配置哈希"""
        try:
            config_hash = self.get_config_hash()
            with open(self.config_hash_file, 'w') as f:
                f.write(config_hash)
        except Exception:
            pass

    def load_config(self) -> Dict[str, Any]:
        """加载配置 - 改进版本"""
        try:
            # 优先尝试加载加密配置
            if os.path.exists(self.encrypted_config_file):
                try:
                    with open(self.encrypted_config_file, 'rb') as f:
                        encrypted_data = f.read()

                    decrypted_data = self._decrypt_data(encrypted_data)
                    loaded_config = json.loads(decrypted_data)

                    # 验证配置完整性（宽松模式）
                    integrity_ok = self._verify_config_integrity()
                    if not integrity_ok:
                        print("⚠️ 配置文件完整性验证失败，但继续使用加载的配置")

                    config = self.default_config.copy()
                    config.update(loaded_config)
                    return config

                except Exception as e:
                    print(f"⚠️ 加载加密配置失败: {str(e)}")

            # 尝试加载普通配置文件
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)
                    # 合并默认配置和加载的配置
                    config = self.default_config.copy()
                    config.update(loaded_config)
                    # 迁移到加密配置
                    self.save_config(config)
                    return config
            else:
                # 如果配置文件不存在，创建默认配置
                self.save_config(self.default_config)
                return self.default_config.copy()
        except Exception as e:
            print(f"加载配置失败: {str(e)}")
            return self.default_config.copy()

    def save_config(self, config: Optional[Dict[str, Any]] = None) -> bool:
        """保存配置 - 改进版本"""
        try:
            if config is None:
                config = self.config

            # 保存为加密配置
            config_json = json.dumps(config, ensure_ascii=False, indent=2)
            encrypted_data = self._encrypt_data(config_json)

            with open(self.encrypted_config_file, 'wb') as f:
                f.write(encrypted_data)

            # 更新配置哈希
            self.config = config
            self._save_config_hash()

            # 删除旧的明文配置文件
            if os.path.exists(self.config_file):
                try:
                    os.remove(self.config_file)
                except Exception:
                    pass

            return True
        except Exception as e:
            print(f"保存配置失败: {str(e)}")
            return False
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置项"""
        return self.config.get(key, default)
    
    def set(self, key: str, value: Any) -> bool:
        """设置配置项"""
        try:
            self.config[key] = value
            return self.save_config()
        except Exception as e:
            print(f"设置配置项失败: {str(e)}")
            return False
    
    def get_dangerous_processes(self) -> List[str]:
        """获取危险进程列表"""
        return self.config.get("dangerous_processes", [])
    
    def get_dangerous_window_titles(self) -> List[str]:
        """获取危险窗口标题列表"""
        return self.config.get("dangerous_window_titles", [])
    
    def get_vm_detection_config(self) -> Dict[str, List[str]]:
        """获取虚拟机检测配置"""
        return self.config.get("vm_detection", {})
    
    def get_vmp_config(self) -> Dict[str, Any]:
        """获取VMP保护配置"""
        return self.config.get("vmp_protection", {})
    
    def is_enabled(self, feature: str) -> bool:
        """检查功能是否启用"""
        return self.config.get(feature, False)
    
    def enable_feature(self, feature: str) -> bool:
        """启用功能"""
        return self.set(feature, True)
    
    def disable_feature(self, feature: str) -> bool:
        """禁用功能"""
        return self.set(feature, False)
    
    def reset_to_default(self) -> bool:
        """重置为默认配置"""
        self.config = self.default_config.copy()
        return self.save_config()
    
    def export_config(self, export_file: str) -> bool:
        """导出配置"""
        try:
            with open(export_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"导出配置失败: {str(e)}")
            return False
    
    def import_config(self, import_file: str) -> bool:
        """导入配置"""
        try:
            with open(import_file, 'r', encoding='utf-8') as f:
                imported_config = json.load(f)
                self.config.update(imported_config)
                return self.save_config()
        except Exception as e:
            print(f"导入配置失败: {str(e)}")
            return False
    
    def get_config_hash(self) -> str:
        """获取配置哈希值"""
        config_str = json.dumps(self.config, sort_keys=True)
        return hashlib.md5(config_str.encode()).hexdigest()
    
    def print_config(self):
        """打印当前配置"""
        print("当前防破解配置:")
        print(json.dumps(self.config, ensure_ascii=False, indent=2))


# 全局配置实例
anti_crack_config = AntiCrackConfig()

# 导出常用函数
def get_config() -> AntiCrackConfig:
    """获取配置实例"""
    return anti_crack_config

def is_feature_enabled(feature: str) -> bool:
    """检查功能是否启用"""
    return anti_crack_config.is_enabled(feature)

def get_dangerous_processes() -> List[str]:
    """获取危险进程列表"""
    return anti_crack_config.get_dangerous_processes()

def get_dangerous_window_titles() -> List[str]:
    """获取危险窗口标题列表"""
    return anti_crack_config.get_dangerous_window_titles()

if __name__ == "__main__":
    # 测试配置管理器
    config = AntiCrackConfig()
    config.print_config()
    
    print(f"\n配置哈希: {config.get_config_hash()}")
    print(f"CE检测启用: {config.is_enabled('check_ce')}")
    print(f"危险进程数量: {len(config.get_dangerous_processes())}")
