#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试闪退检测功能 - 模拟各种异常和闪退情况
"""

import os
import sys
import time
import logging
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def setup_test_logging():
    """设置测试日志"""
    # 创建logs目录
    os.makedirs("logs", exist_ok=True)
    
    # 设置日志配置
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler("logs/toutiao_test.log", encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

def simulate_normal_operations():
    """模拟正常操作"""
    print("📋 模拟正常操作...")
    
    operations = [
        "用户登录成功",
        "加载账号列表",
        "开始批量存稿任务",
        "处理第1个项目",
        "处理第2个项目",
        "上传内容到服务器",
        "任务执行中..."
    ]
    
    for i, operation in enumerate(operations):
        logging.info(f"操作 {i+1}: {operation}")
        time.sleep(0.5)
    
    print("✅ 正常操作模拟完成")

def simulate_warnings():
    """模拟警告情况"""
    print("⚠️ 模拟警告情况...")
    
    warnings = [
        "网络连接不稳定",
        "账号登录超时，正在重试",
        "上传速度较慢",
        "检测到验证码，需要人工处理",
        "浏览器响应缓慢"
    ]
    
    for warning in warnings:
        logging.warning(f"警告: {warning}")
        time.sleep(0.3)
    
    print("✅ 警告情况模拟完成")

def simulate_errors():
    """模拟错误情况"""
    print("❌ 模拟错误情况...")
    
    errors = [
        "网络连接失败",
        "文件读取错误: 文件不存在",
        "数据库连接超时",
        "JSON解析异常: 格式错误",
        "HTTP请求失败: 500 Internal Server Error",
        "登录失败: 用户名或密码错误",
        "上传失败: 文件大小超限"
    ]
    
    for error in errors:
        logging.error(f"错误: {error}")
        time.sleep(0.4)
    
    print("✅ 错误情况模拟完成")

def simulate_exceptions():
    """模拟异常情况"""
    print("🚨 模拟异常情况...")
    
    exceptions = [
        "ValueError: 无效的参数值",
        "KeyError: 'account_id' 键不存在",
        "ConnectionError: 连接被拒绝",
        "TimeoutError: 操作超时",
        "FileNotFoundError: 找不到配置文件",
        "PermissionError: 权限不足",
        "MemoryError: 内存不足"
    ]
    
    for exception in exceptions:
        logging.error(f"Exception: {exception}")
        logging.error(f"Traceback (most recent call last):")
        logging.error(f"  File 'main.py', line 123, in process_item")
        logging.error(f"  File 'browser.py', line 456, in upload_content")
        logging.error(f"{exception}")
        time.sleep(0.5)
    
    print("✅ 异常情况模拟完成")

def simulate_critical_errors():
    """模拟严重错误"""
    print("💥 模拟严重错误...")
    
    critical_errors = [
        "FATAL ERROR: 系统内存不足，程序即将退出",
        "CRITICAL: 数据库连接完全失败",
        "CRASH: 浏览器进程意外终止",
        "SEGMENTATION FAULT: 内存访问违规",
        "ACCESS VIOLATION: 非法内存访问",
        "UNHANDLED EXCEPTION: 未处理的异常导致程序崩溃"
    ]
    
    for error in critical_errors:
        logging.critical(f"严重错误: {error}")
        time.sleep(0.6)
    
    print("✅ 严重错误模拟完成")

def simulate_crash():
    """模拟程序闪退"""
    print("💀 模拟程序闪退...")
    
    crash_scenarios = [
        "程序遇到致命错误，即将崩溃",
        "内存访问冲突，程序无法继续运行",
        "浏览器驱动异常，导致整个程序闪退",
        "系统资源耗尽，程序被强制终止",
        "未捕获的异常导致程序崩溃"
    ]
    
    for scenario in crash_scenarios:
        logging.critical(f"闪退: {scenario}")
        time.sleep(0.3)
    
    # 模拟程序异常退出
    logging.critical("程序异常退出，退出代码: 1")
    logging.critical("APPLICATION CRASH: 头条存稿工具发生闪退")
    
    print("✅ 闪退情况模拟完成")

def simulate_process_exit():
    """模拟进程退出"""
    print("🔚 模拟进程退出...")
    
    exit_scenarios = [
        ("正常退出", 0),
        ("一般错误退出", 1),
        ("用户中断", 130),
        ("进程被强制终止", 137),
        ("段错误", 139)
    ]
    
    for scenario, code in exit_scenarios:
        logging.info(f"进程退出: {scenario}, exit code: {code}")
        time.sleep(0.2)
    
    print("✅ 进程退出模拟完成")

def create_test_log_files():
    """创建测试日志文件"""
    print("📁 创建测试日志文件...")
    
    # 创建多个日志文件
    log_files = [
        "logs/app.log",
        "logs/error.log", 
        "logs/debug.log",
        "logs/batch.log",
        "logs/browser.log"
    ]
    
    for log_file in log_files:
        with open(log_file, "w", encoding="utf-8") as f:
            f.write(f"{datetime.now().isoformat()} - INFO - 日志文件已创建: {log_file}\n")
    
    print(f"✅ 创建了 {len(log_files)} 个测试日志文件")

def run_comprehensive_test():
    """运行综合测试"""
    print("🧪 开始综合闪退检测测试")
    print("=" * 60)
    
    # 设置日志
    setup_test_logging()
    
    # 创建测试日志文件
    create_test_log_files()
    
    print("\n📋 测试说明:")
    print("1. 请先启动监控系统: python monitor_launcher.py")
    print("2. 点击'开始监控'按钮")
    print("3. 观察监控界面的各个标签页")
    print("4. 本测试将模拟各种异常情况")
    print("")
    
    input("按回车键开始测试...")
    
    try:
        # 步骤1: 正常操作
        simulate_normal_operations()
        time.sleep(2)
        
        # 步骤2: 警告情况
        simulate_warnings()
        time.sleep(2)
        
        # 步骤3: 错误情况
        simulate_errors()
        time.sleep(2)
        
        # 步骤4: 异常情况
        simulate_exceptions()
        time.sleep(2)
        
        # 步骤5: 严重错误
        simulate_critical_errors()
        time.sleep(2)
        
        # 步骤6: 闪退情况
        simulate_crash()
        time.sleep(2)
        
        # 步骤7: 进程退出
        simulate_process_exit()
        
        print("\n🎉 综合测试完成！")
        print("")
        print("📊 测试结果验证:")
        print("请在监控界面中检查以下内容:")
        print("")
        print("📋 实时日志标签页:")
        print("   □ 是否显示了所有模拟的日志消息")
        print("   □ 不同级别的日志是否有不同颜色")
        print("   □ 日志过滤功能是否正常工作")
        print("")
        print("⚠️ 异常监控标签页:")
        print("   □ 是否检测到了LOG_CRASH类型的记录")
        print("   □ 是否检测到了LOG_EXCEPTION类型的记录")
        print("   □ 异常统计数量是否正确")
        print("")
        print("🎯 头条存稿工具状态面板:")
        print("   □ 是否显示了闪退警告信息")
        print("   □ 状态面板背景是否变为红色警告色")
        print("   □ 是否显示了详细的错误信息")
        print("")
        print("📈 状态栏:")
        print("   □ 是否显示了最新的异常和闪退状态")
        print("   □ 状态消息是否实时更新")
        print("")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

def show_monitoring_guide():
    """显示监控指南"""
    print("📖 实时闪退检测监控指南")
    print("=" * 50)
    print("")
    
    print("🎯 监控原理:")
    print("   • 实时监控日志文件的变化")
    print("   • 使用正则表达式匹配异常模式")
    print("   • 检测关键词: crash, 崩溃, 闪退, fatal, exception等")
    print("   • 每0.5秒扫描一次日志文件")
    print("")
    
    print("🔍 检测模式:")
    print("   1. 闪退模式: crash, 崩溃, 闪退, fatal, segmentation fault")
    print("   2. 异常模式: exception, error, traceback, failed, timeout")
    print("   3. 退出模式: exit, 退出, shutdown, terminated")
    print("")
    
    print("📁 监控的日志文件:")
    print("   • logs/app.log - 应用程序主日志")
    print("   • logs/error.log - 错误日志")
    print("   • logs/debug.log - 调试日志")
    print("   • logs/batch.log - 批量处理日志")
    print("   • logs/browser.log - 浏览器日志")
    print("   • 以及其他自动发现的日志文件")
    print("")
    
    print("⚡ 实时响应:")
    print("   • 检测到闪退: 立即在监控界面显示红色警告")
    print("   • 检测到异常: 添加到异常监控表格")
    print("   • 检测到退出: 更新状态栏信息")
    print("   • 所有检测都会记录到监控日志")
    print("")
    
    print("🎮 使用步骤:")
    print("   1. 启动监控系统: python monitor_launcher.py")
    print("   2. 点击'开始监控'按钮")
    print("   3. 运行头条存稿工具")
    print("   4. 监控系统会自动检测日志文件")
    print("   5. 任何异常都会立即显示在监控界面")
    print("")

if __name__ == "__main__":
    print("选择测试模式:")
    print("1. 运行综合闪退检测测试")
    print("2. 显示监控指南")
    
    choice = input("请输入选择 (1 或 2): ").strip()
    
    if choice == "1":
        run_comprehensive_test()
    elif choice == "2":
        show_monitoring_guide()
    else:
        print("无效选择，显示监控指南...")
        show_monitoring_guide()
    
    input("\n按回车键退出...")
