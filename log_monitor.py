#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
实时日志监控脚本 - 监控加载账号按钮点击时的日志
"""

import os
import time
import glob
import threading
from datetime import datetime

class LogMonitor:
    """日志监控器"""
    
    def __init__(self, log_dir="logs"):
        self.log_dir = log_dir
        self.monitoring = False
        self.last_positions = {}
        
    def get_latest_log_files(self):
        """获取最新的日志文件"""
        log_files = []
        
        # 应用日志
        app_logs = glob.glob(os.path.join(self.log_dir, "app_*.log"))
        if app_logs:
            latest_app_log = max(app_logs, key=os.path.getmtime)
            log_files.append(latest_app_log)
        
        # 其他重要日志
        other_logs = [
            os.path.join(self.log_dir, "app.log"),
            os.path.join(self.log_dir, "error.log"),
            os.path.join(self.log_dir, "debug.log"),
            os.path.join(self.log_dir, "toutiao_2025-08-01.log")
        ]
        
        for log_file in other_logs:
            if os.path.exists(log_file):
                log_files.append(log_file)
        
        return log_files
    
    def monitor_file(self, file_path):
        """监控单个文件"""
        if not os.path.exists(file_path):
            return
        
        # 获取文件当前位置
        if file_path not in self.last_positions:
            # 如果是新文件，从末尾开始监控
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                f.seek(0, 2)  # 移动到文件末尾
                self.last_positions[file_path] = f.tell()
        
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                f.seek(self.last_positions[file_path])
                new_lines = f.readlines()
                
                if new_lines:
                    print(f"\n📄 {os.path.basename(file_path)}:")
                    print("-" * 50)
                    for line in new_lines:
                        line = line.strip()
                        if line:
                            # 高亮重要关键词
                            if any(keyword in line.lower() for keyword in ['load', 'account', 'button', 'click', 'error', 'exception']):
                                print(f"🔍 {line}")
                            else:
                                print(f"   {line}")
                    print("-" * 50)
                
                self.last_positions[file_path] = f.tell()
                
        except Exception as e:
            print(f"❌ 监控文件 {file_path} 时出错: {str(e)}")
    
    def start_monitoring(self):
        """开始监控"""
        self.monitoring = True
        print("🔍 开始实时监控日志文件...")
        print("请在主程序中点击加载账号按钮，我会捕捉相关日志")
        print("按 Ctrl+C 停止监控")
        print("=" * 60)
        
        try:
            while self.monitoring:
                log_files = self.get_latest_log_files()
                
                for log_file in log_files:
                    self.monitor_file(log_file)
                
                time.sleep(0.5)  # 每0.5秒检查一次
                
        except KeyboardInterrupt:
            print("\n⏹️  监控已停止")
            self.monitoring = False
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False

def monitor_crash_logs():
    """监控崩溃日志"""
    crash_logs = glob.glob("logs/crash_log_*.txt")
    if crash_logs:
        latest_crash = max(crash_logs, key=os.path.getmtime)
        mtime = os.path.getmtime(latest_crash)
        
        # 如果崩溃日志是最近5分钟内的
        if time.time() - mtime < 300:
            print(f"\n🚨 发现最近的崩溃日志: {latest_crash}")
            try:
                with open(latest_crash, 'r', encoding='utf-8') as f:
                    content = f.read()
                    print("崩溃日志内容:")
                    print("-" * 50)
                    print(content)
                    print("-" * 50)
            except Exception as e:
                print(f"读取崩溃日志失败: {str(e)}")

def main():
    """主函数"""
    print("🧪 加载账号按钮日志监控器")
    print("=" * 60)
    
    # 检查日志目录
    if not os.path.exists("logs"):
        print("❌ 日志目录不存在")
        return
    
    # 检查最近的崩溃日志
    monitor_crash_logs()
    
    # 创建监控器
    monitor = LogMonitor()
    
    # 显示当前日志文件
    log_files = monitor.get_latest_log_files()
    print(f"📋 监控的日志文件:")
    for log_file in log_files:
        size = os.path.getsize(log_file) if os.path.exists(log_file) else 0
        mtime = datetime.fromtimestamp(os.path.getmtime(log_file)).strftime("%H:%M:%S") if os.path.exists(log_file) else "N/A"
        print(f"   📄 {log_file} (大小: {size} bytes, 修改时间: {mtime})")
    
    print()
    
    # 开始监控
    try:
        monitor.start_monitoring()
    except Exception as e:
        print(f"❌ 监控过程中出错: {str(e)}")

if __name__ == "__main__":
    main()
