#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
实时监控核心模块 - 监控批量存稿工具的运行状态
"""

import os
import sys
import time
import psutil
import logging
import threading
from datetime import datetime
from collections import deque
from PyQt5.QtCore import QObject, pyqtSignal, QTimer, QThread
from PyQt5.QtWidgets import QApplication

class SystemMonitor(QObject):
    """系统状态监控器"""
    
    # 定义信号
    cpu_usage_updated = pyqtSignal(float)  # CPU使用率更新
    memory_usage_updated = pyqtSignal(float, float)  # 内存使用率更新(使用量MB, 总量MB)
    thread_count_updated = pyqtSignal(int)  # 线程数量更新
    process_status_updated = pyqtSignal(dict)  # 进程状态更新
    
    def __init__(self):
        super().__init__()
        self.is_monitoring = False
        self.monitor_interval = 1.0  # 监控间隔1秒
        self.process = psutil.Process()
        self.cpu_history = deque(maxlen=60)  # 保存60秒的CPU历史数据
        self.memory_history = deque(maxlen=60)  # 保存60秒的内存历史数据
        
    def start_monitoring(self):
        """开始系统监控"""
        if self.is_monitoring:
            return
            
        self.is_monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        logging.info("系统监控已启动")
        
    def stop_monitoring(self):
        """停止系统监控"""
        self.is_monitoring = False
        logging.info("系统监控已停止")
        
    def _monitor_loop(self):
        """监控循环"""
        while self.is_monitoring:
            try:
                # 获取CPU使用率
                cpu_percent = self.process.cpu_percent()
                self.cpu_history.append(cpu_percent)
                self.cpu_usage_updated.emit(cpu_percent)
                
                # 获取内存使用情况
                memory_info = self.process.memory_info()
                memory_mb = memory_info.rss / 1024 / 1024
                virtual_memory = psutil.virtual_memory()
                total_memory_mb = virtual_memory.total / 1024 / 1024
                
                self.memory_history.append(memory_mb)
                self.memory_usage_updated.emit(memory_mb, total_memory_mb)
                
                # 获取线程数量
                thread_count = self.process.num_threads()
                self.thread_count_updated.emit(thread_count)
                
                # 获取进程状态
                process_status = {
                    'pid': self.process.pid,
                    'status': self.process.status(),
                    'create_time': datetime.fromtimestamp(self.process.create_time()),
                    'cpu_percent': cpu_percent,
                    'memory_percent': self.process.memory_percent(),
                    'num_threads': thread_count,
                    'num_fds': self.process.num_fds() if hasattr(self.process, 'num_fds') else 0
                }
                self.process_status_updated.emit(process_status)
                
                time.sleep(self.monitor_interval)
                
            except Exception as e:
                logging.error(f"系统监控出错: {str(e)}")
                time.sleep(self.monitor_interval)

class LogMonitor(QObject):
    """增强的日志监控器 - 实时检测闪退和异常"""

    # 定义信号
    log_message_received = pyqtSignal(str, str, str)  # 日志消息(时间, 级别, 内容)
    log_file_changed = pyqtSignal(str)  # 日志文件变化
    crash_detected_from_log = pyqtSignal(str, str)  # 从日志检测到闪退(时间, 详情)
    exception_detected_from_log = pyqtSignal(str, str, str)  # 从日志检测到异常(时间, 类型, 消息)
    process_exit_detected = pyqtSignal(str, int)  # 检测到进程退出(时间, 退出码)

    def __init__(self):
        super().__init__()
        self.is_monitoring = False
        self.log_files = []  # 监控的日志文件列表
        self.log_handlers = []  # 日志处理器列表
        self.log_buffer = deque(maxlen=1000)  # 日志缓冲区，最多保存1000条
        self.file_positions = {}  # 记录每个文件的读取位置
        self.crash_patterns = [
            r'(?i)crash|崩溃|闪退',
            r'(?i)fatal|致命错误',
            r'(?i)segmentation fault|段错误',
            r'(?i)access violation|访问冲突',
            r'(?i)unhandled exception|未处理异常',
            r'(?i)application error|应用程序错误',
            r'(?i)程序异常退出|程序崩溃',
            r'(?i)critical error|严重错误'
        ]
        self.exception_patterns = [
            r'(?i)exception|异常',
            r'(?i)error|错误',
            r'(?i)traceback|堆栈跟踪',
            r'(?i)failed|失败',
            r'(?i)timeout|超时',
            r'(?i)connection.*(?:lost|failed|error)|连接.*(?:丢失|失败|错误)'
        ]
        
    def add_log_file(self, file_path):
        """添加要监控的日志文件"""
        if os.path.exists(file_path) and file_path not in self.log_files:
            self.log_files.append(file_path)
            logging.info(f"添加日志文件监控: {file_path}")
            
    def start_monitoring(self):
        """开始日志监控"""
        if self.is_monitoring:
            return

        self.is_monitoring = True

        # 自动添加头条工具的日志文件
        self._add_toutiao_log_files()

        # 监控日志文件
        for log_file in self.log_files:
            thread = threading.Thread(target=self._monitor_log_file, args=(log_file,), daemon=True)
            thread.start()

        # 监控内存日志
        self._setup_memory_log_handler()

        # 启动实时检测线程
        self.detection_thread = threading.Thread(target=self._real_time_detection, daemon=True)
        self.detection_thread.start()

        logging.info("增强日志监控已启动（包含闪退检测）")
        
    def stop_monitoring(self):
        """停止日志监控"""
        self.is_monitoring = False
        
        # 移除内存日志处理器
        for handler in self.log_handlers:
            logging.getLogger().removeHandler(handler)
        self.log_handlers.clear()
        
        logging.info("日志监控已停止")

    def _add_toutiao_log_files(self):
        """自动添加头条工具的日志文件"""
        # 常见的日志文件路径
        log_paths = [
            "logs/app.log",
            "logs/error.log",
            "logs/debug.log",
            "logs/batch.log",
            "logs/account.log",
            "logs/browser.log",
            "app.log",
            "error.log",
            "debug.log",
            "toutiao.log",
            "main.log"
        ]

        for log_path in log_paths:
            if os.path.exists(log_path) and log_path not in self.log_files:
                self.log_files.append(log_path)
                self.file_positions[log_path] = 0
                logging.info(f"自动添加日志文件: {log_path}")

        # 如果没有找到日志文件，创建一个用于测试
        if not self.log_files:
            os.makedirs("logs", exist_ok=True)
            test_log = "logs/toutiao_monitor.log"
            with open(test_log, "a", encoding="utf-8") as f:
                f.write(f"{datetime.now().isoformat()} - INFO - 监控系统已启动\n")
            self.log_files.append(test_log)
            self.file_positions[test_log] = 0
            logging.info(f"创建测试日志文件: {test_log}")

    def _real_time_detection(self):
        """实时检测线程"""
        import re

        while self.is_monitoring:
            try:
                # 检查所有日志文件的新内容
                for log_file in self.log_files:
                    if os.path.exists(log_file):
                        self._check_log_file_for_issues(log_file)

                time.sleep(0.5)  # 每0.5秒检查一次

            except Exception as e:
                logging.error(f"实时检测出错: {str(e)}")
                time.sleep(1)

    def _check_log_file_for_issues(self, log_file):
        """检查日志文件中的问题"""
        import re

        try:
            with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
                # 移动到上次读取的位置
                current_pos = self.file_positions.get(log_file, 0)
                f.seek(current_pos)

                # 读取新内容
                new_lines = f.readlines()

                # 更新文件位置
                self.file_positions[log_file] = f.tell()

                # 分析新的日志行
                for line in new_lines:
                    line = line.strip()
                    if line:
                        self._analyze_log_line(line, log_file)

        except Exception as e:
            logging.error(f"检查日志文件 {log_file} 出错: {str(e)}")

    def _analyze_log_line(self, line, log_file):
        """分析日志行，检测异常和闪退"""
        import re

        try:
            # 提取时间戳
            timestamp = datetime.now().strftime('%H:%M:%S')
            if ' - ' in line:
                parts = line.split(' - ', 1)
                if len(parts) >= 1:
                    timestamp = parts[0].split(' ')[-1] if ' ' in parts[0] else parts[0]

            # 检测闪退模式
            for pattern in self.crash_patterns:
                if re.search(pattern, line):
                    crash_details = f"文件: {log_file}\n内容: {line}"
                    self.crash_detected_from_log.emit(timestamp, crash_details)
                    logging.warning(f"检测到闪退迹象: {line}")
                    return

            # 检测异常模式
            for pattern in self.exception_patterns:
                if re.search(pattern, line):
                    # 尝试提取异常类型
                    exception_type = "Unknown"
                    if "Exception" in line:
                        try:
                            exception_type = line.split("Exception")[0].split()[-1] + "Exception"
                        except:
                            exception_type = "Exception"
                    elif "Error" in line:
                        try:
                            exception_type = line.split("Error")[0].split()[-1] + "Error"
                        except:
                            exception_type = "Error"

                    self.exception_detected_from_log.emit(timestamp, exception_type, line)
                    logging.info(f"检测到异常: {line}")
                    return

            # 检测进程退出
            exit_patterns = [
                r'(?i)exit|退出',
                r'(?i)shutdown|关闭',
                r'(?i)terminated|终止',
                r'(?i)stopped|停止'
            ]

            for pattern in exit_patterns:
                if re.search(pattern, line):
                    # 尝试提取退出码
                    exit_code = 0
                    if "code" in line.lower():
                        try:
                            import re
                            codes = re.findall(r'code[:\s]*(\d+)', line, re.IGNORECASE)
                            if codes:
                                exit_code = int(codes[0])
                        except:
                            pass

                    self.process_exit_detected.emit(timestamp, exit_code)
                    logging.info(f"检测到进程退出: {line}")
                    return

        except Exception as e:
            logging.error(f"分析日志行出错: {str(e)}")
        
    def _monitor_log_file(self, file_path):
        """监控单个日志文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                # 移动到文件末尾
                f.seek(0, 2)
                
                while self.is_monitoring:
                    line = f.readline()
                    if line:
                        self._process_log_line(line.strip())
                    else:
                        time.sleep(0.1)
                        
        except Exception as e:
            logging.error(f"监控日志文件 {file_path} 出错: {str(e)}")
            
    def _setup_memory_log_handler(self):
        """设置内存日志处理器"""
        class MemoryLogHandler(logging.Handler):
            def __init__(self, log_monitor):
                super().__init__()
                self.log_monitor = log_monitor
                
            def emit(self, record):
                try:
                    timestamp = datetime.fromtimestamp(record.created).strftime('%H:%M:%S.%f')[:-3]
                    level = record.levelname
                    message = self.format(record)
                    self.log_monitor._emit_log_message(timestamp, level, message)
                except Exception:
                    pass
        
        handler = MemoryLogHandler(self)
        handler.setFormatter(logging.Formatter('%(message)s'))
        logging.getLogger().addHandler(handler)
        self.log_handlers.append(handler)
        
    def _process_log_line(self, line):
        """处理日志行"""
        try:
            # 简单的日志解析，可以根据实际格式调整
            parts = line.split(' - ', 2)
            if len(parts) >= 3:
                timestamp = parts[0]
                level = parts[1]
                message = parts[2]
            else:
                timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                level = 'INFO'
                message = line
                
            self._emit_log_message(timestamp, level, message)
            
        except Exception as e:
            logging.error(f"处理日志行出错: {str(e)}")
            
    def _emit_log_message(self, timestamp, level, message):
        """发送日志消息信号"""
        # 添加到缓冲区
        self.log_buffer.append((timestamp, level, message))
        
        # 发送信号
        self.log_message_received.emit(timestamp, level, message)

class CrashDetector(QObject):
    """闪退检测器"""
    
    # 定义信号
    crash_detected = pyqtSignal(dict)  # 检测到闪退
    exception_detected = pyqtSignal(str, str)  # 检测到异常(类型, 消息)
    memory_leak_detected = pyqtSignal(float)  # 检测到内存泄漏(内存增长MB)
    
    def __init__(self):
        super().__init__()
        self.is_monitoring = False
        self.process = psutil.Process()
        self.initial_memory = 0
        self.memory_threshold = 100  # 内存泄漏阈值100MB
        self.crash_history = []
        
    def start_monitoring(self):
        """开始闪退监控"""
        if self.is_monitoring:
            return
            
        self.is_monitoring = True
        self.initial_memory = self.process.memory_info().rss / 1024 / 1024
        
        # 设置异常钩子
        sys.excepthook = self._exception_hook
        
        # 启动内存泄漏检测
        self.memory_thread = threading.Thread(target=self._memory_leak_detection, daemon=True)
        self.memory_thread.start()
        
        logging.info("闪退检测已启动")
        
    def stop_monitoring(self):
        """停止闪退监控"""
        self.is_monitoring = False
        # 恢复默认异常钩子
        sys.excepthook = sys.__excepthook__
        logging.info("闪退检测已停止")
        
    def _exception_hook(self, exc_type, exc_value, exc_traceback):
        """异常钩子"""
        try:
            import traceback
            
            # 记录异常信息
            exception_info = {
                'type': exc_type.__name__,
                'message': str(exc_value),
                'traceback': ''.join(traceback.format_tb(exc_traceback)),
                'timestamp': datetime.now()
            }
            
            # 发送异常信号
            self.exception_detected.emit(exception_info['type'], exception_info['message'])
            
            # 检查是否为严重异常（可能导致闪退）
            critical_exceptions = ['SegmentationFault', 'SystemExit', 'KeyboardInterrupt']
            if exc_type.__name__ in critical_exceptions:
                self.crash_detected.emit(exception_info)
                
            logging.error(f"检测到异常: {exception_info['type']} - {exception_info['message']}")
            
        except Exception as e:
            logging.error(f"异常钩子处理出错: {str(e)}")
        
        # 调用默认异常处理
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        
    def _memory_leak_detection(self):
        """内存泄漏检测"""
        while self.is_monitoring:
            try:
                current_memory = self.process.memory_info().rss / 1024 / 1024
                memory_growth = current_memory - self.initial_memory
                
                if memory_growth > self.memory_threshold:
                    self.memory_leak_detected.emit(memory_growth)
                    logging.warning(f"检测到可能的内存泄漏，内存增长: {memory_growth:.2f}MB")
                    
                time.sleep(30)  # 每30秒检查一次
                
            except Exception as e:
                logging.error(f"内存泄漏检测出错: {str(e)}")
                time.sleep(30)

class ToutiaoProcessMonitor(QObject):
    """头条存稿工具进程监控器"""

    # 定义信号
    process_found = pyqtSignal(dict)  # 发现头条进程
    process_lost = pyqtSignal()  # 头条进程丢失
    process_status_updated = pyqtSignal(dict)  # 进程状态更新

    def __init__(self):
        super().__init__()
        self.is_monitoring = False
        self.toutiao_process = None
        self.process_name_patterns = [
            "main.py",
            "头条",
            "toutiao",
            "批量存稿",
            "python.exe"  # 可能的Python进程
        ]

    def start_monitoring(self):
        """开始监控头条进程"""
        if self.is_monitoring:
            return

        self.is_monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        logging.info("头条进程监控已启动")

    def stop_monitoring(self):
        """停止监控头条进程"""
        self.is_monitoring = False
        logging.info("头条进程监控已停止")

    def _monitor_loop(self):
        """监控循环"""
        while self.is_monitoring:
            try:
                # 查找头条相关进程
                toutiao_processes = self._find_toutiao_processes()

                if toutiao_processes:
                    # 选择最相关的进程
                    main_process = self._select_main_process(toutiao_processes)

                    if self.toutiao_process is None or self.toutiao_process.pid != main_process.pid:
                        self.toutiao_process = main_process
                        process_info = self._get_process_info(main_process)
                        self.process_found.emit(process_info)
                        logging.info(f"检测到头条存稿工具进程: PID={main_process.pid}")

                    # 更新进程状态
                    if self.toutiao_process:
                        process_info = self._get_process_info(self.toutiao_process)
                        self.process_status_updated.emit(process_info)

                else:
                    # 没有找到头条进程
                    if self.toutiao_process is not None:
                        self.toutiao_process = None
                        self.process_lost.emit()
                        logging.warning("头条存稿工具进程已丢失")

                time.sleep(2)  # 每2秒检查一次

            except Exception as e:
                logging.error(f"头条进程监控出错: {str(e)}")
                time.sleep(2)

    def _find_toutiao_processes(self):
        """查找头条相关进程"""
        toutiao_processes = []

        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'cwd']):
                try:
                    proc_info = proc.info

                    # 检查进程名称
                    if proc_info['name'] and any(pattern in proc_info['name'].lower() for pattern in ['python', 'main']):
                        # 检查命令行参数
                        if proc_info['cmdline']:
                            cmdline = ' '.join(proc_info['cmdline']).lower()
                            if any(pattern in cmdline for pattern in ['main.py', '头条', 'toutiao']):
                                toutiao_processes.append(proc)
                                continue

                        # 检查工作目录
                        if proc_info['cwd']:
                            cwd = proc_info['cwd'].lower()
                            if any(pattern in cwd for pattern in ['tou014', 'tou011', '头条', 'toutiao']):
                                toutiao_processes.append(proc)

                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

        except Exception as e:
            logging.error(f"查找头条进程时出错: {str(e)}")

        return toutiao_processes

    def _select_main_process(self, processes):
        """从多个进程中选择主进程"""
        if not processes:
            return None

        # 优先选择包含main.py的进程
        for proc in processes:
            try:
                cmdline = ' '.join(proc.cmdline()).lower()
                if 'main.py' in cmdline:
                    return proc
            except:
                continue

        # 如果没有找到main.py，返回第一个
        return processes[0]

    def _get_process_info(self, process):
        """获取进程详细信息"""
        try:
            return {
                'pid': process.pid,
                'name': process.name(),
                'status': process.status(),
                'cpu_percent': process.cpu_percent(),
                'memory_info': process.memory_info()._asdict(),
                'memory_percent': process.memory_percent(),
                'create_time': datetime.fromtimestamp(process.create_time()),
                'num_threads': process.num_threads(),
                'cmdline': process.cmdline(),
                'cwd': process.cwd() if hasattr(process, 'cwd') else None,
                'connections': len(process.connections()) if hasattr(process, 'connections') else 0
            }
        except Exception as e:
            logging.error(f"获取进程信息失败: {str(e)}")
            return {'pid': process.pid, 'error': str(e)}

class RealTimeMonitor(QObject):
    """实时监控主控制器"""

    def __init__(self):
        super().__init__()
        self.system_monitor = SystemMonitor()
        self.log_monitor = LogMonitor()
        self.crash_detector = CrashDetector()
        self.toutiao_monitor = ToutiaoProcessMonitor()

        # 导入深度监控组件
        from .deep_monitor import CrashDetector as DeepCrashDetector
        from .deep_monitor import UserInteractionMonitor, StateMonitor, set_deep_monitor

        # 深度监控组件
        self.deep_crash_detector = DeepCrashDetector()
        self.user_monitor = UserInteractionMonitor()
        self.state_monitor = StateMonitor()

        # 设置全局深度监控实例
        set_deep_monitor(self)

        self.is_running = False
        
    def start_all_monitoring(self):
        """启动所有监控"""
        if self.is_running:
            return

        self.is_running = True

        # 启动基础监控器
        self.system_monitor.start_monitoring()
        self.log_monitor.start_monitoring()
        self.crash_detector.start_monitoring()
        self.toutiao_monitor.start_monitoring()

        # 启动深度监控器
        self.deep_crash_detector.start_monitoring()
        self.user_monitor.start_monitoring()
        self.state_monitor.start_monitoring()

        logging.info("实时监控系统已启动（包含深度监控功能）")

    def stop_all_monitoring(self):
        """停止所有监控"""
        if not self.is_running:
            return

        self.is_running = False

        # 停止基础监控器
        self.system_monitor.stop_monitoring()
        self.log_monitor.stop_monitoring()
        self.crash_detector.stop_monitoring()
        self.toutiao_monitor.stop_monitoring()

        # 停止深度监控器
        self.deep_crash_detector.stop_monitoring()
        self.user_monitor.stop_monitoring()
        self.state_monitor.stop_monitoring()

        logging.info("实时监控系统已停止")
        
    def add_log_file(self, file_path):
        """添加日志文件监控"""
        self.log_monitor.add_log_file(file_path)
