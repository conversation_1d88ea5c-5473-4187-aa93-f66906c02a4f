[2025-08-02 01:17:08,234] [INFO] 缓存目录已创建: E:\toutiaoyuanma1\tou014\tou011\browser_cache
[2025-08-02 01:17:08,235] [INFO] 缓存目录已创建: E:\toutiaoyuanma1\tou014\tou011\browser_cache\user_data
[2025-08-02 01:17:08,236] [INFO] 缓存目录已创建: E:\toutiaoyuanma1\tou014\tou011\browser_cache\temp
[2025-08-02 01:17:08,236] [INFO] 缓存目录已创建: E:\toutiaoyuanma1\tou014\tou011\browser_cache\downloads
[2025-08-02 01:17:08,237] [INFO] 缓存目录已创建: E:\toutiaoyuanma1\tou014\tou011\browser_cache\profiles
[2025-08-02 01:17:08,238] [INFO] 缓存目录已创建: E:\toutiaoyuanma1\tou014\tou011\browser_cache\disk_cache
[2025-08-02 01:17:08,238] [INFO] 缓存目录已创建: E:\toutiaoyuanma1\tou014\tou011\browser_cache\media_cache
[2025-08-02 02:02:01,841] [INFO] 缓存目录已创建: E:\toutiaoyuanma1\tou014\tou011\browser_cache
[2025-08-02 02:02:01,843] [INFO] 缓存目录已创建: E:\toutiaoyuanma1\tou014\tou011\browser_cache\user_data
[2025-08-02 02:02:01,843] [INFO] 缓存目录已创建: E:\toutiaoyuanma1\tou014\tou011\browser_cache\temp
[2025-08-02 02:02:01,844] [INFO] 缓存目录已创建: E:\toutiaoyuanma1\tou014\tou011\browser_cache\downloads
[2025-08-02 02:02:01,845] [INFO] 缓存目录已创建: E:\toutiaoyuanma1\tou014\tou011\browser_cache\profiles
[2025-08-02 02:02:01,845] [INFO] 缓存目录已创建: E:\toutiaoyuanma1\tou014\tou011\browser_cache\disk_cache
[2025-08-02 02:02:01,846] [INFO] 缓存目录已创建: E:\toutiaoyuanma1\tou014\tou011\browser_cache\media_cache
[2025-08-02 02:02:03,711] [INFO] OpenCV库已加载，支持视频处理功能
[2025-08-02 02:02:03,800] [INFO] PIL库已加载，支持图像处理功能
[2025-08-02 02:02:11,010] [INFO] 自动化配置加载成功
[2025-08-02 02:02:11,010] [INFO] 自动化任务调度器初始化完成
[2025-08-02 02:02:11,023] [INFO] 心跳状态指示器初始化完成
[2025-08-02 02:02:11,102] [INFO] 跳过示例数据加载，等待真实账号数据
[2025-08-02 02:02:11,107] [ERROR] 连接表格信号时出错: 'AccountTab' object has no attribute 'on_cell_changed'
[2025-08-02 02:02:12,140] [INFO] 账号加载模式: 延迟加载
[2025-08-02 02:02:12,141] [INFO] 数据目录功能已移除
[2025-08-02 02:02:12,141] [INFO] 开始同步加载Cookie文件，路径: E:/软件共享/头条/头条
[2025-08-02 02:02:12,142] [INFO] 路径 E:/软件共享/头条/头条 下有 0 个JSON文件和 118 个TXT文件
[2025-08-02 02:02:12,142] [INFO] 调用account_loader.load_accounts_sync方法前
[2025-08-02 02:02:12,144] [INFO] 启动快速启动模式 - 后台加载账号数据
[2025-08-02 02:02:12,225] [DEBUG] 批量数据处理开始时内存使用: 269.08 MB，数据量: 118
[2025-08-02 02:02:12,226] [DEBUG] 更新账号 m96 的表格数据，是否有数据: True
[2025-08-02 02:02:12,226] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,226] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,228] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,228] [DEBUG] 开始查找账号 m96 对应的行索引
[2025-08-02 02:02:12,228] [DEBUG] 未找到账号 m96 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,228] [DEBUG] 更新账号 m1 的表格数据，是否有数据: True
[2025-08-02 02:02:12,228] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,229] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,229] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,229] [DEBUG] 开始查找账号 m1 对应的行索引
[2025-08-02 02:02:12,229] [DEBUG] 未找到账号 m1 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,230] [DEBUG] 更新账号 m10 的表格数据，是否有数据: True
[2025-08-02 02:02:12,230] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,230] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,230] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,230] [DEBUG] 开始查找账号 m10 对应的行索引
[2025-08-02 02:02:12,230] [DEBUG] 未找到账号 m10 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,231] [DEBUG] 更新账号 m100 的表格数据，是否有数据: True
[2025-08-02 02:02:12,231] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,231] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,231] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,231] [DEBUG] 开始查找账号 m100 对应的行索引
[2025-08-02 02:02:12,232] [DEBUG] 未找到账号 m100 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,232] [DEBUG] 更新账号 m101 的表格数据，是否有数据: True
[2025-08-02 02:02:12,232] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,232] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,232] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,233] [DEBUG] 开始查找账号 m101 对应的行索引
[2025-08-02 02:02:12,233] [DEBUG] 未找到账号 m101 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,233] [DEBUG] 更新账号 m102 的表格数据，是否有数据: True
[2025-08-02 02:02:12,233] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,233] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,234] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,234] [DEBUG] 开始查找账号 m102 对应的行索引
[2025-08-02 02:02:12,234] [DEBUG] 未找到账号 m102 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,234] [DEBUG] 更新账号 m103 的表格数据，是否有数据: True
[2025-08-02 02:02:12,234] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,235] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,235] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,235] [DEBUG] 开始查找账号 m103 对应的行索引
[2025-08-02 02:02:12,235] [DEBUG] 未找到账号 m103 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,236] [DEBUG] 更新账号 m104 的表格数据，是否有数据: True
[2025-08-02 02:02:12,236] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,236] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,236] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,237] [DEBUG] 开始查找账号 m104 对应的行索引
[2025-08-02 02:02:12,237] [DEBUG] 未找到账号 m104 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,237] [DEBUG] 更新账号 m1102 的表格数据，是否有数据: True
[2025-08-02 02:02:12,237] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,237] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,237] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,239] [DEBUG] 开始查找账号 m1102 对应的行索引
[2025-08-02 02:02:12,239] [DEBUG] 未找到账号 m1102 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,239] [DEBUG] 更新账号 m1105 的表格数据，是否有数据: True
[2025-08-02 02:02:12,239] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,239] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,240] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,240] [DEBUG] 开始查找账号 m1105 对应的行索引
[2025-08-02 02:02:12,240] [DEBUG] 未找到账号 m1105 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,240] [DEBUG] 更新账号 m1106 的表格数据，是否有数据: True
[2025-08-02 02:02:12,240] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,241] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,241] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,241] [DEBUG] 开始查找账号 m1106 对应的行索引
[2025-08-02 02:02:12,241] [DEBUG] 未找到账号 m1106 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,242] [DEBUG] 更新账号 m1107 的表格数据，是否有数据: True
[2025-08-02 02:02:12,242] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,242] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,242] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,242] [DEBUG] 开始查找账号 m1107 对应的行索引
[2025-08-02 02:02:12,242] [DEBUG] 未找到账号 m1107 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,243] [DEBUG] 更新账号 m1108 的表格数据，是否有数据: True
[2025-08-02 02:02:12,243] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,243] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,243] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,243] [DEBUG] 开始查找账号 m1108 对应的行索引
[2025-08-02 02:02:12,244] [DEBUG] 未找到账号 m1108 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,244] [DEBUG] 更新账号 m12 的表格数据，是否有数据: True
[2025-08-02 02:02:12,244] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,244] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,244] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,245] [DEBUG] 开始查找账号 m12 对应的行索引
[2025-08-02 02:02:12,245] [DEBUG] 未找到账号 m12 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,245] [DEBUG] 更新账号 m1201 的表格数据，是否有数据: True
[2025-08-02 02:02:12,245] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,245] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,246] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,246] [DEBUG] 开始查找账号 m1201 对应的行索引
[2025-08-02 02:02:12,246] [DEBUG] 未找到账号 m1201 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,246] [DEBUG] 更新账号 m1202 的表格数据，是否有数据: True
[2025-08-02 02:02:12,246] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,247] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,247] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,247] [DEBUG] 开始查找账号 m1202 对应的行索引
[2025-08-02 02:02:12,247] [DEBUG] 未找到账号 m1202 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,248] [DEBUG] 更新账号 m1203 的表格数据，是否有数据: True
[2025-08-02 02:02:12,248] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,248] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,248] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,248] [DEBUG] 开始查找账号 m1203 对应的行索引
[2025-08-02 02:02:12,250] [DEBUG] 未找到账号 m1203 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,250] [DEBUG] 更新账号 m1204 的表格数据，是否有数据: True
[2025-08-02 02:02:12,250] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,250] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,250] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,250] [DEBUG] 开始查找账号 m1204 对应的行索引
[2025-08-02 02:02:12,251] [DEBUG] 未找到账号 m1204 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,251] [DEBUG] 更新账号 m13 的表格数据，是否有数据: True
[2025-08-02 02:02:12,251] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,251] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,252] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,252] [DEBUG] 开始查找账号 m13 对应的行索引
[2025-08-02 02:02:12,252] [DEBUG] 未找到账号 m13 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,252] [DEBUG] 更新账号 m1401 的表格数据，是否有数据: True
[2025-08-02 02:02:12,252] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,253] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,253] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,253] [DEBUG] 开始查找账号 m1401 对应的行索引
[2025-08-02 02:02:12,253] [DEBUG] 未找到账号 m1401 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,254] [DEBUG] 更新账号 m1402 的表格数据，是否有数据: True
[2025-08-02 02:02:12,254] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,254] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,254] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,254] [DEBUG] 开始查找账号 m1402 对应的行索引
[2025-08-02 02:02:12,255] [DEBUG] 未找到账号 m1402 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,255] [DEBUG] 更新账号 m141 的表格数据，是否有数据: True
[2025-08-02 02:02:12,255] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,255] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,255] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,255] [DEBUG] 开始查找账号 m141 对应的行索引
[2025-08-02 02:02:12,256] [DEBUG] 未找到账号 m141 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,256] [DEBUG] 更新账号 m142 的表格数据，是否有数据: True
[2025-08-02 02:02:12,256] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,256] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,256] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,256] [DEBUG] 开始查找账号 m142 对应的行索引
[2025-08-02 02:02:12,257] [DEBUG] 未找到账号 m142 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,257] [DEBUG] 更新账号 m143 的表格数据，是否有数据: True
[2025-08-02 02:02:12,257] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,257] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,257] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,259] [DEBUG] 开始查找账号 m143 对应的行索引
[2025-08-02 02:02:12,259] [DEBUG] 未找到账号 m143 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,259] [DEBUG] 更新账号 m151 的表格数据，是否有数据: True
[2025-08-02 02:02:12,259] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,259] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,259] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,260] [DEBUG] 开始查找账号 m151 对应的行索引
[2025-08-02 02:02:12,260] [DEBUG] 未找到账号 m151 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,260] [DEBUG] 更新账号 m152 的表格数据，是否有数据: True
[2025-08-02 02:02:12,260] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,261] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,261] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,261] [DEBUG] 开始查找账号 m152 对应的行索引
[2025-08-02 02:02:12,261] [DEBUG] 未找到账号 m152 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,261] [DEBUG] 更新账号 m153 的表格数据，是否有数据: True
[2025-08-02 02:02:12,262] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,262] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,262] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,262] [DEBUG] 开始查找账号 m153 对应的行索引
[2025-08-02 02:02:12,262] [DEBUG] 未找到账号 m153 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,263] [DEBUG] 更新账号 m154 的表格数据，是否有数据: True
[2025-08-02 02:02:12,263] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,263] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,263] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,263] [DEBUG] 开始查找账号 m154 对应的行索引
[2025-08-02 02:02:12,264] [DEBUG] 未找到账号 m154 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,264] [DEBUG] 更新账号 m155 的表格数据，是否有数据: True
[2025-08-02 02:02:12,264] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,264] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,264] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,265] [DEBUG] 开始查找账号 m155 对应的行索引
[2025-08-02 02:02:12,265] [DEBUG] 未找到账号 m155 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,265] [DEBUG] 更新账号 m156 的表格数据，是否有数据: True
[2025-08-02 02:02:12,265] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,265] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,266] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,266] [DEBUG] 开始查找账号 m156 对应的行索引
[2025-08-02 02:02:12,266] [DEBUG] 未找到账号 m156 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,266] [DEBUG] 更新账号 m157 的表格数据，是否有数据: True
[2025-08-02 02:02:12,266] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,267] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,267] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,267] [DEBUG] 开始查找账号 m157 对应的行索引
[2025-08-02 02:02:12,268] [DEBUG] 未找到账号 m157 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,268] [DEBUG] 更新账号 m158 的表格数据，是否有数据: True
[2025-08-02 02:02:12,268] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,268] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,268] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,268] [DEBUG] 开始查找账号 m158 对应的行索引
[2025-08-02 02:02:12,270] [DEBUG] 未找到账号 m158 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,270] [DEBUG] 更新账号 m159 的表格数据，是否有数据: True
[2025-08-02 02:02:12,270] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,270] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,270] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,270] [DEBUG] 开始查找账号 m159 对应的行索引
[2025-08-02 02:02:12,271] [DEBUG] 未找到账号 m159 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,271] [DEBUG] 更新账号 m16 的表格数据，是否有数据: True
[2025-08-02 02:02:12,271] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,271] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,271] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,272] [DEBUG] 开始查找账号 m16 对应的行索引
[2025-08-02 02:02:12,272] [DEBUG] 未找到账号 m16 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,272] [DEBUG] 更新账号 m161 的表格数据，是否有数据: True
[2025-08-02 02:02:12,272] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,272] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,272] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,273] [DEBUG] 开始查找账号 m161 对应的行索引
[2025-08-02 02:02:12,273] [DEBUG] 未找到账号 m161 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,273] [DEBUG] 更新账号 m162 的表格数据，是否有数据: True
[2025-08-02 02:02:12,273] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,274] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,274] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,274] [DEBUG] 开始查找账号 m162 对应的行索引
[2025-08-02 02:02:12,274] [DEBUG] 未找到账号 m162 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,274] [DEBUG] 更新账号 m163 的表格数据，是否有数据: True
[2025-08-02 02:02:12,274] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,276] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,276] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,276] [DEBUG] 开始查找账号 m163 对应的行索引
[2025-08-02 02:02:12,276] [DEBUG] 未找到账号 m163 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,276] [DEBUG] 更新账号 m164 的表格数据，是否有数据: True
[2025-08-02 02:02:12,276] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,276] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,277] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,277] [DEBUG] 开始查找账号 m164 对应的行索引
[2025-08-02 02:02:12,277] [DEBUG] 未找到账号 m164 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,277] [DEBUG] 更新账号 m165 的表格数据，是否有数据: True
[2025-08-02 02:02:12,277] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,278] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,278] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,278] [DEBUG] 开始查找账号 m165 对应的行索引
[2025-08-02 02:02:12,278] [DEBUG] 未找到账号 m165 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,278] [DEBUG] 更新账号 m166 的表格数据，是否有数据: True
[2025-08-02 02:02:12,279] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,279] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,279] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,279] [DEBUG] 开始查找账号 m166 对应的行索引
[2025-08-02 02:02:12,279] [DEBUG] 未找到账号 m166 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,279] [DEBUG] 更新账号 m167 的表格数据，是否有数据: True
[2025-08-02 02:02:12,279] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,281] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,281] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,281] [DEBUG] 开始查找账号 m167 对应的行索引
[2025-08-02 02:02:12,281] [DEBUG] 未找到账号 m167 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,281] [DEBUG] 更新账号 m168 的表格数据，是否有数据: True
[2025-08-02 02:02:12,282] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,282] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,282] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,282] [DEBUG] 开始查找账号 m168 对应的行索引
[2025-08-02 02:02:12,283] [DEBUG] 未找到账号 m168 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,283] [DEBUG] 更新账号 m17 的表格数据，是否有数据: True
[2025-08-02 02:02:12,283] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,283] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,284] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,284] [DEBUG] 开始查找账号 m17 对应的行索引
[2025-08-02 02:02:12,285] [DEBUG] 未找到账号 m17 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,285] [DEBUG] 更新账号 m171 的表格数据，是否有数据: True
[2025-08-02 02:02:12,285] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,285] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,286] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,286] [DEBUG] 开始查找账号 m171 对应的行索引
[2025-08-02 02:02:12,286] [DEBUG] 未找到账号 m171 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,286] [DEBUG] 更新账号 m172 的表格数据，是否有数据: True
[2025-08-02 02:02:12,287] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,287] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,287] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,288] [DEBUG] 开始查找账号 m172 对应的行索引
[2025-08-02 02:02:12,288] [DEBUG] 未找到账号 m172 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,288] [DEBUG] 更新账号 m173 的表格数据，是否有数据: True
[2025-08-02 02:02:12,289] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,289] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,289] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,289] [DEBUG] 开始查找账号 m173 对应的行索引
[2025-08-02 02:02:12,289] [DEBUG] 未找到账号 m173 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,291] [DEBUG] 更新账号 m175 的表格数据，是否有数据: True
[2025-08-02 02:02:12,291] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,291] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,291] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,292] [DEBUG] 开始查找账号 m175 对应的行索引
[2025-08-02 02:02:12,292] [DEBUG] 未找到账号 m175 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,292] [DEBUG] 更新账号 m176 的表格数据，是否有数据: True
[2025-08-02 02:02:12,292] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,293] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,293] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,293] [DEBUG] 开始查找账号 m176 对应的行索引
[2025-08-02 02:02:12,294] [DEBUG] 未找到账号 m176 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,294] [DEBUG] 更新账号 m177 的表格数据，是否有数据: True
[2025-08-02 02:02:12,294] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,295] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,295] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,295] [DEBUG] 开始查找账号 m177 对应的行索引
[2025-08-02 02:02:12,295] [DEBUG] 未找到账号 m177 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,296] [DEBUG] 更新账号 m178 的表格数据，是否有数据: True
[2025-08-02 02:02:12,296] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,296] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,297] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,297] [DEBUG] 开始查找账号 m178 对应的行索引
[2025-08-02 02:02:12,297] [DEBUG] 未找到账号 m178 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,298] [DEBUG] 更新账号 m179 的表格数据，是否有数据: True
[2025-08-02 02:02:12,298] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,298] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,298] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,299] [DEBUG] 开始查找账号 m179 对应的行索引
[2025-08-02 02:02:12,299] [DEBUG] 未找到账号 m179 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,299] [DEBUG] 更新账号 m18 的表格数据，是否有数据: True
[2025-08-02 02:02:12,299] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,300] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,300] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,300] [DEBUG] 开始查找账号 m18 对应的行索引
[2025-08-02 02:02:12,301] [DEBUG] 未找到账号 m18 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,301] [DEBUG] 更新账号 m181 的表格数据，是否有数据: True
[2025-08-02 02:02:12,301] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,302] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,302] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,302] [DEBUG] 开始查找账号 m181 对应的行索引
[2025-08-02 02:02:12,302] [DEBUG] 未找到账号 m181 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,303] [DEBUG] 更新账号 m182 的表格数据，是否有数据: True
[2025-08-02 02:02:12,303] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,303] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,304] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,304] [DEBUG] 开始查找账号 m182 对应的行索引
[2025-08-02 02:02:12,304] [DEBUG] 未找到账号 m182 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,304] [DEBUG] 更新账号 m183 的表格数据，是否有数据: True
[2025-08-02 02:02:12,305] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,305] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,305] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,306] [DEBUG] 开始查找账号 m183 对应的行索引
[2025-08-02 02:02:12,306] [DEBUG] 未找到账号 m183 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,306] [DEBUG] 更新账号 m184 的表格数据，是否有数据: True
[2025-08-02 02:02:12,306] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,307] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,307] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,307] [DEBUG] 开始查找账号 m184 对应的行索引
[2025-08-02 02:02:12,308] [DEBUG] 未找到账号 m184 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,308] [DEBUG] 更新账号 m19 的表格数据，是否有数据: True
[2025-08-02 02:02:12,308] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,309] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,309] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,309] [DEBUG] 开始查找账号 m19 对应的行索引
[2025-08-02 02:02:12,309] [DEBUG] 未找到账号 m19 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,310] [DEBUG] 更新账号 m2 的表格数据，是否有数据: True
[2025-08-02 02:02:12,310] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,310] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,310] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,311] [DEBUG] 开始查找账号 m2 对应的行索引
[2025-08-02 02:02:12,311] [DEBUG] 未找到账号 m2 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,311] [DEBUG] 更新账号 m20 的表格数据，是否有数据: True
[2025-08-02 02:02:12,311] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,312] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,312] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,312] [DEBUG] 开始查找账号 m20 对应的行索引
[2025-08-02 02:02:12,313] [DEBUG] 未找到账号 m20 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,313] [DEBUG] 更新账号 m22 的表格数据，是否有数据: True
[2025-08-02 02:02:12,313] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,313] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,314] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,314] [DEBUG] 开始查找账号 m22 对应的行索引
[2025-08-02 02:02:12,314] [DEBUG] 未找到账号 m22 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,315] [DEBUG] 更新账号 m24 的表格数据，是否有数据: True
[2025-08-02 02:02:12,315] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,315] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,316] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,316] [DEBUG] 开始查找账号 m24 对应的行索引
[2025-08-02 02:02:12,316] [DEBUG] 未找到账号 m24 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,316] [DEBUG] 更新账号 m25 的表格数据，是否有数据: True
[2025-08-02 02:02:12,317] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,318] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,318] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,318] [DEBUG] 开始查找账号 m25 对应的行索引
[2025-08-02 02:02:12,318] [DEBUG] 未找到账号 m25 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,319] [DEBUG] 更新账号 m26 的表格数据，是否有数据: True
[2025-08-02 02:02:12,319] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,319] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,320] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,320] [DEBUG] 开始查找账号 m26 对应的行索引
[2025-08-02 02:02:12,320] [DEBUG] 未找到账号 m26 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,320] [DEBUG] 更新账号 m27 的表格数据，是否有数据: True
[2025-08-02 02:02:12,321] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,321] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,321] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,321] [DEBUG] 开始查找账号 m27 对应的行索引
[2025-08-02 02:02:12,322] [DEBUG] 未找到账号 m27 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,322] [DEBUG] 更新账号 m28 的表格数据，是否有数据: True
[2025-08-02 02:02:12,322] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,322] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,322] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,323] [DEBUG] 开始查找账号 m28 对应的行索引
[2025-08-02 02:02:12,323] [DEBUG] 未找到账号 m28 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,323] [DEBUG] 更新账号 m29 的表格数据，是否有数据: True
[2025-08-02 02:02:12,323] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,325] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,325] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,325] [DEBUG] 开始查找账号 m29 对应的行索引
[2025-08-02 02:02:12,325] [DEBUG] 未找到账号 m29 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,325] [DEBUG] 更新账号 m3 的表格数据，是否有数据: True
[2025-08-02 02:02:12,325] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,325] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,325] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,326] [DEBUG] 开始查找账号 m3 对应的行索引
[2025-08-02 02:02:12,326] [DEBUG] 未找到账号 m3 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,326] [DEBUG] 更新账号 m33 的表格数据，是否有数据: True
[2025-08-02 02:02:12,326] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,327] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,327] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,327] [DEBUG] 开始查找账号 m33 对应的行索引
[2025-08-02 02:02:12,327] [DEBUG] 未找到账号 m33 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,328] [DEBUG] 更新账号 m30 的表格数据，是否有数据: True
[2025-08-02 02:02:12,328] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,328] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,328] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,329] [DEBUG] 开始查找账号 m30 对应的行索引
[2025-08-02 02:02:12,329] [DEBUG] 未找到账号 m30 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,329] [DEBUG] 更新账号 m34 的表格数据，是否有数据: True
[2025-08-02 02:02:12,329] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,329] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,330] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,330] [DEBUG] 开始查找账号 m34 对应的行索引
[2025-08-02 02:02:12,330] [DEBUG] 未找到账号 m34 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,330] [DEBUG] 更新账号 m35 的表格数据，是否有数据: True
[2025-08-02 02:02:12,330] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,331] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,331] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,331] [DEBUG] 开始查找账号 m35 对应的行索引
[2025-08-02 02:02:12,331] [DEBUG] 未找到账号 m35 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,331] [DEBUG] 更新账号 m36 的表格数据，是否有数据: True
[2025-08-02 02:02:12,333] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,333] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,333] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,333] [DEBUG] 开始查找账号 m36 对应的行索引
[2025-08-02 02:02:12,333] [DEBUG] 未找到账号 m36 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,334] [DEBUG] 更新账号 m37 的表格数据，是否有数据: True
[2025-08-02 02:02:12,334] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,334] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,334] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,335] [DEBUG] 开始查找账号 m37 对应的行索引
[2025-08-02 02:02:12,335] [DEBUG] 未找到账号 m37 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,335] [DEBUG] 更新账号 m38 的表格数据，是否有数据: True
[2025-08-02 02:02:12,335] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,335] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,336] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,336] [DEBUG] 开始查找账号 m38 对应的行索引
[2025-08-02 02:02:12,336] [DEBUG] 未找到账号 m38 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,336] [DEBUG] 更新账号 m39 的表格数据，是否有数据: True
[2025-08-02 02:02:12,337] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,337] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,338] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,338] [DEBUG] 开始查找账号 m39 对应的行索引
[2025-08-02 02:02:12,338] [DEBUG] 未找到账号 m39 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,338] [DEBUG] 更新账号 m40 的表格数据，是否有数据: True
[2025-08-02 02:02:12,339] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,339] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,339] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,340] [DEBUG] 开始查找账号 m40 对应的行索引
[2025-08-02 02:02:12,340] [DEBUG] 未找到账号 m40 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,340] [DEBUG] 更新账号 m41 的表格数据，是否有数据: True
[2025-08-02 02:02:12,341] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,341] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,341] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,342] [DEBUG] 开始查找账号 m41 对应的行索引
[2025-08-02 02:02:12,342] [DEBUG] 未找到账号 m41 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,342] [DEBUG] 更新账号 m43 的表格数据，是否有数据: True
[2025-08-02 02:02:12,343] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,343] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,343] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,343] [DEBUG] 开始查找账号 m43 对应的行索引
[2025-08-02 02:02:12,344] [DEBUG] 未找到账号 m43 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,344] [DEBUG] 更新账号 m44 的表格数据，是否有数据: True
[2025-08-02 02:02:12,344] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,344] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,345] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,345] [DEBUG] 开始查找账号 m44 对应的行索引
[2025-08-02 02:02:12,345] [DEBUG] 未找到账号 m44 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,345] [DEBUG] 更新账号 m46 的表格数据，是否有数据: True
[2025-08-02 02:02:12,345] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,345] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,346] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,346] [DEBUG] 开始查找账号 m46 对应的行索引
[2025-08-02 02:02:12,346] [DEBUG] 未找到账号 m46 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,346] [DEBUG] 更新账号 m47 的表格数据，是否有数据: True
[2025-08-02 02:02:12,347] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,347] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,347] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,347] [DEBUG] 开始查找账号 m47 对应的行索引
[2025-08-02 02:02:12,347] [DEBUG] 未找到账号 m47 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,348] [DEBUG] 更新账号 m48 的表格数据，是否有数据: True
[2025-08-02 02:02:12,348] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,348] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,348] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,348] [DEBUG] 开始查找账号 m48 对应的行索引
[2025-08-02 02:02:12,349] [DEBUG] 未找到账号 m48 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,349] [DEBUG] 更新账号 m5 的表格数据，是否有数据: True
[2025-08-02 02:02:12,349] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,349] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,349] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,349] [DEBUG] 开始查找账号 m5 对应的行索引
[2025-08-02 02:02:12,351] [DEBUG] 未找到账号 m5 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,351] [DEBUG] 更新账号 m52 的表格数据，是否有数据: True
[2025-08-02 02:02:12,351] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,351] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,351] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,351] [DEBUG] 开始查找账号 m52 对应的行索引
[2025-08-02 02:02:12,351] [DEBUG] 未找到账号 m52 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,352] [DEBUG] 更新账号 m54 的表格数据，是否有数据: True
[2025-08-02 02:02:12,352] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,352] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,352] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,353] [DEBUG] 开始查找账号 m54 对应的行索引
[2025-08-02 02:02:12,353] [DEBUG] 未找到账号 m54 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,353] [DEBUG] 更新账号 m55 的表格数据，是否有数据: True
[2025-08-02 02:02:12,353] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,354] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,354] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,354] [DEBUG] 开始查找账号 m55 对应的行索引
[2025-08-02 02:02:12,354] [DEBUG] 未找到账号 m55 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,354] [DEBUG] 更新账号 m58 的表格数据，是否有数据: True
[2025-08-02 02:02:12,355] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,355] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,355] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,355] [DEBUG] 开始查找账号 m58 对应的行索引
[2025-08-02 02:02:12,355] [DEBUG] 未找到账号 m58 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,356] [DEBUG] 更新账号 m6 的表格数据，是否有数据: True
[2025-08-02 02:02:12,356] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,356] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,356] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,356] [DEBUG] 开始查找账号 m6 对应的行索引
[2025-08-02 02:02:12,357] [DEBUG] 未找到账号 m6 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,357] [DEBUG] 更新账号 m60 的表格数据，是否有数据: True
[2025-08-02 02:02:12,357] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,357] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,358] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,358] [DEBUG] 开始查找账号 m60 对应的行索引
[2025-08-02 02:02:12,358] [DEBUG] 未找到账号 m60 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,358] [DEBUG] 更新账号 m62 的表格数据，是否有数据: True
[2025-08-02 02:02:12,358] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,359] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,359] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,359] [DEBUG] 开始查找账号 m62 对应的行索引
[2025-08-02 02:02:12,360] [DEBUG] 未找到账号 m62 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,360] [DEBUG] 更新账号 m63 的表格数据，是否有数据: True
[2025-08-02 02:02:12,360] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,360] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,361] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,361] [DEBUG] 开始查找账号 m63 对应的行索引
[2025-08-02 02:02:12,361] [DEBUG] 未找到账号 m63 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,361] [DEBUG] 更新账号 m61 的表格数据，是否有数据: True
[2025-08-02 02:02:12,361] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,361] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,363] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,363] [DEBUG] 开始查找账号 m61 对应的行索引
[2025-08-02 02:02:12,363] [DEBUG] 未找到账号 m61 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,363] [DEBUG] 更新账号 m64 的表格数据，是否有数据: True
[2025-08-02 02:02:12,363] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,363] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,364] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,364] [DEBUG] 开始查找账号 m64 对应的行索引
[2025-08-02 02:02:12,364] [DEBUG] 未找到账号 m64 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,364] [DEBUG] 更新账号 m65 的表格数据，是否有数据: True
[2025-08-02 02:02:12,364] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,365] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,365] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,365] [DEBUG] 开始查找账号 m65 对应的行索引
[2025-08-02 02:02:12,365] [DEBUG] 未找到账号 m65 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,365] [DEBUG] 更新账号 m66 的表格数据，是否有数据: True
[2025-08-02 02:02:12,365] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,366] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,366] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,366] [DEBUG] 开始查找账号 m66 对应的行索引
[2025-08-02 02:02:12,366] [DEBUG] 未找到账号 m66 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,366] [DEBUG] 更新账号 m7 的表格数据，是否有数据: True
[2025-08-02 02:02:12,366] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,367] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,367] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,367] [DEBUG] 开始查找账号 m7 对应的行索引
[2025-08-02 02:02:12,367] [DEBUG] 未找到账号 m7 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,367] [DEBUG] 更新账号 m70 的表格数据，是否有数据: True
[2025-08-02 02:02:12,368] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,368] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,368] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,368] [DEBUG] 开始查找账号 m70 对应的行索引
[2025-08-02 02:02:12,368] [DEBUG] 未找到账号 m70 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,370] [DEBUG] 更新账号 m71 的表格数据，是否有数据: True
[2025-08-02 02:02:12,370] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,370] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,370] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,370] [DEBUG] 开始查找账号 m71 对应的行索引
[2025-08-02 02:02:12,370] [DEBUG] 未找到账号 m71 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,371] [DEBUG] 更新账号 m72 的表格数据，是否有数据: True
[2025-08-02 02:02:12,371] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,371] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,371] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,371] [DEBUG] 开始查找账号 m72 对应的行索引
[2025-08-02 02:02:12,371] [DEBUG] 未找到账号 m72 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,372] [DEBUG] 更新账号 m73 的表格数据，是否有数据: True
[2025-08-02 02:02:12,372] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,372] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,372] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,372] [DEBUG] 开始查找账号 m73 对应的行索引
[2025-08-02 02:02:12,372] [DEBUG] 未找到账号 m73 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,373] [DEBUG] 更新账号 m74 的表格数据，是否有数据: True
[2025-08-02 02:02:12,373] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,373] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,373] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,373] [DEBUG] 开始查找账号 m74 对应的行索引
[2025-08-02 02:02:12,374] [DEBUG] 未找到账号 m74 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,374] [DEBUG] 更新账号 m75 的表格数据，是否有数据: True
[2025-08-02 02:02:12,374] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,374] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,374] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,376] [DEBUG] 开始查找账号 m75 对应的行索引
[2025-08-02 02:02:12,376] [DEBUG] 未找到账号 m75 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,376] [DEBUG] 更新账号 m76 的表格数据，是否有数据: True
[2025-08-02 02:02:12,376] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,376] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,376] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,377] [DEBUG] 开始查找账号 m76 对应的行索引
[2025-08-02 02:02:12,377] [DEBUG] 未找到账号 m76 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,377] [DEBUG] 更新账号 m8 的表格数据，是否有数据: True
[2025-08-02 02:02:12,377] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,377] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,378] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,378] [DEBUG] 开始查找账号 m8 对应的行索引
[2025-08-02 02:02:12,378] [DEBUG] 未找到账号 m8 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,378] [DEBUG] 更新账号 m86 的表格数据，是否有数据: True
[2025-08-02 02:02:12,379] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,379] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,379] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,379] [DEBUG] 开始查找账号 m86 对应的行索引
[2025-08-02 02:02:12,380] [DEBUG] 未找到账号 m86 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,380] [DEBUG] 更新账号 m88 的表格数据，是否有数据: True
[2025-08-02 02:02:12,380] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,380] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,380] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,380] [DEBUG] 开始查找账号 m88 对应的行索引
[2025-08-02 02:02:12,380] [DEBUG] 未找到账号 m88 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,381] [DEBUG] 更新账号 m9 的表格数据，是否有数据: True
[2025-08-02 02:02:12,381] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,381] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,381] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,382] [DEBUG] 开始查找账号 m9 对应的行索引
[2025-08-02 02:02:12,382] [DEBUG] 未找到账号 m9 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,382] [DEBUG] 更新账号 m91 的表格数据，是否有数据: True
[2025-08-02 02:02:12,382] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,382] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,383] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,383] [DEBUG] 开始查找账号 m91 对应的行索引
[2025-08-02 02:02:12,383] [DEBUG] 未找到账号 m91 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,383] [DEBUG] 更新账号 m92 的表格数据，是否有数据: True
[2025-08-02 02:02:12,383] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,383] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,385] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,385] [DEBUG] 开始查找账号 m92 对应的行索引
[2025-08-02 02:02:12,385] [DEBUG] 未找到账号 m92 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,385] [DEBUG] 更新账号 m93 的表格数据，是否有数据: True
[2025-08-02 02:02:12,385] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,386] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,386] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,386] [DEBUG] 开始查找账号 m93 对应的行索引
[2025-08-02 02:02:12,386] [DEBUG] 未找到账号 m93 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,386] [DEBUG] 更新账号 m94 的表格数据，是否有数据: True
[2025-08-02 02:02:12,386] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,387] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,387] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,387] [DEBUG] 开始查找账号 m94 对应的行索引
[2025-08-02 02:02:12,387] [DEBUG] 未找到账号 m94 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,387] [DEBUG] 更新账号 m97 的表格数据，是否有数据: True
[2025-08-02 02:02:12,387] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,388] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,388] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,388] [DEBUG] 开始查找账号 m97 对应的行索引
[2025-08-02 02:02:12,388] [DEBUG] 未找到账号 m97 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,388] [DEBUG] 更新账号 m95 的表格数据，是否有数据: True
[2025-08-02 02:02:12,388] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,390] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,390] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,390] [DEBUG] 开始查找账号 m95 对应的行索引
[2025-08-02 02:02:12,390] [DEBUG] 未找到账号 m95 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,391] [DEBUG] 更新账号 m1302 的表格数据，是否有数据: True
[2025-08-02 02:02:12,391] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,391] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,391] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,391] [DEBUG] 开始查找账号 m1302 对应的行索引
[2025-08-02 02:02:12,392] [DEBUG] 未找到账号 m1302 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,392] [DEBUG] 更新账号 m1301 的表格数据，是否有数据: True
[2025-08-02 02:02:12,392] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,392] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,392] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,393] [DEBUG] 开始查找账号 m1301 对应的行索引
[2025-08-02 02:02:12,393] [DEBUG] 未找到账号 m1301 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,393] [DEBUG] 更新账号 m1303 的表格数据，是否有数据: True
[2025-08-02 02:02:12,393] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,393] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,393] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,394] [DEBUG] 开始查找账号 m1303 对应的行索引
[2025-08-02 02:02:12,394] [DEBUG] 未找到账号 m1303 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,394] [DEBUG] 更新账号 m1501 的表格数据，是否有数据: True
[2025-08-02 02:02:12,394] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,394] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,394] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,395] [DEBUG] 开始查找账号 m1501 对应的行索引
[2025-08-02 02:02:12,395] [DEBUG] 未找到账号 m1501 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,395] [DEBUG] 更新账号 m1502 的表格数据，是否有数据: True
[2025-08-02 02:02:12,395] [DEBUG] 账号数据字段: ['account_id', 'username', 'status', 'credit_score', 'draft_count', 'yesterday_fans', 'total_fans', 'total_play_count', 'yesterday_play', 'total_income', 'yesterday_income', 'seven_days_total', 'withdrawable_amount', 'register_days', 'verification_status', 'total_withdraw', 'withdraw_date', 'recent_withdraw']
[2025-08-02 02:02:12,395] [DEBUG] 表格结构: 0行 x 19列
[2025-08-02 02:02:12,396] [DEBUG] 表格列标题: ['昵称', '账号', '状态', '信用分', '草稿数', '昨日粉丝', '总粉丝', '累计播放', '昨日播放', '累计收益', '昨日收益', '七天收益', '待提现', '账号注册天数', '实名状态', '总提现金额', '最近提现日期', '最近提现金额', '操作']
[2025-08-02 02:02:12,396] [DEBUG] 开始查找账号 m1502 对应的行索引
[2025-08-02 02:02:12,396] [DEBUG] 未找到账号 m1502 对应的行索引，尝试记录所有行的账号ID
[2025-08-02 02:02:12,437] [INFO] 用户取消后台加载
[2025-08-02 02:02:12,437] [INFO] 后台加载完成: 账号数据加载完成
[2025-08-02 02:02:12,641] [DEBUG] 批量数据处理完成后内存使用: 269.22 MB
[2025-08-02 02:02:12,643] [ERROR] 更新加载进度时出错: 'NoneType' object has no attribute 'setLabelText'
[2025-08-02 02:02:12,897] [INFO] account_loader.load_accounts_sync方法返回: success=True, accounts数量=118, error_msg=
[2025-08-02 02:02:12,897] [INFO] 同步加载账号数据完成，结果: True, 账号数量: 118, 错误信息: 
[2025-08-02 02:02:12,898] [INFO] 账号数据加载成功，共 118 个账号
[2025-08-02 02:02:12,898] [INFO] 账号加载完成回调开始时内存使用: 275.81 MB
[2025-08-02 02:02:12,898] [INFO] 已创建账号列表的浅拷贝，大小: 118
[2025-08-02 02:02:12,899] [INFO] 成功加载 118 个账号文件，延迟加载模式: True
[2025-08-02 02:02:12,930] [INFO] 账号列表大小: 118
[2025-08-02 02:02:12,930] [INFO] 第一个账号信息: {'file_path': 'E:/软件共享/头条/头条\\m1.txt', 'account_id': 'm1', 'data': {'已发 0 篇----passport_csrf_token': '6207557d075a193223003ffd2ca532c9', 'passport_csrf_token_default': '6207557d075a193223003ffd2ca532c9', 'ttwid': '1%7Cu94SiIFT1C_4CiZKNEJTM-tn26uPvfopcc0qJt_pVjQ%7C1750131151%7C7737498a78edfb7e5ae6716d81eaae7e28d80c90a5b2ba0866160aa952123071', 'passport_mfa_token': 'Cje%2F%2BI4T6rjT0ogMfB%2BlxAoonRLYVC89JBcF9wx4X3w%2Bp3xXciN93NoBtFOxJmcGy9YwP45LvHvVGkoKPAAAAAAAAAAAAABPIArbC8qVRNkV4aOCVtZg1ElcnEywMW%2BxhHC0K8nQ%2B%2FR1kcwWCYg9QTkBDObqF5G0%2BRCjqfQNGPax0WwgAiIBA%2BZbqm8%3D', 'd_ticket': '17057f08c25ceadc0db174f1b38924eb80c28', 'n_mh': 'tyVst0yKL6Yh4p1Z8a5EaJh3dZ11Ek9k-Pcaw8oaohM', 'sso_auth_status': '4007a6fe2759f76bfe22cd0cc12b8067', 'sso_auth_status_ss': '4007a6fe2759f76bfe22cd0cc12b8067', 'sso_uid_tt': '1e03d1fd46632886039c10eda35732e4', 'sso_uid_tt_ss': '1e03d1fd46632886039c10eda35732e4', 'toutiao_sso_user': '62b92c4d4a368df2c966d3473faa826c', 'toutiao_sso_user_ss': '62b92c4d4a368df2c966d3473faa826c', 'sid_ucp_sso_v1': '1.0.0-KGE4MjU1ZDk0OTU0ZjIyMDgxYTY3MGRiNzY2MDdjYWZiYWI0Mzk0MzYKHwiMxaD1z6yUAhDyysPCBhjPCSAMMMzVv8IGOAJA8QcaAmhsIiA2MmI5MmM0ZDRhMzY4ZGYyYzk2NmQzNDczZmFhODI2Yw', 'ssid_ucp_sso_v1': '1.0.0-KGE4MjU1ZDk0OTU0ZjIyMDgxYTY3MGRiNzY2MDdjYWZiYWI0Mzk0MzYKHwiMxaD1z6yUAhDyysPCBhjPCSAMMMzVv8IGOAJA8QcaAmhsIiA2MmI5MmM0ZDRhMzY4ZGYyYzk2NmQzNDczZmFhODI2Yw', 'odin_tt': '0828ed94aa42c3741deac699bb98110b0c8929641a29cc49bd0044f94627daa9f552af6dc0c2761c2c181e41c00358341a09a0a2fa5d3c86457e8b7abe077f9f', 'passport_auth_status': '39e5bc22d61cc43c2432ea11c784d2b3%2C9532be3a4a78bfb50c02df1c8ceda723', 'passport_auth_status_ss': '39e5bc22d61cc43c2432ea11c784d2b3%2C9532be3a4a78bfb50c02df1c8ceda723', 'sid_guard': '712336638b189e1affc97b74c988e22f%7C1750132082%7C5184002%7CSat%2C+16-Aug-2025+03%3A48%3A04+GMT', 'uid_tt': 'e61660f14b4e704a87a762729e78dba4', 'uid_tt_ss': 'e61660f14b4e704a87a762729e78dba4', 'sid_tt': '712336638b189e1affc97b74c988e22f', 'sessionid': '712336638b189e1affc97b74c988e22f', 'sessionid_ss': '712336638b189e1affc97b74c988e22f', 'is_staff_user': 'false', 'sid_ucp_v1': '1.0.0-KGRiZTNhZjUzMjU4Y2VjYzNkNzE1MWEwNmIxMzU5MGJlNDQ5M2NmNzkKGQiMxaD1z6yUAhDyysPCBhjPCSAMOAJA8QcaAmhsIiA3MTIzMzY2MzhiMTg5ZTFhZmZjOTdiNzRjOTg4ZTIyZg', 'ssid_ucp_v1': '1.0.0-KGRiZTNhZjUzMjU4Y2VjYzNkNzE1MWEwNmIxMzU5MGJlNDQ5M2NmNzkKGQiMxaD1z6yUAhDyysPCBhjPCSAMOAJA8QcaAmhsIiA3MTIzMzY2MzhiMTg5ZTFhZmZjOTdiNzRjOTg4ZTIyZg----'}, 'file_type': '.txt'}
[2025-08-02 02:02:12,931] [INFO] 使用延迟加载模式，准备更新表格基本信息
[2025-08-02 02:02:12,933] [DEBUG] 已导入高级内存管理器
[2025-08-02 02:02:12,947] [INFO] 更新表格开始时内存使用: 276.60MB, 可用: 1435.60MB/16236.06MB
[2025-08-02 02:02:12,979] [INFO] 准备设置表格行数: 118
[2025-08-02 02:02:12,980] [INFO] 已设置表格行数: 118，当前表格行数: 118
[2025-08-02 02:02:12,980] [DEBUG] 处理账号批次: 1-50/118
[2025-08-02 02:02:13,515] [DEBUG] 处理账号批次: 51-100/118
[2025-08-02 02:02:13,779] [DEBUG] 处理账号批次: 101-118/118
[2025-08-02 02:02:13,961] [INFO] 表格更新完成，耗时: 1.02秒，内存使用: 314.43MB
[2025-08-02 02:02:13,962] [INFO] 使用延迟加载模式，已更新表格基本信息
[2025-08-02 02:02:13,962] [INFO] 延迟加载模式下，表格行数: 118
[2025-08-02 02:02:13,994] [INFO] 已重新启用加载账号按钮
[2025-08-02 02:02:13,994] [INFO] 不设置账号已加载标志，允许重复加载
[2025-08-02 02:02:13,995] [INFO] 开始计算收益统计，表格行数: 118, 列数: 19
[2025-08-02 02:02:13,995] [INFO] 收益统计完成 - 统计了 0 个账号
[2025-08-02 02:02:13,996] [INFO] 七天总收益: ¥0.00
[2025-08-02 02:02:13,996] [INFO] 昨日总收益: ¥0.00
[2025-08-02 02:02:13,996] [INFO] 更新7天总收益统计: ¥0.00
[2025-08-02 02:02:13,998] [INFO] 更新昨日总收益统计: ¥0.00
[2025-08-02 02:02:14,001] [INFO] 已重新启用加载账号按钮
[2025-08-02 02:02:20,049] [INFO] 开始计算收益统计，表格行数: 118, 列数: 19
[2025-08-02 02:02:20,050] [INFO] 收益统计完成 - 统计了 0 个账号
[2025-08-02 02:02:20,051] [INFO] 七天总收益: ¥0.00
[2025-08-02 02:02:20,051] [INFO] 昨日总收益: ¥0.00
[2025-08-02 02:02:20,051] [INFO] 更新7天总收益统计: ¥0.00
[2025-08-02 02:02:20,053] [INFO] 更新昨日总收益统计: ¥0.00
