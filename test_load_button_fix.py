#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试加载账号按钮修复效果的脚本
用于验证按钮点击的稳定性和防闪退功能
"""

import sys
import os
import time
import logging
from PyQt5.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget, QLabel, QTextEdit
from PyQt5.QtCore import QTimer, Qt

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test_load_button.log', encoding='utf-8')
    ]
)

class LoadButtonTester(QMainWindow):
    """加载按钮测试器"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("加载账号按钮稳定性测试")
        self.setGeometry(100, 100, 600, 400)
        
        # 测试计数器
        self.click_count = 0
        self.success_count = 0
        self.error_count = 0
        
        self.init_ui()
        
    def init_ui(self):
        """初始化界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title = QLabel("加载账号按钮稳定性测试")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px;")
        layout.addWidget(title)
        
        # 状态显示
        self.status_label = QLabel("准备开始测试...")
        self.status_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.status_label)
        
        # 测试按钮
        self.test_button = QPushButton("开始快速点击测试")
        self.test_button.clicked.connect(self.start_rapid_click_test)
        self.test_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                font-size: 14px;
                font-weight: bold;
                padding: 10px;
                border-radius: 5px;
                border: none;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        layout.addWidget(self.test_button)
        
        # 手动测试按钮
        self.manual_test_button = QPushButton("手动测试加载按钮")
        self.manual_test_button.clicked.connect(self.manual_test)
        self.manual_test_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                font-size: 14px;
                font-weight: bold;
                padding: 10px;
                border-radius: 5px;
                border: none;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
        """)
        layout.addWidget(self.manual_test_button)
        
        # 重置按钮
        self.reset_button = QPushButton("重置测试状态")
        self.reset_button.clicked.connect(self.reset_test)
        self.reset_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                font-size: 14px;
                font-weight: bold;
                padding: 10px;
                border-radius: 5px;
                border: none;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        layout.addWidget(self.reset_button)
        
        # 日志显示
        self.log_display = QTextEdit()
        self.log_display.setMaximumHeight(200)
        self.log_display.setStyleSheet("""
            QTextEdit {
                background-color: #2c3e50;
                color: #ecf0f1;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 10px;
                border: 1px solid #34495e;
                border-radius: 3px;
            }
        """)
        layout.addWidget(self.log_display)
        
    def log_message(self, message):
        """记录日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        log_text = f"[{timestamp}] {message}"
        self.log_display.append(log_text)
        logging.info(message)
        
    def start_rapid_click_test(self):
        """开始快速点击测试"""
        self.log_message("开始快速点击测试...")
        self.test_button.setEnabled(False)
        
        # 重置计数器
        self.click_count = 0
        self.success_count = 0
        self.error_count = 0
        
        # 创建定时器进行快速点击
        self.click_timer = QTimer()
        self.click_timer.timeout.connect(self.simulate_click)
        self.click_timer.start(100)  # 每100毫秒点击一次
        
        # 10秒后停止测试
        QTimer.singleShot(10000, self.stop_rapid_test)
        
    def simulate_click(self):
        """模拟点击加载按钮"""
        try:
            self.click_count += 1
            self.log_message(f"模拟点击 #{self.click_count}")
            
            # 这里应该调用实际的加载按钮点击方法
            # 由于这是测试脚本，我们只是模拟
            self.simulate_load_account_click()
            
            self.success_count += 1
            self.update_status()
            
        except Exception as e:
            self.error_count += 1
            self.log_message(f"点击出错: {str(e)}")
            self.update_status()
            
    def simulate_load_account_click(self):
        """模拟加载账号按钮点击"""
        # 模拟一些可能导致问题的操作
        time.sleep(0.01)  # 模拟处理时间
        
        # 随机抛出异常来测试错误处理
        import random
        if random.random() < 0.1:  # 10%的概率出错
            raise Exception("模拟的随机错误")
            
    def stop_rapid_test(self):
        """停止快速点击测试"""
        if hasattr(self, 'click_timer'):
            self.click_timer.stop()
            
        self.test_button.setEnabled(True)
        self.log_message(f"快速点击测试完成！总点击: {self.click_count}, 成功: {self.success_count}, 错误: {self.error_count}")
        
    def manual_test(self):
        """手动测试"""
        try:
            self.log_message("执行手动测试...")
            
            # 尝试导入并测试实际的账号标签页
            try:
                from app.tabs.account_tab import AccountTab
                
                # 创建账号标签页实例进行测试
                account_tab = AccountTab()
                
                # 检查加载状态
                if hasattr(account_tab, '_check_loading_state'):
                    state = account_tab._check_loading_state()
                    self.log_message(f"当前加载状态: {state}")
                
                # 测试按钮点击
                if hasattr(account_tab, 'on_load_account_clicked'):
                    account_tab.on_load_account_clicked()
                    self.log_message("手动测试完成 - 按钮点击正常")
                else:
                    self.log_message("手动测试失败 - 找不到按钮点击方法")
                    
            except ImportError as e:
                self.log_message(f"无法导入账号标签页: {str(e)}")
            except Exception as e:
                self.log_message(f"手动测试出错: {str(e)}")
                
        except Exception as e:
            self.log_message(f"手动测试异常: {str(e)}")
            
    def reset_test(self):
        """重置测试状态"""
        self.click_count = 0
        self.success_count = 0
        self.error_count = 0
        self.log_display.clear()
        self.update_status()
        self.log_message("测试状态已重置")
        
    def update_status(self):
        """更新状态显示"""
        status_text = f"点击: {self.click_count} | 成功: {self.success_count} | 错误: {self.error_count}"
        self.status_label.setText(status_text)

def main():
    """主函数"""
    app = QApplication(sys.argv)
    app.setApplicationName("加载按钮测试器")
    
    tester = LoadButtonTester()
    tester.show()
    
    return app.exec_()

if __name__ == "__main__":
    sys.exit(main())
